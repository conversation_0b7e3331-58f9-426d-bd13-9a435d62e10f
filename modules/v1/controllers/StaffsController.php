<?php

namespace app\modules\v1\controllers;

use app\controllers\RestFulController;
use app\libs\ValidationException;
use app\models\backyard\HcmExcelTask;
use app\models\backyard\OsStaffInfoExtend;
use app\models\backyard\SettingEnv;
use app\models\backyard\StaffCriminalRecord;
use app\models\backyard\VehicleInfo;
use app\models\fle\StaffAccount;
use app\models\fle\StaffInfo as StaffInfo_fle;
use app\models\fle\SysDepartment;
use app\models\fle\SysProvince;
use app\models\fle\SysStore;
use app\models\manage\BaseStaffInfo;
use app\models\manage\ExportManage;
use app\models\manage\HrJobTitle;
use app\models\manage\HrOperateLogs;
use app\models\manage\HrStaffInfoPosition;
use app\models\manage\HrStaffTransferLog;
use app\models\manage\StaffInfo;
use app\models\manage\StaffItems;
use app\models\manage\SysManagePiece;
use app\models\manage\SysManageRegion;
use app\models\manage\SysStoreTemp;
use app\modules\v1\business\JobTitleDepartmentRelation;
use app\modules\v1\business\Staff;
use app\modules\v1\business\StaffHold;
use app\modules\v1\business\StaffManager;
use app\modules\v1\business\StaffSalary;
use app\modules\v1\business\SysArea;
use app\modules\v1\business\SysGroupDeptV2;
use app\modules\v1\config\AreaManager;
use app\modules\v1\config\SysConfig;
use app\services\base\HrStaffInsuranceBeneficiaryService;
use app\services\base\InstructorService;
use app\services\base\OutSourcingService;
use app\services\base\RoleService;
use app\services\base\SettingEnvService;
use app\services\base\StaffRelationsService;
use app\services\base\StaffService;
use app\services\base\SysConfigService;
use app\services\base\SysDepartmentService;
use app\services\base\SysStoreService;
use app\services\base\VehicleInfoService;
use app\services\base\CompanyService;
use Yii;
use yii\db\Query;

/**
 * Default controller for the `v1` module
 */
class StaffsController extends RestFulController
{
    public $operateBeferContent;
    public $modelClass = 'app\models\manage\StaffInfo';
    public $newStaffInfoId;
    public $config_role = ['SUPER_ADMIN','SYSTEM_ADMIN', 'PERSONNEL_COMMISSIONER', 'PERSONNEL_MANAGER', 'HRIS_MANAGER', 'PAYROLL_MANAGER', 'HR_OFFICER', 'HRBP', 'SHARED_SERVICES'];
    public $view_basic_config_role = ['HRD','ER','HR_SERVICE','TALENT_ACQUISITION', 'SUPER_ADMIN','SYSTEM_ADMIN', 'PERSONNEL_COMMISSIONER', 'PERSONNEL_MANAGER', 'HRIS_MANAGER', 'PAYROLL_MANAGER', 'HR_OFFICER', 'HRBP'];
    public $config_view = ['formal','store','trainee','download'];
    // excel 可导出的个人信息字段
    public $personal_field = [
        58,59,38,45,57,53,71,65,66,67,68,62,63,64,6,9,70,60,61,10,7,87,
        39,     // 银行卡号
        76,     // 社保号 ID VN LA
        77,     // 公积金号
        78,     // 医保号 ID VN
        79,     // 种族
        80,     // 宗教
        81,     // 工作所在州
        88,     // 银行分行名称 ID
        89,     // 户籍照号     ID
        90,     // PTKP状态 ID
        91,     // 税卡号 ID VN
        92,     // 备用银行卡持卡人 PH
        93,     // 备用银行卡类型 PH
        94,     // 备用银行卡卡号 PH
        100,    // 户口所在地地址(分项)
        101,    // 居住地分享  TH PH VN LA ID MY
        102,    // 102->备用银行账号审核状态 PH,
        103,    // 103 -> 银行账号审核状态 TH
        105,    // 社保号审核状态 ph
        106,    // 公积金号审核状态
        107,    // 医疗保险号 审核状态
        108,    // 税卡号审核状态
        121,    // 户口簿审核状态
    ];

    public function actions()
    {
        return [
            'sync-item' => 'app\modules\v1\actions\ActionSyncItem',
        ];
    }

    /**
     * 员工列表
     * @return array
     */
    public function actionIndex()
    {
        $page = $this->pageIndexAndNums();
        $params = Yii::$app->request->get();
        $params['user_fbi_role'] = Yii::$app->user->identity->getPermission()['fbi.role'];
        $params['user_staff_id'] = $this->fbid;
        $params['user_departmentId'] = Yii::$app->user->identity->departmentId;
        $query = StaffService::getInstance()->getStaffListQueryObject($params);
        //只显示非子账号数据
        $isSubStaff = Yii::$app->request->get('master_staff');
        if (isset($isSubStaff) && 'SUB_STAFF' == $isSubStaff) {
            $data['page_count'] = 0;
            $data['rows'] = [];
            return $this->succ($data);
        }
        $countResult = $query->select('count(1) as total')->one(Yii::$app->get('r_backyard'));
        $data['page_count'] = $countResult['total'];
        if($data['page_count'] == 0){
            $data['page_count'] = 0;
            $data['rows'] = [];
            return $this->succ($data);
        }
        $order = ['hr_staff_info.id' => SORT_DESC];
        if (YII_COUNTRY == 'PH') {
            $order = ['hr_staff_info.hire_date' => SORT_DESC];
        }
        $data['rows'] = $query
            ->select(StaffService::getInstance()->getQueryField())
            ->orderBy($order)
            ->offset($page[0] * $page[1])
            ->limit($page[1])
            ->all(Yii::$app->get('r_backyard'));

        Yii::$app->logger->write_log('Staff：fbid ' . $this->fbid . " sql" . $query->createCommand()->getRawSql(), "info");

        $sysDepIds = $_staff_info_ids = $nodeDepIds = $job_title =  [];
        foreach ($data['rows'] as $items) {
            $_staff_info_ids[] = $items['staff_info_id'];
            $sysDepIds[] = $items['sys_department_id'];
            $nodeDepIds[] = $items['node_department_id'];
            $job_title[] = $items['job_title'];
        }

        $rolesPermission = staff::rolesPermission($_staff_info_ids, $this->fbid);
        $hrStaffInfoPosition =  HrStaffInfoPosition::find()
            ->select(['staff_info_id', 'group_concat(position_category) as position_category'])
            ->where(['IN', 'staff_info_id', $_staff_info_ids])
            ->indexBy('staff_info_id')
            ->groupBy('staff_info_id')
            ->asArray()
            ->all(Yii::$app->get('r_backyard'));
        //外协的字段
        $osExtendInfo = $outsourceCompanyListToId = [];
        if(isset($params['formal']) && $params['formal'] == 0){
            $pay_type = StaffItems::find()->select(['value', 'staff_info_id'])->where(['IN', 'staff_info_id', $_staff_info_ids])->andWhere(['item' => 'PAY_TYPE'])->indexBy('staff_info_id')->asArray()->column(Yii::$app->get('r_backyard'));
            $outsourcing_type = StaffItems::find()->select(['value', 'staff_info_id'])->where(['IN', 'staff_info_id', $_staff_info_ids])->andWhere(['item' => 'OUTSOURCING_TYPE'])->indexBy('staff_info_id')->asArray()->column(Yii::$app->get('r_backyard'));

            $osExtendInfo = OsStaffInfoExtend::find()->select(['staff_info_id', 'company_item_id', 'is_complete_address'])->where(['IN', 'staff_info_id', $_staff_info_ids])->asArray()->indexBy('staff_info_id')->all(Yii::$app->get('r_backyard'));

            $outsourceCompanyList = Yii::$app->jrpc->getOsCompanyList();
            $outsourceCompanyListToId = !empty($outsourceCompanyList) ? array_column($outsourceCompanyList, 'label', 'value') : [];
        }

        $vehicle_type_category_list = StaffService::getInstance()->getVehicleTypeCategoryList($_staff_info_ids);

        $department = SysGroupDeptV2::getAllDepartmentByIds($nodeDepIds,$sysDepIds);
        $jobs = StaffManager::getAllJobTitleByIds($job_title);

        $show_vehicle_type_category_job_title = SettingEnvService::getInstance()->getSetVal('show_vehicle_type_category_job_title');
        $show_vehicle_type_category_job_title_arr = [];
        if(!empty($show_vehicle_type_category_job_title)) {
            $show_vehicle_type_category_job_title_arr = explode(',', $show_vehicle_type_category_job_title);
        }

        $sorting_no_my = array_flip(AreaManager::$areasMy);
        $sorting_no_la = array_flip(AreaManager::$areasLa);
        $roleService   = RoleService::getInstance();
        $hidden_job_grade_staffs     = StaffService::getInstance()->getCEmployee();
        $contract_company_map        = CompanyService::getInstance()->getContractCompanyMap();
        $area_list                   = SysStoreService::getInstance()->getAreaByProvinceCode();
        $store_region_map            = SysStoreService::getInstance()->getStoreRegionMap();
        $store_piece_map             = SysStoreService::getInstance()->getStorePieceMap();

        foreach ($data['rows'] as $key => &$one) {

            $storeName = $one['sys_store_id'] == '-1' ? Yii::$app->lang->get('head_office') : ($one['store_name']??'');
            $one['region_name'] = $store_region_map[$one['store_manage_region']] ?? '';
            $one['piece_name']  = $store_piece_map[$one['store_manage_piece']] ?? '';
            $one['position_category'] = isset($hrStaffInfoPosition[$one['staff_info_id']]) ? explode(',',$hrStaffInfoPosition[$one['staff_info_id']]['position_category']):[];
            $one['position_category_text'] = implode(',',$roleService->getRoleNameByIds($one['position_category']));

            $one['sys_department_name'] = $department[$one['sys_department_id']]['name'] ?? '';
            if (!empty($one['sys_department_name']) && SysDepartment::DELETE_1 == $department[$one['sys_department_id']]['deleted']) {
                $one['sys_department_name'] .= Yii::$app->lang->get('deleted');
            }

            $one['node_department_name'] = $department[$one['node_department_id']]['name'] ?? '';
            if (!empty($one['node_department_name']) && SysDepartment::DELETE_1 == $department[$one['node_department_id']]['deleted']) {
                $one['node_department_name'] .= Yii::$app->lang->get('deleted');
            }

            $one['department_name'] = $one['node_department_id'] != 0 ? $one['node_department_name'] : $one['sys_department_name'];
            $one['sys_store_name'] = $storeName;
            
            $one['job_title_name'] = $jobs[$one['job_title']]['job_name'] ?? '';
            if (!empty($one['job_title_name']) && HrJobTitle::STATUS_2 == $jobs[$one['job_title']]['status']) {
                $one['job_title_name'] .= Yii::$app->lang->get('deleted');
            }
            $one['contract_company_name'] = $contract_company_map[$one['contract_company_id']] ?? '';

            switch (YII_COUNTRY) {
                case 'MY':
                    $one['store_area_id'] = $sorting_no_my[$one['store_sorting_no']] ?? 0;
                    $one['store_area_text'] = isset($one['store_sorting_no']) && isset($sorting_no_my[$one['store_sorting_no']]) ? $one['store_sorting_no'] : '';
                    break;
                case 'LA':
                    $one['store_area_id'] = $sorting_no_la[$one['store_sorting_no']] ?? 0 ;
                    $one['store_area_text'] = isset($one['store_sorting_no']) && isset($sorting_no_la[$one['store_sorting_no']]) ? $one['store_sorting_no'] : '';
                    break;
                default:
                    $area_name = $area_list[$one['store_province_code']]??'';
                    $one['store_area_id'] = null;
                    $one['store_area_text'] = $area_name;
            }
            $one['manager'] = $one['manger'];
            $one['pay_type'] = $pay_type[$one['staff_info_id']] ?? '';
            $one['outsourcing_type'] = $outsourcing_type[$one['staff_info_id']] ?? '';

            $one['job_title_level_name'] = empty($one['job_title_level']) ? null : Yii::$app->sysconfig->config_job_title_level[$one['job_title_level']] ?? '';
            $one['job_title_grade_v2_name'] = Yii::$app->sysconfig->config_job_title_grade_v2[$one['job_title_grade_v2']] ?? '';

            $one['hire_date'] = date('Y-m-d', strtotime($one['hire_date']));
            $one['leave_date'] = empty($one['leave_date']) ? '' : date('Y-m-d',strtotime($one['leave_date']));
            $one['stop_duties_date'] = empty($one['stop_duties_date']) ? '' : date('Y-m-d',strtotime($one['stop_duties_date']));
            if($one['staff_type'] != 0) {
                //$one['staff_type_name']  = $one['staff_type'] == 1 ? Yii::$app->lang->get('staff_type_'.$one['staff_type']) : Yii::$app->lang->get('staff_type_2');
                $one['staff_type_name']  = in_array($one['staff_type'],[2, 3]) ? Yii::$app->lang->get('staff_type_2') : Yii::$app->lang->get('staff_type_'.$one['staff_type']);
            } else {
                $one['staff_type_name']  = '';
            }

            if ($one['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_YES && in_array($one['formal'],
                    [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE])) {
                $one['state_name'] = Yii::$app->lang->get('wait_leave_state');
            } else {
                $one['state_name'] = Yii::$app->lang->get('state_' . $one['state']);
            }

            switch ($one['state']) {
                case 1:
                    if($one['wait_leave_state'] == 1) {
                        $one['stop_duties_date'] = '';
                    } else {
                        $one['stop_duties_date'] = '';
                        $one['leave_date'] = '';
                    }
                    break;
                case 3:
                    $one['leave_date'] = '';
                    break;
            }
            if (!empty(Yii::$app->request->get('from')) && Yii::$app->request->get('from') == 'job_title_grade_whitelist') {
                $one['edit_permission'] = Yii::$app->lang->get('job_grade_edit_permission_' . $one['job_grade_edit_permission']);
                $one['jurisdiction']    = $this->jurisdictionByStaffId($one['staff_info_id']);
            } else {
                $one['job_title_grade_v2_name'] = null;
                $one['job_title_grade_v2']      = null;
                $one['job_title_level']         = null;
                $one['job_title_level_name']    = null;
            }
            $one['hire_type_text'] = $one['hire_type'] ? Yii::$app->lang->get('hire_type_' . $one['hire_type']) : '';

            $one['is_can_view'] = in_array($one['staff_info_id'], $rolesPermission['view_staff_ids']) ? 1 : 0;
            $one['is_can_edit'] = in_array($one['staff_info_id'], $rolesPermission['edit_staff_ids']) ? 1 : 0;
            //外协雇佣类型
            $one['hire_os_type_text']   =    $one['hire_type'] ? Yii::$app->lang->get('hire_type_' . $one['hire_type']) : '';

            if(!empty($show_vehicle_type_category_job_title_arr) && in_array($one['job_title'], $show_vehicle_type_category_job_title_arr)) {
                $one['vehicle_type_category'] = $vehicle_type_category_list[$one['staff_info_id']] ?? '';//马来 车类型
            } else {
                $one['vehicle_type_category'] = '';
            }

            if(isset($params['formal']) && $params['formal'] == BaseStaffInfo::FORMAL_OUTSOURCE) {
                //外协是否完善地址信息
                $one['is_complete_address_text'] = isset($osExtendInfo[$one['staff_info_id']]) ? Yii::$app->lang->get('is_disability_' . $osExtendInfo[$one['staff_info_id']]['is_complete_address']) : '';
                //外协公司名称
                $one['os_company_name'] = isset($osExtendInfo[$one['staff_info_id']]) ? ($outsourceCompanyListToId[$osExtendInfo[$one['staff_info_id']]['company_item_id']] ?? '') : '';
            }
            if (in_array($one['staff_info_id'], $hidden_job_grade_staffs)) {
                $one['job_title_grade_v2_name'] = null;
                $one['job_title_grade_v2']      = null;
                $one['job_title_level']         = null;
                $one['job_title_level_name']    = null;
            }
        }

        return $this->succ($data);
    }

    private function jurisdictionByStaffId($staffId)
    {
        // 管辖范围
        $jurisdictions = StaffRelationsService::getInstance()->getStaffRelationsName($staffId,StaffRelationsService::TYPE_JOB_GRADE_WHITElIST);

        $result = '';
        $result .= Yii::$app->lang->get('department') . ": " . implode(",", $jurisdictions['jurisdiction_department']) . "<br/> ";
        $result .= Yii::$app->lang->get('responsible_outlets') . ": " . implode(",", $jurisdictions['jurisdiction_branch']) . "<br/>  ";
        $result .= Yii::$app->lang->get('jurisdiction_district') . ": " . implode(",", $jurisdictions['jurisdiction_district']) . "<br/> ";
        $result .= Yii::$app->lang->get('jurisdiction_area') . ": " . implode(",", $jurisdictions['jurisdiction_area']) . "<br/> ";
        $result .= Yii::$app->lang->get('store_category') . ": " . implode(",", $jurisdictions['jurisdiction_store_categories']) . "<br/> ";


        return $result;
    }

    //员工信息
    public function actionManager()
    {
        try {
            $manageType = Yii::$app->request->get('type');
            $depeartmentId = Yii::$app->request->get('sys_department_id');
            $storeId = Yii::$app->request->get('sys_store_id');
            $roles = Yii::$app->request->get('positions') ?? [];
            $name = Yii::$app->request->get('name');

            $result = [];
            if (!empty($name)) {
                $where = [
                    'OR',
                    ['LIKE', 'hr_staff_info.name', $name],
                    ['LIKE', 'hr_staff_info.staff_info_id', $name],
                ];
                if (is_numeric($name)) {
                    $where = ['staff_info_id' => $name];
                }
                // 搜索
                $result = StaffInfo::find()
                    ->select(['staff_info_id', 'name', 'state'])
                    ->where($where)
                    ->andWhere(['formal' => 1])
                    ->andWhere(['state' => 1])
                    ->andWhere(['is_sub_staff' => 0])
                    ->orderBy(['staff_info_id' => SORT_ASC])
                    ->limit(10)
                    ->asArray()
                    ->all(Yii::$app->get('r_backyard'));
            }

            if ($manageType != 'direct') {
                return $this->succ($result ?? []);
            }

            if (empty($depeartmentId) || empty($storeId)) {
                return $this->err(['miss params']);
            }

            $default = Staff::getDefaultManager($depeartmentId, $storeId, $roles);
            $result = array_filter($result, function ($row) use ($default) {
                if (!empty($default)) {
                    return $default['staff_info_id'] != $row['staff_info_id'];
                }
                return true;
            });

            if (!empty($default)) {
                $default['staff_info_id'] = (string) ($default['staff_info_id'] ?? '');
                array_unshift($result, $default);
            }
            Yii::$app->logger->write_log('actionManager 参数：' . $name .';结果:'. json_encode($result), 'info');
            return $this->succ($result);
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('actionManager 可能出现的问题：' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE).';行号:'.$e->getLine());
            return $this->err(['error']);
        }
    }

    //员工信息
    public function actionView($staff_info_id = null)
    {
        $staffView = Staff::view($staff_info_id);

        $criminalRecord = StaffCriminalRecord::find()->where(['staff_info_id' => $staff_info_id])->one();
        $relation = StaffRelationsService::getInstance()->getStaffRelations($staff_info_id,StaffRelationsService::TYPE_STAFF_MANAGEMENT);

        $staffView = array_merge($staffView,$relation, [
            'record_status' => $criminalRecord['record_status'] ?? 0,
            'record_status_text' => $criminalRecord['record_status'] ? \Yii::$app->lang->get('record_status_' . $criminalRecord['record_status']) : \Yii::$app->lang->get('record_status_0'),
        ]);


        $permission = Yii::$app->cache->get('user.permission.' . $this->fbid);
        if (!$permission) {
            return $this->err(["Please refresh the page"]);
        }
        // 在职
        if (isset($staffView['state']) && isset($staffView['wait_leave_state']) && $staffView['state'] == 1 && $staffView['wait_leave_state'] == 0) {
            if (array_intersect(['PERSONNEL_MANAGER', 'HRIS_MANAGER'], $permission['fbi.role'])) {
                $staffView = array_merge($staffView, ['is_edit_state' => 1]);
            } else {
                $staffView = array_merge($staffView, ['is_edit_state' => 0]);
            }
        } else {
            $staffView = array_merge($staffView, ['is_edit_state' => 1]);
        }

        if (isset($staffView['formal']) && $staffView['formal'] == BaseStaffInfo::FORMAL_OUTSOURCE && isset($staffView['wait_leave_state']) && $staffView['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_YES && !empty($staffView['leave_date'])) {
            $staffView['wait_leave_date'] = $staffView['leave_date'];
        }

        $staffView['has_personal_tab'] = count(array_intersect(
            array_diff(
                array_merge(staff::$view_all_roles, staff::$ev_manage_range_roles, staff::$v_manage_range_roles),
                ['SHARED_SERVICES', 'SYSTEM_ADMIN']
            ), Yii::$app->user->identity->getPermission()['fbi.role'])) ? 1 : 0;

        $staffView['has_grade_permission_tab'] = Staff::hasGradePermissionTab($staff_info_id, $this->fbid);
        $staffView = array_merge($staffView, Staff::jobGradeEditPermission($staff_info_id, $this->fbid));

        $insurance_beneficiary = HrStaffInsuranceBeneficiaryService::getInstance()->getInsuranceBeneficiaryList([$staff_info_id], Yii::$app->language);
        $staffView['beneficiary_name']          = '';
        $staffView['beneficiary_identity']      = '';
        $staffView['beneficiary_mobile']        = '';
        $staffView['beneficiary_relation']      = '';
        $staffView['beneficiary_relation_text'] = '';
        if (YII_COUNTRY != 'TH' && isset($insurance_beneficiary[$staff_info_id])) {
            $staffView['beneficiary_name']          = $insurance_beneficiary[$staff_info_id]['beneficiary_name'] ?? '';
            $staffView['beneficiary_identity']      = $insurance_beneficiary[$staff_info_id]['beneficiary_identity'] ?? '';
            $staffView['beneficiary_mobile']        = $insurance_beneficiary[$staff_info_id]['beneficiary_mobile'] ?? '';
            $staffView['beneficiary_relation']      = $insurance_beneficiary[$staff_info_id]['beneficiary_relation'] ?? '';
            $staffView['beneficiary_relation_text'] = $insurance_beneficiary[$staff_info_id]['beneficiary_relation_text'] ?? '';
        }

        //保险受益人PDF
        $staffView['insurance_beneficiary_annex_path'] = HrStaffInsuranceBeneficiaryService::getInstance()->getInsuranceAnnexPathList($staff_info_id);

        // 判断是否允许修改离职变在职
        $staffView['is_resignation_to_employment'] = false;
        $resignation_to_employment_staffids = (array)explode(",", SettingEnvService::getInstance()->getSetVal('resignation_to_employment_staffids'));
        if (!empty($resignation_to_employment_staffids) && in_array($this->fbid,$resignation_to_employment_staffids)){
            $staffView['is_resignation_to_employment'] = true;
        }
        if (!$staffView['has_grade_permission_tab']) {
            $staffView['job_title_grade_v2_name'] = null;
            $staffView['job_title_grade_v2']      = null;
            $staffView['job_title_level']         = null;
            $staffView['job_title_level_name']    = null;
        }
        //必填企业邮箱的角色（配置角色id）
        $staffView['staff_email_roles'] = SettingEnvService::getInstance()->getSetVal('staff_email_roles', ',');

        return $this->succ($staffView);
    }

    // 根据 省市县获取code
    public function actionGetPostcode () {
        $province_code = trim(Yii::$app->request->get('province_code'));  //省
        $city_code = trim(Yii::$app->request->get('city_code'));          //市/区
        $district_code = trim(Yii::$app->request->get('district_code'));  //乡/镇

        if (empty($province_code) || empty($city_code) || empty($district_code)) {
            return $this->err('parameter is empty');
        }

        $province_list = array_column(Yii::$app->sysconfig->getProvince(),null,'code');
        $city_list = array_column(Yii::$app->sysconfig->getCityList(),null,'code');
        $districtData = SysArea::getSysDistrict($province_code,$city_code,$district_code);

        $result = [
            'postal_code'   => isset($districtData['postal_code']) ? $districtData['postal_code'] : "",            // 邮编
            'province_code' => $province_code,                          // 省code
            'province_name' => isset($province_list[$province_code]['name']) ? $province_list[$province_code]['name'] : "",  // 省 名称
            'city_code'     => $city_code,                              // 市/区 code
            'city_name'     => isset($city_list[$city_code]['name']) ? $city_list[$city_code]['name'] : "",          // 市/区 名称
            'district_code' => $district_code,                          // 乡镇 code
            'district_name' => isset($districtData['name']) ? $districtData['name'] : ""                    // 乡镇 名称
        ];
        return $this->succ($result);
    }

    private function jobGradeEditPermission($staffId)
    {
        $staffs = StaffInfo::find()->where(['IN', 'staff_info_id', [$staffId, $this->fbid]])->asArray()->indexBy('staff_info_id')->all();

        // 操作人 编辑权限
        $jobGradeEditPermission = $staffs && isset($staffs[$this->fbid]) ? $staffs[$this->fbid]['job_grade_edit_permission'] : StaffInfo::JOB_GRADE_EDIT_PERMISSION_0;
        $staff_result['job_grade_edit_permission_for_fbid'] =
            $jobGradeEditPermission == StaffInfo::JOB_GRADE_EDIT_PERMISSION_2
            ||
            $jobGradeEditPermission == StaffInfo::JOB_GRADE_EDIT_PERMISSION_1 && $staffs[$this->fbid]['job_title_grade_v2'] >= $staffs[$staffId]['job_title_grade_v2']
                ? $jobGradeEditPermission : StaffInfo::JOB_GRADE_EDIT_PERMISSION_0;      // 普通权限 编辑前后都不低于操作人职级
        $staff_result['config_grade_list'] = [];
        // 员工可编辑职位等级列表 有编辑普通权限 和 特殊权限
        if ($staffs && isset($staffs[$staffId]) && $jobGradeEditPermission) {
            $staff_result['config_grade_list'] = Staff::getHrJobDepartmentRelationById($staffs[$staffId]['node_department_id'], $staffs[$staffId]['job_title']);
            $staff_result['config_grade_list'] = Staff::filterGradeListByStaff($staff_result['config_grade_list'], $staffs[$this->fbid]['job_title_grade_v2'], $jobGradeEditPermission);
        }

        return $staff_result;
    }

    private function hasGradePermissionTab($staffId)
    {
        $hris_staff_super_admin = [];
        $_tmp_hris_staff_super_admin = SettingEnv::find()->where(['code' => 'hris_staff_super_admin'])->one();
        if(!empty($_tmp_hris_staff_super_admin) && $_tmp_hris_staff_super_admin->set_val){
            $hris_staff_super_admin = explode(',',$_tmp_hris_staff_super_admin->set_val);
        }

        if (in_array('PERSONNEL_MANAGER',
                Yii::$app->user->identity->getPermission()['fbi.role'])
            ||
            in_array('SUPER_ADMIN',
                Yii::$app->user->identity->getPermission()['fbi.role'])
            && !in_array($this->fbid, $hris_staff_super_admin)
        ) {
            // 全部
            return 1;
        }
        $andConditions = [];
        if (count(array_intersect(['HR_SERVICE', 'ER', 'HRBP', 'PAYROLL_MANAGER', 'TALENT_ACQUISITION', 'HRD', 'PERSONNEL_COMMISSIONER', 'HR_OFFICER'],
            Yii::$app->user->identity->getPermission()['fbi.role']))
        ) {
            //  管辖范围
            $result = Staff::dominDepartmentsAndStores($this->fbid);
            $andConditions['node_department_id'] = $result['flash_home_departments'];
            $andConditions['sys_store_id'] = $result['stores'];

        }

        // 所属部门以及下级
        $dept_ids = SysGroupDeptV2::getDeptIdByManagerId($this->fbid, 0); // 去掉自己所属部门
        $dept_ids = array_unique(array_merge($dept_ids, SysGroupDeptV2::getDeptIdByAssistantId($this->fbid, 0))); // 去掉自己所属部门
        $dept_ids = array_values($dept_ids);
        if (isset($andConditions) && isset($andConditions['node_department_id'])) {
            $andConditions['node_department_id'] = array_merge($andConditions['node_department_id'], $dept_ids);
        } else {
            $andConditions['node_department_id'] = $dept_ids;
        }
        $next_level_staff_ids = Staff::getNextLevelStaff($this->fbid);
        $andConditions['staff_info_id'] = $next_level_staff_ids;
        if ($andConditions) {
            $orConditions[] = 'OR';
            foreach ($andConditions as $k => $v) {
                if ($k == 'sys_store_id' && in_array('-2', (array) $v)) {
                    $orConditions[] = ['NOT IN', 'hr_staff_info.' . $k, ['-1']];
                } else {
                    $orConditions[] = ['IN', 'hr_staff_info.' . $k, $v ?? []];
                }
            }

            $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');
            $query->where(['hr_staff_info.staff_info_id' => $staffId]);

            if (count($orConditions) >1) {
                $query->andWhere($orConditions);
                $row = $query->all(Yii::$app->get('r_backyard'));
                if ($row) {
                    // 上面的范围查询可以查到 就不用查白名单了
                    return 1;
                }
            }
        }

        /**
         *
         * 4. 对于不符合上述角色/身份的员工 职位等级白名单
         */
        $result = Staff::gradeWhiteListDepartmentsAndStores($this->fbid);
        $andConditions = [];
        $andConditions['node_department_id'] = $result['flash_home_departments'];
        $andConditions['sys_store_id'] = $result['stores'];
        if ($andConditions) {
            $orConditions = [];
            $orConditions[] = 'OR';
            foreach ($andConditions as $k => $v) {
                if ($k == 'sys_store_id' && in_array('-2', (array)$v)) {
                    $orConditions[] = ['NOT IN', 'hr_staff_info.' . $k, ['-1']];
                } else {
                    $orConditions[] = ['IN', 'hr_staff_info.' . $k, $v ?? []];
                }
            }
            $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');
            $query->where(['hr_staff_info.staff_info_id' => $staffId]);

            if (count($orConditions) >1) {
                $query->andWhere($orConditions);
                $row = $query->all(Yii::$app->get('r_backyard'));
                return $row ? 1 : 0;
            }
        }

        return 0;
    }

    //创建员工
    public function actionCreate()
    {
        $form = Yii::$app->request->post();
        unset($form['job_title_grade_v2'],$form['project_num'],$form['contract_company_id']);
        try{

            $validate = Staff::validate($form);//验证表单元素
            if($validate !== true) {
                return $this->err($validate);
            }
            Yii::$app->logger->write_log('actionCreate' . json_encode($form), 'info');
            $form['is_single'] = true;//是否是单个修改

            $model = Staff::combination($form);//检查表单元素
            //防止重复提交
            $staff_edit_key = 'HRIS_'.$model->mobile.$model->identity;
            $c = Yii::$app->cache->redis->get($staff_edit_key);
            if(!empty($c)) {
                return $this->err(['Please do not submit repeatedly']);
            }
            Yii::$app->cache->redis->setex($staff_edit_key, 30, $staff_edit_key);

            $before = Staff::view($model->staff_info_id) ?? [];
            //$model->forceState = $this->fbid == 10000;//废弃
            $check_hire_date = !empty($form['hire_date']) ? $form['hire_date'] : ($before['hire_date'] ?? '');
            if (isset($form['contract_expiry_date']) && !empty($check_hire_date) && $form['contract_expiry_date'] < $check_hire_date) {

                return $this->err('The contract expiration date cannot be earlier than the entry date');
            }

            $beforeRelation =  StaffRelationsService::getInstance()->getStaffRelationsName($model->staff_info_id,StaffRelationsService::TYPE_STAFF_MANAGEMENT);
            StaffRelationsService::getInstance()->saveStaffRelations($form, $this->fbid,StaffRelationsService::TYPE_STAFF_MANAGEMENT);
            $model->projectNum = $before['project_num'] ?? null;
            if (($res = Staff::save($model, $this->fbid)) && $res !== true) {
                //防止重复提交
                Yii::$app->cache->redis->del($staff_edit_key);
                return $this->err($res);
            }

            //如果是winhr 同步过来的 数据，并且职位是Van 、Bike Car 则同步添加到员工车辆管理
            //兼容winhr生成公号已存在员工管理，导致不是新数据，不能用isNewRecord来判断
            if(isset($form['data_from']) && $form['data_from']== 'winhr'){
                $vehicleTypeJobTitle =  VehicleInfoService::getInstance()->getVehicleTypeJobTitle();
                if((isset($form['job_title']) && in_array($form['job_title'],$vehicleTypeJobTitle))){
                    Staff::winhr_sync_vehicle($model->staff_info_id,$form);
                }
            }

            $after = Staff::view($model->staff_info_id);
            $afterRelation =  StaffRelationsService::getInstance()->getStaffRelationsName($model->staff_info_id,StaffRelationsService::TYPE_STAFF_MANAGEMENT);

            $log = new HrOperateLogs();
            $log->operater = $this->fbid;
            $log->request_body =  molten_get_traceid();
            $log->before = json_encode(['body' => array_merge($before, $beforeRelation)]);
            $log->after = json_encode(['body' => array_merge($after, $afterRelation)]);
            $log->type = 'staff';
            $log->staff_info_id = $model->staff_info_id;
            $r = $log->save();
            if (!$r) {
                Yii::$app->logger->write_log('员工信息日志保存失败，可能出现的问题：' . json_encode($log->getErrors(), JSON_UNESCAPED_UNICODE).';工号：'.$model->staff_info_id);
            }

            $staff_transfer_log = new HrStaffTransferLog();
            //值类型1.部门;2.职位;3.所属网点4:角色
            //部门
            if(count($before) > 0) {
                if($before['sys_department_id'] != $after['sys_department_id']) {
                    $staff_transfer_log = new HrStaffTransferLog();
                    $staff_transfer_log->staff_info_id = $model->staff_info_id;
                    $staff_transfer_log->cur_value = $after['sys_department_id'];
                    $staff_transfer_log->old_value = $before['sys_department_id'];
                    $staff_transfer_log->type = 1;
                    $staff_transfer_log->change_date = date('Y-m-d',time());
                    $staff_transfer_log->save();
                }
                //职位
                if($before['job_title'] != $after['job_title']) {
                    $staff_transfer_log = new HrStaffTransferLog();
                    $staff_transfer_log->staff_info_id = $model->staff_info_id;
                    $staff_transfer_log->cur_value = $after['job_title'];
                    $staff_transfer_log->old_value = $before['job_title'];
                    $staff_transfer_log->type = 2;
                    $staff_transfer_log->change_date = date('Y-m-d',time());
                    $staff_transfer_log->save();

                    //如果职位是从van（110）和bike（13）之间变更
                    $needVehicleJobTitle =  VehicleInfoService::getInstance()->getVehicleTypeJobTitle();
                    $vehicle_model = VehicleInfo::find()->where(['uid' => $model->staff_info_id])->one();

                    if($before['formal'] == 1 && !empty($vehicle_model) && in_array($after['job_title'],$needVehicleJobTitle)){

                        $vehicle_type = VehicleInfo::getVehicleTypeByJobTitle($after['job_title']); // $after['job_title'] == '110' ? 1 : 0; //车辆类型 van=1，bike=0
                        //变更车辆管理信息中的车辆类型信息
                        $vehicle_model->vehicle_type = $vehicle_type;
                        $vehicel_formal_data = json_decode($vehicle_model->formal_data,true);
                        $vehicel_formal_data['job_title'] = $before['job_title'];
                        $vehicle_model->formal_data = json_encode($vehicel_formal_data);
                        $vehicle_model->save();
                        //同步items更新车辆类型
                        $model->newCarType = VehicleInfo::$staffCarTypeMap[$vehicle_model->vehicle_type];
                        $model->save(false);
                    }
                }
                //网点
                if($before['sys_store_id'] != $after['sys_store_id']) {
                    $staff_transfer_log = new HrStaffTransferLog();
                    $staff_transfer_log->staff_info_id = $model->staff_info_id;
                    $staff_transfer_log->cur_value = $after['sys_store_id'];
                    $staff_transfer_log->old_value = $before['sys_store_id'];
                    $staff_transfer_log->type = 3;
                    $staff_transfer_log->change_date = date('Y-m-d',time());
                    $staff_transfer_log->save();
                }

                $before_position_category = implode(',',$before['position_category']);
                $after_position_category = implode(',',$after['position_category']);
                if($before_position_category != $after_position_category) {
                    $staff_transfer_log = new HrStaffTransferLog();
                    $staff_transfer_log->staff_info_id = $model->staff_info_id;
                    $staff_transfer_log->cur_value = $after_position_category;
                    $staff_transfer_log->old_value = $before_position_category;
                    $staff_transfer_log->type = 4;
                    $staff_transfer_log->change_date = date('Y-m-d',time());
                    $staff_transfer_log->save();
                }

                //同步变更直线上级
                $before_manager = $before['manager'] ?? '';
                $after_manager = $after['manager'] ?? '';
                if($before_manager != $after_manager) {
                    //Yii::$app->jrpc->staffChangeManager($model->staff_info_id, $before_manager, $after_manager);

                    //直线上级有变更时， 同步给kpi 直线上级变更
                    Yii::$app->jrpc->changeLeader([
                        "change_leader" => [
                            $after_manager => [$model->staff_info_id],
                        ]
                    ], "actionCreate", Yii::$app->language);
                }

            }

            $this->newStaffInfoId = $model->staff_info_id;
            //防止重复提交
            Yii::$app->cache->redis->del($staff_edit_key);

            return $this->succ(['staff_info_id' => (int) $this->newStaffInfoId]);
        } catch (\Exception $exception) {
            $result = [
                'title'=> '员工信息保存失败!!!',
                'message' => $exception->getMessage(),
                'file'    => $exception->getFile(),
                'line'    => $exception->getLine(),
                'trace'   => $exception->getTraceAsString(),
            ];
            Yii::$app->logger->write_log($result);
            return $this->err([$exception->getMessage()]);
        }
    }

    private function isJson($str)
    {
        if (is_numeric($str)) {
            return false;
        }

        if (is_string($str)) {
            json_decode($str, true);
            return json_last_error() == JSON_ERROR_NONE;
        } else {
            return false;
        }
    }



    //创建员工 - 批量创建
    public function actionCreates()
    {
        $form_post = Yii::$app->request->post();

        foreach (Yii::$app->request->post() as $key => $form) {
            $index = $key + 1;
            if (!is_array($form)) {
                $validateErrors[] = ['index' => $index, 'attribute' => 'type', 'msg' => 'request type error'];
                break;
            }
            $model = Staff::combination($form);
            $model->forceState = $this->fbid == 10000;//废弃

            if(isCountry('TH') && $form['formal'] == BaseStaffInfo::FORMAL_OUTSOURCE && isset($form['company_item_id']) && $form['company_item_id'] == OutSourcingService::COMPANY_ITEM_PREMIER && !preg_match(OutSourcingService::PREG_MATCH_OS_COMPANY_PREMIER, $form['identity'])){
                $validateErrors[] = [
                    'index' => $index,
                    'attribute' => Yii::$app->lang->get('identity'),
                    'msg' => Yii::$app->lang->get('os_staff_identity_length_premier') ,
                ];
                break;
            }

            if (!$model->validate()) {
                foreach ($model->getErrors() as $key => $row) {
                    $validateErrors[] = [
                        'index' => $index,
                        'attribute' => Yii::$app->lang->get($key),
                        'msg' => Yii::$app->lang->get($row[0]?? 'common_exception') ,
                    ];
                    break;
                }
                break;
            }
            $models[] = $model;
        }

        if (!empty($validateErrors)) {
            return ['code' => 2, 'msg' => $validateErrors];
        }

        foreach ($models as $key => $model) {
            if ($model->formal == 0) {
                $model->staff_type = 1;
            }
            try{
                if (($res = Staff::save($model, $this->fbid)) && $res !== true) {
                    Yii::$app->logger->write_log('批量创建，可能出现的问题：' . json_encode($res, JSON_UNESCAPED_UNICODE), 'info');

                    $saveErrors[] = [
                        'index' => $key + 1,
                        'attribute' => Yii::$app->lang->get($res[0]),
                        'msg' => isset($res[1])? Yii::$app->lang->get($res[1]) : 'sys error',
                    ];
                }

                $after = Staff::view($model->staff_info_id);

                $log = new HrOperateLogs();
                $log->operater = $this->fbid;
                //$log->request_body = json_encode(Yii::$app->request->post());
                $log->request_body =  molten_get_traceid();
                $log->before = json_encode(['body' => []]);
                $log->after = json_encode(['body' => $after]);
                $log->type = 'staff';
                $log->staff_info_id = $model->staff_info_id;
                $r = $log->save();
                if (!$r) {
                    Yii::$app->logger->write_log('员工信息日志保存失败，可能出现的问题：' . json_encode($log->getErrors(), JSON_UNESCAPED_UNICODE).';工号：'.$model->staff_info_id);
                }

            }catch (\Exception $e){
                $saveErrors[] = [
                    'index' => $key + 1,
                    'attribute' => $model->name,
                    'msg' => 'sys error',
                ];
                Yii::$app->logger->write_log('批量创建，可能出现的问题：' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE).';行号:'.$e->getLine());
            }

        }

        if (!empty($saveErrors)) {
            return ['code' => 3, 'msg' => $saveErrors];
        }
        return $this->succ('ok');
    }

    private function kvWrap($arr)
    {
        foreach ($arr as $key => $val) {
            $data[] = ['key' => (string) $key,
                'value' => $val,
            ];
        }
        return $data ?? [];
    }

    public function actionSysinfo(){
        $query_field = Yii::$app->request->get('query_field')??[];
        $result = SysConfigService::getInstance()->sysInfo($query_field);
        return $this->succ($result);
    }

    

    /**
     *
     * @return array
     *
     */
    public function actionPieces()
    {
        $regionId = Yii::$app->request->get("region_id");

        $pieces  = SysManagePiece::find()->select(["id", "manage_region_id", "name"])->where(["deleted" => 0])->asArray()->all(Yii::$app->get('r_backyard'));
        $pieceses = [];
        foreach ($pieces as $piece) {
            $pieceses[$piece['manage_region_id']][] = [
                "id" => $piece['id'],
                "name" => $piece['name'],
            ];
        }

        return $this->succ(['rows' => isset($pieceses[$regionId]) ? $pieceses[$regionId] : $pieces]);
    }

    /**
     * staffs/batch-upd
     * @return array
     */
    public function actionBatchUpd()
    {
        // 'state', 'leave_date', 'stop_duties_date'
        $data = Yii::$app->request->post();
        $return_data = StaffService::getInstance()->batchUpd($data,$this->fbid);

        return $this->succ($return_data);
    }

    /**
     * 薪资结构导出
     * @return array
     */
    public function actionSarryStructExport()
    {
        //获取参数
        try {
            $param = Yii::$app->request->get();
            $param['lang'] = Yii::$app->language;
            $param['user_departmentId'] = Yii::$app->user->identity->departmentId;
            $param['user_fbi_role'] = Yii::$app->user->identity->getPermission()['fbi.role'];
            $param['user_staff_id'] = $this->fbid;
            $param['file_name'] = $args['file_name'] = 'base-sarry-'.date('YmdHms').'-'.$param['lang'].'.csv';
            $args['date'] = $param;


            $action_name = 'staff-export/excel-sarry-struct';

            //hcm用导出列表
            $hcmExcelTask = new HcmExcelTask();
            $hcmExcelTask->staff_info_id = (string)$this->fbid;
            $hcmExcelTask->file_name  = Yii::$app->params['csv_file_name_prefix'].'hcm_'. $args['file_name'];//文件名前缀 $args['file_name'];
            $hcmExcelTask->executable_path = Yii::$app->basePath.'/yii';
            $hcmExcelTask->action_name = $action_name;
            $hcmExcelTask->created_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR*3600);
            $hcmExcelTask->args_json = base64_encode(json_encode($args));
            $hcmExcelTaskResult = $hcmExcelTask->save();

            if($hcmExcelTaskResult) {

                return $this->succ($this->lang->get('download_001'));
            } else {
                return $this->err(['error']);
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('flash 员工下载，可能出现的问题：' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE).';行号:'.$e->getLine());
            return $this->err(['error']);
        }
    }

    public function actionExport() {
        //获取参数
        $param = Yii::$app->request->get();
        $headers = Yii::$app->getRequest()->getHeaders();

        try {
            $param['user_departmentId'] = Yii::$app->user->identity->departmentId;
            $param['user_fbi_role'] = Yii::$app->user->identity->getPermission()['fbi.role'];
            $param['user_staff_id'] = $this->fbid;
            $param['header_from'] = $headers->get('From');
            $args['file_name'] =  uniqid('EmployeeInfo_'.date('YmdHis')).'.csv';
            $args['date'] = $param;

            $hcmExcelTaskExists = HcmExcelTask::find()->where(['staff_info_id' => $this->fbid])->andWhere(['action_name' => 'staff-export/excel-v6'])->andWhere(['status' => 0])->andWhere(['is_delete' => 0])->exists();
            if($hcmExcelTaskExists) {
                return $this->succ($this->lang->get('download_001'));
            }
            $file_name = Yii::$app->params['csv_file_name_prefix']. $args['file_name'];
            $action_name = 'staff-export/excel-v6';
            $hcmExcelTask = new HcmExcelTask();
            $hcmExcelTask->staff_info_id = (string)$this->fbid;
            $hcmExcelTask->file_name  = $file_name;//文件名前缀 $args['file_name'];
            $hcmExcelTask->executable_path = Yii::$app->basePath.'/yii';
            $hcmExcelTask->action_name = $action_name;
            $hcmExcelTask->created_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR*3600);
            $hcmExcelTask->args_json = json_encode($args,JSON_UNESCAPED_UNICODE);
            $hcmExcelTaskResult = $hcmExcelTask->save();
            if($hcmExcelTaskResult) {
                //是否切换到hcm
                if($param['project'] == 'fbi' ||  $param['header_from'] == 'fbi') {
                    $export_manager_model = new ExportManage();
                    $export_manager_model->staff_id = $this->fbid;
                    $export_manager_model->file_name = $file_name;
                    $export_manager_model->file_path = '';
                    $export_manager_model->state = ExportManage::STATUS_WAITING;
                    $export_manager_model->module_code = $action_name.'_'.$hcmExcelTask->id;
                    $export_manager_model->save();
                }
                return $this->succ($this->lang->get('download_001'));
            } else {
                return $this->err(['error']);
            }

        } catch (\Exception $e) {
            Yii::$app->logger->write_log('flash 员工下载，可能出现的问题：' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE).';行号:'.$e->getLine());
            return $this->err(['error']);
        }
    }

    public function actionExportField() {
        //$export_field = YII_COUNTRY == 'TH' ? Yii::$app->sysconfig->export_basic_field : Yii::$app->sysconfig->export_basic_field_ph;//基本信息
        $is_all = false;
        if (count(array_intersect($this->view_basic_config_role, Yii::$app->user->identity->getPermission()['fbi.role'])) > 0) {
            //$export_field = YII_COUNTRY == 'TH' ? Yii::$app->sysconfig->export_field : Yii::$app->sysconfig->export_field_ph;
            $is_all = true;
        }
        switch (YII_COUNTRY) {
            case 'TH':
                $export_field = $is_all ? Yii::$app->sysconfig->export_field : Yii::$app->sysconfig->export_basic_field;
                break;
            case 'PH':
                $export_field = $is_all ? Yii::$app->sysconfig->export_field_ph : Yii::$app->sysconfig->export_basic_field_ph;
                break;
            case 'MY':
                $export_field = $is_all ? Yii::$app->sysconfig->export_field_my : Yii::$app->sysconfig->export_basic_field_my;
                break;
            case 'ID':
                $export_field = $is_all ? Yii::$app->sysconfig->export_field_id : Yii::$app->sysconfig->export_basic_field_id;
                break;
            case 'VN':
                $export_field = $is_all ? Yii::$app->sysconfig->export_field_vn : Yii::$app->sysconfig->export_basic_field_vn;
                break;
            case 'LA':
                $export_field = $is_all ? Yii::$app->sysconfig->export_field_la : Yii::$app->sysconfig->export_basic_field_la;
                break;
            default:
                $export_field = $is_all ? Yii::$app->sysconfig->export_field : Yii::$app->sysconfig->export_basic_field;
        }

        $isHas15 = false;
        $staff_info_download_env = SettingEnv::find()->where(['code' => 'job_grade_download'])->one();
        if(!empty($staff_info_download_env) && $staff_info_download_env->set_val){
            $staff_info_download = explode(',', $staff_info_download_env->set_val);
            if(in_array(Yii::$app->user->identity->getId(), $staff_info_download)) {
                $isHas15 = true;
            }
        }

        $isHasPersonalTab = count(array_intersect(
            array_diff(
                array_merge(staff::$view_all_roles, staff::$ev_manage_range_roles, staff::$v_manage_range_roles),
                ['SHARED_SERVICES', 'SYSTEM_ADMIN']
            ), Yii::$app->user->identity->getPermission()['fbi.role']));
        foreach ($export_field as $key => $val) {
            if ($key == 15 && $isHas15 || $key != 15) {
                if(in_array($key, $this->personal_field) && !$isHasPersonalTab) {
                    continue;
                }
                if ($key == 91 && YII_COUNTRY == 'PH') {
                    $data[] = ['key' => (string) $key,
                        'value' => $this->lang->get($val."_ph"),
                    ];
                } else {
                    $data[] = ['key' => (string) $key,
                        'value' => $this->lang->get($val),
                    ];
                }
            }
        }
        return $this->succ($data ?? []);
    }


    public function actionExportHold() {
        $storeTemp = SysStoreTemp::temp();
        $origins = $this->searchQuery()->andFilterWhere(['payment_state' => 2])->indexBy('staff_info_id')->all(Yii::$app->get('r_backyard'));
        if (empty($origins)) {
            return false;
        }

        $allDepeartments = SysDepartmentService::getInstance()->getDataUseIdIndex(['id', 'name'], 'name');
        $config_JobTitle = Yii::$app->sysconfig->jobTitle;
        $sorting_no_my = array_flip(AreaManager::$areasMy);
        $sorting_no_la = array_flip(AreaManager::$areasLa);

        $staff_info_ids = array_column($origins, 'staff_info_id');
        $hold_cycle_list = StaffHold::getStaffHoldCycleList(['staff_info_ids' => $staff_info_ids]);
        foreach ($origins as $one) {
            $row[$this->lang->get('staff_no')] = $one['staff_info_id'];
            $row[$this->lang->get('name')] = $one['name'];
            $row[$this->lang->get('name_en')] = $one['name_en'];
            $row[$this->lang->get('gender')] = $this->lang->get($one['sex'] == 1 ? 'male' : ($one['sex'] == 2 ? 'female' : ''));
            $row[$this->lang->get('job_title')] = $config_JobTitle[$one['job_title']] ?? $one['job_title'];
            $storeInfoOne = $storeTemp[$one['sys_store_id']] ?? [];
            if(YII_COUNTRY != 'PH') {
                $row[$this->lang->get('respective_region')] = '';
            }
            if ($one['sys_store_id'] != -1) {
                switch (YII_COUNTRY) {
                    case 'MY':
                        $row[$this->lang->get('respective_region')] = isset($storeInfoOne['sorting_no']) && isset($sorting_no_my[$storeInfoOne['sorting_no']]) ? $storeInfoOne['sorting_no'] : '';
                        break;
                    case 'LA':
                        $row[$this->lang->get('respective_region')] = isset($storeInfoOne['sorting_no']) && isset($sorting_no_la[$storeInfoOne['sorting_no']]) ? $storeInfoOne['sorting_no'] : '';
                        break;
                    case 'PH'://菲律宾去掉 所属区域
                        unset($row[$this->lang->get('respective_region')]);
                        break;
                    default:
                        $area = Yii::$app->sysconfig->getAreaByProvinceCode($storeTemp[$one['sys_store_id']]['province_code'] ?? '');
                        if (!empty($area)) {
                            $row[$this->lang->get('respective_region')] = $area['name'] ?? ''; //'所属区域';
                        }
                }
            }
            $row[$this->lang->get('department')] = $allDepeartments[$one['sys_department_id']] ?? $one['sys_department_id'];
            if($one['state'] == 1 && $one['wait_leave_state'] == 1) {
                $row[$this->lang->get('state')] = $this->lang->get('wait_leave_state');
            } else {
                $row[$this->lang->get('state')] = $this->lang->get('state_' . $one['state']);
            }
            $row[$this->lang->get('hire_date')] = (date('Y-m-d', strtotime($one['hire_date'])));
            $row[$this->lang->get('leave_date')] = $one['state'] == 2 && $one['leave_date'] ? (date('Y-m-d', strtotime($one['leave_date']))) : '';
            //hold 时间
            $row['Updated At'] = $one['updated_at'];
            //hold类型
            if(!empty($one['stop_payment_type'])) {
                $stop_payment_type = explode(',', $one['stop_payment_type']);
                $stop_payment = [];
                foreach ($stop_payment_type as $k => $v) {
                    $stop_payment[] = $this->lang->get('stop_payment_type_'.$v);
                }
                $row[$this->lang->get('stop_payment_type')] = implode(",",$stop_payment);
            } else {
                $row[$this->lang->get('stop_payment_type')] = '';
            }

            //hold原因
            if(!empty($one['payment_markup'])) {
                $payment_markup_arr = array_filter(explode(',',$one['payment_markup']));
                $payment_markup_name = [];
                foreach ($payment_markup_arr as $p_k => $p_v) {
                    $payment_markup_name[] = $this->lang->get('payment_markup_' . $p_v);
                }
                $row[$this->lang->get('payment_state_block_reason')] = implode(";", $payment_markup_name);
            } else {
                $row[$this->lang->get('payment_state_block_reason')] = '';
            }
            $hold_cycle = $hold_cycle_list[$one['staff_info_id']] ?? '';
            $row[$this->lang->get('staff_hold_cycle')] = !empty($hold_cycle) ? implode(',', $hold_cycle) : '';

            if (!isset($arr)) {
                $arr[] = array_keys($row);
            }
            $arr[] = $row;
        }

        $file_name = 'EmployeeInfoHold'.date('YmdHms').'.csv';
        //上传oss
        $upload_oss_result = Yii::$app->csv->filePut_v2($arr, $file_name);
        $data['path'] = $upload_oss_result['object_url'];
        return $this->succ($data);
    }

    //hold 导出
    public function actionExportHoldHcm() {
        try {
            $headers = Yii::$app->getRequest()->getHeaders();
            $param = Yii::$app->request->get();
            $param['header_from'] = $headers->get('From');
            $param['user_departmentId'] = Yii::$app->user->identity->departmentId;
            $param['user_fbi_role'] = Yii::$app->user->identity->getPermission()['fbi.role'];
            $param['user_staff_id'] = $this->fbid;
            $args['file_name'] = 'Hold_'.date('YmdHms').'.csv';
            $args['date'] = $param;
            $action_name = 'staff-export/staff-hold';
            $hcmExcelTaskExists = HcmExcelTask::find()->where(['staff_info_id' => $this->fbid])->andWhere(['action_name' => 'staff-export/staff-hold'])->andWhere(['status' => 0])->andWhere(['is_delete' => 0])->exists();
            if($hcmExcelTaskExists) {
                return $this->succ($this->lang->get('download_001'));
            }
            $file_name = Yii::$app->params['csv_file_name_prefix']. $args['file_name'];
            //hcm用导出列表
            $hcmExcelTask = new HcmExcelTask();
            $hcmExcelTask->staff_info_id = (string)$this->fbid;
            $hcmExcelTask->file_name  = $file_name;//文件名前缀 $args['file_name'];
            $hcmExcelTask->executable_path = Yii::$app->basePath.'/yii';
            $hcmExcelTask->action_name = $action_name;
            $hcmExcelTask->created_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR*3600);
            $hcmExcelTask->args_json = base64_encode(json_encode($args));
            $hcmExcelTaskResult = $hcmExcelTask->save();
            if($hcmExcelTaskResult) {
                if($param['header_from'] == 'fbi') {
                    $export_manager_model = new ExportManage();
                    $export_manager_model->staff_id = $this->fbid;
                    $export_manager_model->file_name = $file_name;
                    $export_manager_model->file_path = '';
                    $export_manager_model->state = ExportManage::STATUS_WAITING;
                    $export_manager_model->module_code = $action_name.'_'.$hcmExcelTask->id;
                    $export_manager_model->save();
                }
                return $this->succ($this->lang->get('download_001'));
            } else {
                return $this->err(['error']);
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('flash 员工下载，可能出现的问题：' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE).';行号:'.$e->getLine());
            return $this->err(['error']);
        }
    }

    //重置密码
    public function actionResetPassword() {
        try {
            $lang          = Yii::$app->language;
            $form          = Yii::$app->request->post();
            $staff_account = StaffAccount::getStaffLastLanguage($form['staff_info_id']);
            if (!empty($staff_account['accept_language'])) {
                $lang = $staff_account['accept_language'];
            }
            $result = Staff::resetPassword($form['staff_info_id'], $this->fbid, $lang);
            if ($result) {
                //重置成功
                return $this->succ('ok');
            } else {
                //重置失败
                return $this->err('error');
            }
        } catch (ValidationException $e) {
            return $this->err($e->getMessage());
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('员工密码重置失，可能出现的问题：' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE).';行号:'.$e->getLine(),'info');
            return $this->err($e->getMessage());
        }
    }

    /**
     * [staffBill 拿到快递员回款明细 ]
     * @return [type] [// 5321 【PHP】 【FBI】 【HRIS】记录员工未回公款情况 - panjia - 2019年8月9日17:59:09]
     * <AUTHOR> <[email address]>
     */
    public function staffBill($staff_info_id = [])
    {
        // 无数据直接返回
        if( empty($staff_info_id) ) return [];

        /*处理参数*/
        if( count($staff_info_id) == 1 ){
            $staff_info_id_in = " AND `staff_info_id` = '{$staff_info_id[0]}'";
        }elseif( count($staff_info_id) < 2000 ){
            $staff_info_id_in = " AND `staff_info_id` IN (".implode(",", $staff_info_id).") ";
        }else{
            $staff_info_id_in = '';
        }

        // 时间
        $date = " < '".date('Y-m-d', strtotime("+0 days"))."'";

        /*历史快递员回款*/
        $sql = "
                SELECT
                    staff_info_id,
                    count(staff_info_id) num,
                    SUM(receivable_amount)/100 amount
                FROM
                    store_receivable_bill_detail 
                WHERE
                    state = 0
                {$staff_info_id_in}
                AND
                    business_date {$date}
                GROUP BY
                    staff_info_id
            ";

        /*執行sql*/
        $staffBill = Yii::$app->fle_ads->createCommand($sql)->queryAll();

        /*初始化数据*/
        $data = [];

        /*处理数据*/
        if( !empty($staffBill) ){
            $data = [];
            foreach ($staffBill as $v) {
                $data[$v['staff_info_id']] = $v;
            };
        }
        return $data;
    }

    /**
     * [remittanceBill 拿到网点出纳历史回款明细 ]
     * @return [type] [// 5321 【PHP】 【FBI】 【HRIS】记录员工未回公款情况 - panjia - 2019年8月10日16:10:16]
     * <AUTHOR> <[email address]>
     */
    public function remittanceBill($staff_info_id = [])
    {
        // 无数据直接返回
        if( empty($staff_info_id) ) return [];

        /*处理参数*/
        if( count($staff_info_id) == 1 ){
            $staff_info_id_in = " AND `staff_info_id` = '{$staff_info_id[0]}'";
        }elseif( count($staff_info_id) < 2000 ){
            $staff_info_id_in = " AND `staff_info_id` IN (".implode(",", $staff_info_id).") ";
        }else{
            $staff_info_id_in = '';
        }
        // 时间
        $date = " < '".date('Y-m-d', strtotime("+0 days"))."'";

        /*历史网点出纳回款*/
        $sql = "
                SELECT
                    `info`.`id` staff_info_id,
                    COUNT(`info`.`id`) num,
                    SUM(IF(`bill`.`parcel_state`=0,`bill`.`parcel_amount`,0)+IF(`bill`.`cod_state`=0,`bill`.`cod_amount`,0))/100 amount
                FROM
                    `staff_info_position` position
                JOIN
                    `staff_info` info ON `info`.`id` = `position`.`staff_info_id`
                JOIN
                    `store_remittance_bill` bill ON `bill`.`store_id` = `info`.`organization_id`
                WHERE
                    `bill`.`state` = 0
                {$staff_info_id_in}
                AND
                    `position_category` = 4
                AND
                    `business_date` {$date}
                GROUP BY
                    `info`.`id`
            ";

        /*執行sql*/
        $remittanceBill = Yii::$app->fle->createCommand($sql)->queryAll();

        /*初始化数据*/
        $data = [];

        /*处理数据*/
        if( !empty($remittanceBill) ){
            $data = [];
            foreach ($remittanceBill as $v) {
                $data[$v['staff_info_id']] = $v;
            };
        }
        return $data;
    }

    //导出外协员工数据
    public function actionExportAssist() {
        //获取参数
        $param = Yii::$app->request->get();
        $headers = Yii::$app->getRequest()->getHeaders();

        try {
            $param['user_departmentId'] = Yii::$app->user->identity->departmentId;
            $param['user_fbi_role'] = Yii::$app->user->identity->getPermission()['fbi.role'];
            $param['user_staff_id'] = $this->fbid;
            $param['header_from'] = $headers->get('From');
            $args['file_name'] = 'Outsourcing_Staff_'.date('YmdHms').'.csv';
            $args['data'] = $param;

            $hcmExcelTaskExists = HcmExcelTask::find()->where(['staff_info_id' => $this->fbid])->andWhere(['action_name' => 'staff-export/excel-assist'])->andWhere(['status' => 0])->andWhere(['is_delete' => 0])->exists();
            if($hcmExcelTaskExists) {
                return $this->succ($this->lang->get('download_001'));
            }
            $file_name = Yii::$app->params['csv_file_name_prefix']. $args['file_name'];
            $action_name = 'staff-export/excel-assist';
            $hcmExcelTask = new HcmExcelTask();
            $hcmExcelTask->staff_info_id = (string)$this->fbid;
            $hcmExcelTask->file_name  = $file_name;//文件名前缀 $args['file_name'];
            $hcmExcelTask->executable_path = Yii::$app->basePath.'/yii';
            $hcmExcelTask->action_name = $action_name;
            $hcmExcelTask->created_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR*3600);
            $hcmExcelTask->args_json = base64_encode(json_encode($args,JSON_UNESCAPED_UNICODE));
            $hcmExcelTaskResult = $hcmExcelTask->save();
            if($hcmExcelTaskResult) {
                //是否切换到hcm
                if($param['project'] == 'fbi' ||  $param['header_from'] == 'fbi') {
                    $export_manager_model = new ExportManage();
                    $export_manager_model->staff_id = $this->fbid;
                    $export_manager_model->file_name = $file_name;
                    $export_manager_model->file_path = '';
                    $export_manager_model->state = ExportManage::STATUS_WAITING;
                    $export_manager_model->module_code = $action_name.'_'.$hcmExcelTask->id;
                    $export_manager_model->save();
                }
                return $this->succ($this->lang->get('download_001'));
            } else {
                return $this->err(['error']);
            }

        } catch (\Exception $e) {
            Yii::$app->logger->write_log('flash 外协员工下载，可能出现的问题：' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE).';行号:'.$e->getLine());
            return $this->err(['error']);
        }
    }

    private function searchQuery()
    {
        //新版查询
        $whereDepartment = Yii::$app->request->get('department_id');
        $dept_detail = [];
        if(!empty($whereDepartment)) {
            $dept_detail = SysDepartment::find()
                ->select(['id', 'name', 'ancestry', 'ancestry_v2', 'ancestry_v3', 'type', 'level', 'group_boss_id'])
                ->andWhere(['id' => $whereDepartment])
                ->asArray()
                ->one();
            if(!empty($dept_detail)) {
                $ancestry_v3 = empty($dept_detail['ancestry_v3']) ? $dept_detail['id'] : $dept_detail['ancestry_v3'];

                $dept_list_query = SysDepartment::find()->select(['id', 'name', 'ancestry', 'ancestry_v2', 'ancestry_v3', 'type', 'level', 'group_boss_id'])
                    ->andWhere(['LIKE', 'ancestry_v3', $ancestry_v3.'/%', false])
                    ->orWhere(['id' => $whereDepartment]);
                /*
                if($dept_detail['type'] != 1) {
                    $dept_list_query->orWhere(['id' => $whereDepartment]);
                }
                */
                $dept_list = $dept_list_query->asArray()->indexBy('id')->all();
                if(!empty($dept_list)) {
                    $whereDepartment = array_column($dept_list,'id');
                }
            }
        }
        /*
        if (Yii::$app->user->identity->departmentId == 13 && !in_array('SUPER_ADMIN', Yii::$app->user->identity->getPermission()['fbi.role'])) {
            $whereDepartment = 13;
        }
        */
        // fbi.role
        if (!empty(Yii::$app->request->get('job_title'))) {
            $jobTitle = Yii::$app->request->get('job_title');
        }
        $state = null;
        if (!empty(Yii::$app->request->get('state'))) {
            $state = Yii::$app->request->get('state');
        }

        if (!empty(Yii::$app->request->get('store'))) {
            $store = Yii::$app->request->get('store');
        }

        $name = null;
        if (!empty(Yii::$app->request->get('name'))) {
            $name = trim(Yii::$app->request->get('name'));
        }

        if (!empty(Yii::$app->request->get('manage_staff_id'))) {
            $manger = Yii::$app->request->get('manage_staff_id');
        }

        if (!empty(Yii::$app->request->get('staff_info_id'))) {
            $staff_info_id = Yii::$app->request->get('staff_info_id');
        }

        if (!empty(Yii::$app->request->get('nationality'))) {
            $nationality = Yii::$app->request->get('nationality');
        }

        if (!empty(Yii::$app->request->get('working_country'))) {
            $working_country = Yii::$app->request->get('working_country');
        }

        if (!empty(Yii::$app->request->get('store_area_id'))) {
            switch (YII_COUNTRY) {
                case 'MY':
                    $stores = Yii::$app->sysconfig->getStoresBySortingNoV2(AreaManager::$areasMy, Yii::$app->request->get('store_area_id'));
                    break;
                case 'LA':
                    $stores = Yii::$app->sysconfig->getStoresBySortingNoV2(AreaManager::$areasLa, Yii::$app->request->get('store_area_id'));
                    break;
                default:
                    $stores = Yii::$app->sysconfig->getStoresByAreaV2(Yii::$app->request->get('store_area_id'));
            }
        }

        $mobileIdentityBank = null;
        if (!empty(Yii::$app->request->get('mobile_identity_bank'))) {
            $mobileIdentityBank = trim(Yii::$app->request->get('mobile_identity_bank'));
        }

        $staffType = null;
        if (!empty(Yii::$app->request->get('staff_type'))) {
            $staff_type = trim(Yii::$app->request->get('staff_type'));
            switch ($staff_type) {
                case 1:
                    $staffType = [1];
                    break;
                case 2:
                    $staffType = [2, 3];
                    break;
                case 5:
                    $staffType = [5];
                    break;
            }
        }

        $notEqStaff = null;
        if (!empty(Yii::$app->request->get('not_eq_staff'))) {
            $notEqStaff = trim(Yii::$app->request->get('not_eq_staff'));
        }

        /*
         * 判断条件
         * 指定角色(系统管理员[14]/人事专员[16]/人事管理员[17]/HRIS管理员[41]/薪酬管理员[42]/人力资源[43]/HRBP[68]) 可以查看全部
         *
         *指定部门查询
         *
         */
        $_view = Yii::$app->request->get('_view');
        //$config_role = ['SUPER_ADMIN','SYSTEM_ADMIN', 'PERSONNEL_COMMISSIONER', 'PERSONNEL_MANAGER', 'HRIS_MANAGER', 'PAYROLL_MANAGER', 'HR_OFFICER', 'HRBP'];
        //$config_view = ['formal','store','trainee','download'];
//        if (in_array($_view, $this->config_view) && count(array_intersect($this->config_role, Yii::$app->user->identity->getPermission()['fbi.role'])) == 0) {
//            //运营主管[63]权限：仅查看——所属一级部门及一级下级各级部门员工
//            if(in_array('OPERATION_SPECIALIST', Yii::$app->user->identity->getPermission()['fbi.role'])) {
//                $dept_ids = SysGroupDeptV2::getDeptIdByManagerIdV2($this->fbid, Yii::$app->user->identity->departmentId);
//            } else {
//                $dept_ids = SysGroupDeptV2::getDeptIdByManagerId($this->fbid, Yii::$app->user->identity->departmentId);
//            }
//            $next_level_staff_ids = Staff::getNextLevelStaff($this->fbid);
//            $new_query = StaffInfo::find()->andFilterWhere(['node_department_id' => $dept_ids])->orFilterWhere(['staff_info_id' => $next_level_staff_ids]);
//            $query = (new Query())->select('hr_staff_info.*')->from(['hr_staff_info' => $new_query]);
//        } else {
//            $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');
//        }

        if (in_array($_view, $this->config_view)) {
            if (count(array_intersect(staff::$view_all_roles, Yii::$app->user->identity->getPermission()['fbi.role']))) {
                // 所有员工
                $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');
            } else {
                $andConditions = [];
                if (count(array_intersect(staff::$view_manage_range_roles, Yii::$app->user->identity->getPermission()['fbi.role']))) {
                    //  管辖范围
                    $result = Staff::dominDepartmentsAndStores($this->fbid);
                    $andConditions['node_department_id'] = $result['flash_home_departments'];
                    $andConditions['sys_store_id'] = $result['stores'];
                }
                if (in_array('OPERATION_SPECIALIST', Yii::$app->user->identity->getPermission()['fbi.role'])) {
                    //运营主管[63]权限：仅查看——所属一级部门及一级下级各级部门员工
                    $dept_ids = SysGroupDeptV2::getDeptIdByManagerIdV2($this->fbid, Yii::$app->user->identity->departmentId);
                } else {
                    $dept_ids = SysGroupDeptV2::getDeptIdByManagerId($this->fbid, 0); // 去掉自己所属部门
                    $dept_ids = array_unique(array_merge($dept_ids, SysGroupDeptV2::getDeptIdByAssistantId($this->fbid, 0))); // 去掉自己所属部门
                    $dept_ids = array_values($dept_ids);
                }

                if (isset($andConditions) && isset($andConditions['node_department_id'])) {
                    $andConditions['node_department_id'] = array_merge($andConditions['node_department_id'], $dept_ids);
                } else {
                    $andConditions['node_department_id'] = $dept_ids;
                }

                $next_level_staff_ids = Staff::getNextLevelStaff($this->fbid);
                $andConditions['staff_info_id'] = $next_level_staff_ids;
                $orConditions[] = 'OR';
                foreach ($andConditions as $k => $v) {
                    if ($k == 'sys_store_id' && in_array('-2', (array) $v)) {
                        $orConditions[] = ['NOT IN', 'hr_staff_info.' . $k, ['-1']];
                    } else {
                        $orConditions[] = ['IN', 'hr_staff_info.' . $k, $v ?? []];
                    }
                }
                Yii::$app->logger->write_log('rolesPermission 结果：' . json_encode($andConditions, JSON_UNESCAPED_UNICODE), 'info');
                $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');
            }
        } else {
            $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');
        }

        // 合作商
        $formal = Yii::$app->request->get('formal');
        if (Yii::$app->request->get('is_offical_partner') && empty($formal)) {
            $query->where(['IN', 'hr_staff_info.formal', [2, 3]]);
        } else if (!empty(Yii::$app->request->get('from')) && Yii::$app->request->get('from') == 'job_title_grade_whitelist') {
            $query->where(['IN', 'hr_staff_info.formal', [1, 4]]);
        } else {
            if($formal == 1) {//如果是正式员工列表，包含实习生
                $query->where(['IN', 'hr_staff_info.formal', [1, 4]]);
            } else {
                $query->where(['hr_staff_info.formal' => $formal]);
            }
        }

        if (isset($orConditions)) {

            $query->andWhere($orConditions);
        }
        // 雇佣类型
        $hireType = null;
        if (!empty(Yii::$app->request->get('hire_type'))) {
            $hireType = Yii::$app->request->get('hire_type');
            $query->andwhere(['IN', 'hr_staff_info.hire_type', $hireType]);
        }

        // 入职日期-开始
        if (!empty(Yii::$app->request->get('hire_date_begin'))) {
            $begin = date('Y-m-d', strtotime(Yii::$app->request->get('hire_date_begin')));
            $query->andFilterWhere(['>=', 'hr_staff_info.hire_date', $begin]);
        }
        // 入职日期-结束
        if (!empty(Yii::$app->request->get('hire_date_end'))) {
            $begin = date('Y-m-d', strtotime(Yii::$app->request->get('hire_date_end')));
            $query->andFilterWhere(['<=', 'hr_staff_info.hire_date', $begin]);
        }

        // 离职日期-开始
        if (!empty(Yii::$app->request->get('leave_date_begin'))) {
            $begin = date('Y-m-d', strtotime(Yii::$app->request->get('leave_date_begin')));
            $query->andFilterWhere(['>=', 'hr_staff_info.leave_date', $begin]);
        }
        // 离职日期-结束
        if (!empty(Yii::$app->request->get('leave_date_end'))) {
            $begin = date('Y-m-d', strtotime(Yii::$app->request->get('leave_date_end')));
            $query->andFilterWhere(['<=', 'hr_staff_info.leave_date', $begin]);
        }

        // 仅仅为快捷入口站点
        if (!empty(Yii::$app->request->get('is_only_store'))) {
            $query->andFilterWhere(['<>', 'sys_store_id', '-1']);
        }

        //职级
        if(is_numeric(Yii::$app->request->get('job_title_grade')) || is_array(Yii::$app->request->get('job_title_grade'))) {
            $job_title_grade = Yii::$app->request->get('job_title_grade');
        }

        // from
        if (!empty(Yii::$app->request->get('from'))) {
            if (Yii::$app->request->get('from') == 'job_title_grade_whitelist') {

                $query->andFilterWhere(['hr_staff_info.is_has_job_grade_permission' => 1]);
            }
        }

        if(!empty($state)) {
            if(in_array(999, $state)) {
                $query->andFilterWhere(
                    [
                        'OR', ['hr_staff_info.state' => $state], ['hr_staff_info.wait_leave_state' => 1]
                    ]
                );
            } else {
                if ($formal == 0 && in_array(YII_COUNTRY, ['MY', 'PH']) && in_array(1, $state)) {
                    // 外协员工的马来 菲律宾  只有在职和离职 需要特殊处理
                    $query->andFilterWhere(['hr_staff_info.state' => $state])
                        ->andFilterWhere(['hr_staff_info.wait_leave_state' => [0,  1]]);
                } else {
                    $query->andFilterWhere(['hr_staff_info.state' => $state])
                        ->andFilterWhere(['hr_staff_info.wait_leave_state' => 0]);
                }
            }
        }
        if(!empty($formal) && in_array($formal,[1,4])) {
            $query->andFilterWhere(['node_department_id' => $whereDepartment ?? null]);
        } else {
            if(!empty($whereDepartment) && !empty($dept_detail)) {
                if($dept_detail['type'] == 1) { //公司
                    $dept_list_query = SysDepartment::find()->select(['id', 'name', 'ancestry', 'ancestry_v2', 'type', 'level', 'group_boss_id'])
                        ->where(['deleted' => 0])->andWhere(['LIKE', 'ancestry_v2', $whereDepartment.'/%', false])->asArray()->indexBy('id')->all();

                    $dept_ids = array_column($dept_list_query,'id');
                    $query->andFilterWhere(['hr_staff_info.sys_department_id' => $dept_ids ?? null]);//公司取该公司下面的所有
                } else if($dept_detail['type'] ==2 ) { //正常部门 如果是
                    $ancestry_arr = explode('/',$dept_detail['ancestry_v2']);
                    if(count($ancestry_arr) == 2) {
                        $query->andFilterWhere(['hr_staff_info.sys_department_id' => $whereDepartment ?? null]);
                    } else {
                        $last_id = count($ancestry_arr) -1;
                        $query->andFilterWhere(['hr_staff_info.sys_department_id' => $ancestry_arr[1] ?? null]);
                        $query->andFilterWhere(['hr_staff_info.sys_department_id' => $ancestry_arr[$last_id] ?? null]);
                    }

                } else { //type=3 组织下的部门
                    $ancestry_arr = explode('/',$dept_detail['ancestry_v2']);
                    if(count($ancestry_arr) == 1) {
                        $query->andFilterWhere(['hr_staff_info.sys_department_id' => $whereDepartment ?? null]);
                    } else {
                        $last_id = count($ancestry_arr) -1;
                        $query->andFilterWhere(['hr_staff_info.sys_department_id' => $ancestry_arr[0] ?? null]);
                        $query->andFilterWhere(['hr_staff_info.sys_department_id' => $ancestry_arr[$last_id] ?? null]);
                    }
                }
            }
        }

        $query->andFilterWhere(['job_title' => $jobTitle ?? null])
            ->andFilterWhere(['hr_staff_info.sys_store_id' => $store ?? null])
            ->andFilterWhere(['hr_staff_info.sys_store_id' => $stores ?? null])
            ->andFilterWhere(['hr_staff_info.staff_type' => $staffType ?? null])
            ->andFilterWhere(['hr_staff_info.working_country' => $working_country ?? null])
            ->andFilterWhere(['hr_staff_info.manger' => $manger ?? null])
            ->andFilterWhere(['hr_staff_info.staff_info_id' => $staff_info_id ?? null])
            ->andFilterWhere(['hr_staff_info.nationality' => $nationality ?? null])
            ->andFilterWhere(['hr_staff_info.job_title_grade_v2' => $job_title_grade ?? null])
            ->andFilterWhere(['OR',
                ['LIKE', 'hr_staff_info.name', $name],
                ['LIKE', 'hr_staff_info.staff_info_id', $name],
                ['LIKE', 'hr_staff_info.emp_id', $name],
                ['LIKE', 'hr_staff_info.name_en', $name],
                ['LIKE', 'hr_staff_info.nick_name', $name],
            ])
            ->andFilterWhere(
                [
                    'OR',
                    [
                        'mobile' => $mobileIdentityBank,

                    ],
                    [
                        'bank_no' => $mobileIdentityBank,

                    ],
                    [
                        'identity' => $mobileIdentityBank,
                    ],
                ]
            )
            ->andFilterWhere(['<>', 'hr_staff_info.staff_info_id', $notEqStaff]);

        $positions = Yii::$app->request->get('position');
        if (!empty($positions)) {
            $positions = !is_array($positions) ? explode(',',$positions) : $positions;
            $subQuery = (new Query())->select('staff_info_id')
                ->where(['IN', 'hr_staff_info_position.position_category', array_values($positions)])
                ->andWhere('hr_staff_info.staff_info_id = hr_staff_info_position.staff_info_id')
                ->from('hr_staff_info_position');
            $query->andWhere(['IN', 'hr_staff_info.staff_info_id', $subQuery]);
        }

        // 关联查询
        if (!empty(Yii::$app->request->get('pay_type')) || !empty(Yii::$app->request->get('outsourcing_type'))) {
            $item_where = [];
            $value_where = [];
            // 外协类型 改为结算类型
            if (!empty(Yii::$app->request->get('pay_type'))) {

                $item_where[] = StaffItems::ITEM_PAY_TYPE;
                $value_where[] = Yii::$app->request->get('pay_type');
            }
            //外协类型 individual  company
            if(!empty(Yii::$app->request->get('outsourcing_type'))) {
                $item_where[] = 'OUTSOURCING_TYPE';
                $value_where[] = Yii::$app->request->get('outsourcing_type');
            }


            if(!empty($item_where) && !empty($value_where)) {
                $subQuery = (new Query())->select('staff_info_id')
                    ->where([
                        'hr_staff_items.item' => $item_where,
                        'hr_staff_items.value' => $value_where,
                    ])
                    ->andWhere('hr_staff_info.staff_info_id = hr_staff_items.staff_info_id')
                    ->from('hr_staff_items');
                $query->andWhere(['IN', 'hr_staff_info.staff_info_id', $subQuery]);

            }
        }

        // 排除子账号，关联子账号使用
        if (!empty(Yii::$app->request->get('can_master_staff'))) {
            $subQuery = (new Query())->select('staff_info_id')
                ->where([
                    'hr_staff_items.item' => StaffItems::IIEM_MASTER_STAFF,
                ])
                ->andWhere('hr_staff_info.staff_info_id = hr_staff_items.staff_info_id')
                ->from('hr_staff_items');
            $query->andWhere(['NOT IN', 'hr_staff_info.staff_info_id', $subQuery]);
        }

        $query->andWhere(['hr_staff_info.is_sub_staff' => 0]);

        return $query;
    }

    public function actionOperateLog()
    {
        $operater = Yii::$app->request->get('operater');
        $staffInfoId = Yii::$app->request->get('staff_info_id');
        if (empty($operater) && empty($staffInfoId)) {
            return $this->err(['miss param']);
        }

        //$result = HrOperateLogs::find()
        //    ->filterWhere(['operater' => $operater, 'staff_info_id' => $staffInfoId])
        //    ->andWhere(['<>','type', 'salary'])
        //    ->orderBy(['id' => SORT_DESC])
        //    ->asArray()->all();
        //$where = " type<>'salary' ";
        $where = "  type not in ('salary','assets_edit','assets_del') ";
        if(!empty($operater)) {
            if(!preg_match("/^[0-9]*$/",$operater)) {
                return $this->err(['param error']);
            }
            $where .= ' and operater='.$operater;
        }
        if(!empty($staffInfoId)) {
            if(!preg_match("/^[0-9]*$/",$staffInfoId)) {
                return $this->err(['param error']);
            }
            $where .= ' and staff_info_id='.$staffInfoId;
        }

        try {
            $result = Yii::$app->get('r_backyard')->createCommand("SELECT * FROM hr_operate_logs  WHERE ".$where." ORDER BY id DESC  LIMIT 0,200 ")->queryAll();
            $stores = SysStoreTemp::temp();
            $provinces = array_column(SysProvince::find()->all(),"sorting_no","id");
            $regions = array_column(SysManageRegion::find()->all(),"name","id");
            $pieces = array_column(SysManagePiece::find()->all(),"name","id");
            $response = [];
            $job_titles = HrJobTitle::find()
                ->select('job_name')
                ->indexBy('id')
                ->orderBy(['job_name' => SORT_ASC])
                ->asArray()
                ->column();
            foreach ($result as $key => $obj) {
                $before = json_decode($obj['before'], true)['body'] ?? [];
                if(!is_array($before)) {
                    $before = json_decode($before,true);
                }
                $after = json_decode($obj['after'], true)['body'] ?? [];
                if(!is_array($after)) {
                    $after = json_decode($after,true);
                }
                if (count($before) == 0 && count($after) == 0) {
                    continue;
                }
                $changes = [];
                $tableChanges = [];
                array_filter($after, function ($afval, $afkey) use ($before, &$changes, &$tableChanges, $stores,$provinces,$regions,$pieces,$job_titles) {
                    if (in_array($afkey, ['manager_name', 'indirect_manager_name', 'updated_at', 'uuid', 'created_at', 'id', 'sub_staffs','creater','position_category_name','job_title_name','manager_state','sys_store_name','sys_department_name','bank_type','email_suffix','payment_markup_name_arr','stop_payment_type_arr','department_level', 'job_title_grade', 'job_title_grade_v2'])) {
                        return false;
                    }
                    $afkeyLang = $this->lang->get($afkey);
                    $beforeValShow = $before[$afkey] ?? '';
                    $afterValShow = $afval;

                    if(in_array($afkey,["node_department_id","sys_department_id"])) {
                        if (!empty($beforeValShow)) {
                            $beforeValShow = Yii::$app->sysconfig->allDepeartments[$beforeValShow] ?? $beforeValShow;
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = Yii::$app->sysconfig->allDepeartments[$afterValShow] ?? $afterValShow;
                        }
                    }

                    if ($afkey == 'job_title') {
                        if (!empty($beforeValShow)) {
                            $beforeValShow = $job_titles[$beforeValShow] ?? $beforeValShow;
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = $job_titles[$afterValShow] ?? $afterValShow;
                        }
                    }

                    if ($afkey == 'position_category') {
                        if (!empty($beforeValShow)) {
                            $beforeValShow = array_map(function ($v) {
                                return $this->lang->get('role_' . $v);
                            }, $beforeValShow);
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = array_map(function ($v) {
                                return $this->lang->get('role_' . $v);
                            }, $afterValShow);
                        }
                    }

                    if ($afkey == 'sys_store_id') {
                        $beforeValShow = $stores[$beforeValShow]['name'] ?? $beforeValShow;
                        $afterValShow = $stores[$afterValShow]['name'] ?? $afterValShow;
                    }

                    //if (in_array($afkey, ['state', 'payment_state', 'sex', 'payment_markup'])) {
                    //    $beforeValShow = $this->lang->get($afkey . '_' . $beforeValShow);
                    //    $afterValShow = $this->lang->get($afkey . '_' . $afterValShow);
                    //}

                    if (in_array($afkey, ['state', 'payment_state', 'sex','formal'])) {
                        $beforeValShow = $this->lang->get($afkey . '_' . $beforeValShow)??$beforeValShow;
                        $afterValShow = $this->lang->get($afkey . '_' . $afterValShow)??$afterValShow;
                    }

                    if($afkey == 'payment_markup') {
                        $before_payment_markup_arr = array_filter(explode(',', $beforeValShow));
                        foreach ($before_payment_markup_arr as $k => $v) {
                            $beforeValShow .= Yii::$app->lang->get('payment_markup_' . $v).';' ?? '';
                        }

                        $after_payment_markup_arr = array_filter(explode(',', $afterValShow));
                        foreach ($after_payment_markup_arr as $k => $v) {
                            $afterValShow .= Yii::$app->lang->get('payment_markup_' . $v).';' ?? '';
                        }
                    }


                    if (in_array($afkey,['area_manager_strore','approval_jurisdictions_store']) ){
                        // var_export($afkey);die();
                        if (!empty($beforeValShow)) {
                            $beforeValShow = array_map(function ($v) use ($stores) {
                                return $stores[$v]['name'] ?? $v;
                            }, $beforeValShow);
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = array_map(function ($v) use ($stores) {

                                return $stores[$v]['name'] ?? $v;
                            }, $afterValShow);
                        }
                    }

                    if($afkey == 'job_title_level'){
                        if (!empty($beforeValShow)) {
                            $beforeValShow = Yii::$app->sysconfig->config_job_title_level[$beforeValShow] ?? $beforeValShow;
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = Yii::$app->sysconfig->config_job_title_level[$afterValShow] ?? $afterValShow;
                        }
                    }


                    if ($afkey == 'manage_area_name') {
                        if (!empty($beforeValShow)) {
                            $beforeValShow = array_map(function ($v) use ($provinces) {
                                return $provinces[$v] ?? $v;
                            }, $beforeValShow);
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = array_map(function ($v) use ($provinces) {
                                return $provinces[$v]?? $v;
                            }, $afterValShow);
                        }
                    }

                    if ($afkey == 'manage_region_and_piece') {
                        //第一个大区，第二个片区（有可能没有）

                        if (!empty($beforeValShow)) {
                            $temp = "";
                            if(!empty($beforeValShow[0])){
                                $temp= $regions[$beforeValShow[0]]??$beforeValShow[0];
                            }

                            if(!empty($beforeValShow[1])){
                                $temp .= ",".$pieces[$beforeValShow[1]]??$beforeValShow[1];
                            }
                            $beforeValShow = $temp;
                        }

                        if (!empty($afterValShow)) {
                            $temp = "";
                            if(!empty($afterValShow[0])){
                                $temp= $regions[$afterValShow[0]]??$afterValShow[0];
                            }

                            if(!empty($afterValShow[1])){
                                $temp .= ",".$pieces[$afterValShow[1]]??$afterValShow[1];
                            }
                            $afterValShow = $temp;
                        }
                    }


                    if(in_array($afkey, ["piece_id","approval_jurisdictions_piece"])){
                        if (!empty($beforeValShow)) {
                            if(is_array($beforeValShow)){
                                $beforeValShow = array_map(function ($v) use ($pieces) {
                                    return $pieces[$v] ?? $v;
                                }, $beforeValShow);
                            }else{
                                $beforeValShow = $pieces[$beforeValShow]??$beforeValShow;
                            }
                        }


                        if (!empty($afterValShow)) {
                            if(is_array($afterValShow)){
                                $afterValShow = array_map(function ($v) use ($pieces) {
                                    return $pieces[$v]?? $v;
                                }, $afterValShow);
                            }else{
                                $afterValShow = $pieces[$afterValShow]??$afterValShow;
                            }
                        }
                    }

                    if(in_array($afkey, ["region_id","approval_jurisdictions_region"])){
                        if (!empty($beforeValShow)) {
                            if(is_array($beforeValShow)){
                                $beforeValShow = array_map(function ($v) use ($regions) {
                                    return $regions[$v] ?? $v;
                                }, $beforeValShow);
                            }else{
                                $beforeValShow = $regions[$beforeValShow]??$beforeValShow;
                            }
                        }

                        if (!empty($afterValShow)) {
                            if(is_array($afterValShow)){
                                $afterValShow = array_map(function ($v) use ($regions) {
                                    return $regions[$v]?? $v;
                                }, $afterValShow);
                            }else{
                                $afterValShow = $regions[$afterValShow]??$afterValShow;
                            }
                        }
                    }


                    if ($afkey == 'criminal_types') {
                        $afters = $befores = [];
                        foreach ($afterValShow as $item) {
                            $afters[] = $item['record_type_text'];
                            $afters[] = $item['record_case_text'];
                            if ($item['record_text']) {
                                $afters[] = $item['record_text'];
                            }
                            $afters[] = $item['record_status_text'];
                            $afters[] = "/";
                        }
                        if (isset($before[$afkey])) foreach ($before[$afkey] as $item) {
                            $befores[] = $item['record_type_text'];
                            $befores[] = $item['record_case_text'];
                            if ($item['record_text']) {
                                $befores[] = $item['record_text'];
                            }
                            $befores[] = $item['record_status_text'];
                            $befores[] = "/";
                        }

                        $afterValShow = $afters;
                        $beforeValShow= $befores;
                    }

                    if ($afkey == 'inspection_certificate' || $afkey == 'company_inspection_certificate') {

                        return false;
                    }

                    //角色管辖范围部门
                    if ($afkey == 'approval_jurisdictions_department') {
                        if (!empty($beforeValShow)) {
                            if(is_array($afterValShow)) {
                                $departmentBeforeValShow = [];
                                foreach ($beforeValShow as $department_v) {
                                    $departmentBeforeValShow[] = (Yii::$app->sysconfig->allDepeartments[$department_v['id']] ?? $department_v['id']) . ($department_v['is_include_sub'] ?  ' -- (Include sub-dept)' :  '');
                                }
                                $beforeValShow = implode(',', $departmentBeforeValShow);
                            }
                        }


                        if (!empty($afterValShow)) {
                            if(is_array($afterValShow)){
                                $departmentAfterValShow = [];
                                foreach ($afterValShow as $department_v) {
                                    $departmentAfterValShow[] = (Yii::$app->sysconfig->allDepeartments[$department_v['id']] ?? $department_v['id']) . ($department_v['is_include_sub'] ?  ' -- (Include sub-dept)' :  '');
                                }
                                $afterValShow = implode(',', $departmentAfterValShow);
                            }
                        }
                    }



                    // $afkeyLang = $afkey;
                    if (!isset($before[$afkey])) {
                        if (is_array($afterValShow)) {
                            $changes[] = '[新增]: ' . $afkeyLang . ': ' . implode(' ', $afterValShow);
                            $tableChanges[$afkey] = implode(' ', $afterValShow);
                        } else if (!is_null($afval)) {
                            $changes[] = '[新增]: ' . $afkeyLang . ': ' . $afterValShow;
                            $tableChanges[$afkey] = $afterValShow;
                        }
                        return false;
                    }

                    if ($before[$afkey] != $afval) {

                        if(is_array($afterValShow)){
                            $afterValShow = implode(' ', $afterValShow);
                        }

                        if(is_array($beforeValShow)){
                            $beforeValShow = implode(' ', $beforeValShow);
                        }

                        $changes[] = '[更新]: ' . $afkeyLang . ': ' . $beforeValShow . ' => ' . $afterValShow;
                        $tableChanges[$afkey] = $beforeValShow . ' => ' . $afterValShow;
                    }
                }, ARRAY_FILTER_USE_BOTH);
                if (empty($changes)) {
                    continue;
                }
                $odep = '';
                $opos = '';
                $operater = StaffInfo::find()->where(['staff_info_id' => $obj['operater']])->one();
                if ($operater) {
                    $odep = Yii::$app->sysconfig->allDepeartments[$operater['sys_department_id']] ?? $operater['sys_department_id'];
                    $opos = Yii::$app->sysconfig->jobTitle[$operater['job_title']] ?? $operater['job_title'];
                }

                array_unshift($changes, '');
                array_unshift($changes, '类别: ' . $obj['type']);
                array_unshift($changes, '操作者部门: ' . ($odep ?? ''));
                array_unshift($changes, '操作者职位: ' . ($opos ?? ''));
                array_unshift($changes, '操作者: ' . ($obj['operater'] == -1 ? 'System Auto' : ($operater && isset($operater['name']) ? $operater['name'] : ($obj['operater'] == 10000 ? 10000 : "")))."，操作者工号: ".$obj['operater']) ;
                array_unshift($changes, '员工号: ' . $obj['staff_info_id']);
                //array_unshift($changes, '日  期: ' . $obj['created_at']);
                array_unshift($changes, '日  期: ' . date('Y-m-d H:i:s',strtotime($obj['created_at'])+TIME_ADD_HOUR*3600));

                //$tableChanges['created_at'] = $obj['created_at'];
                $tableChanges['created_at'] = date('Y-m-d H:i:s',strtotime($obj['created_at'])+TIME_ADD_HOUR*3600);
                $tableChanges['type'] = $obj['type'];
                $tableChanges['operater'] = $obj['operater'] == -1 ? 'System Auto' : $obj['operater'];
                $tableChanges['operater_job_title'] = $opos ?? '';
                $tableChanges['operater_department'] = $odep ?? '';
                $tableChanges['staff_info_id'] = $obj['staff_info_id'];

                $one['table_change'] = $tableChanges;
                $one['change'] = $changes;
                $one['before'] = $before;
                $one['after'] = $after;
                $response[] = $one;

                //if (count($response) > 10) {
                //    break;
                //}
            }
            return $this->succ($response);
        } catch (\Exception $e) {
            var_dump($e->getMessage().';行号:'.$e->getLine());die;
            Yii::$app->logger->write_log('flash operate-log 员工日志查询，可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
            return $this->succ([]);
        }
    }

    /**
     * Flash员工操作记录(薪资)
     * @return array
     */
    public function actionRoleOperateLog()
    {
        $operater = Yii::$app->request->get('operater');
        $staffInfoId = Yii::$app->request->get('staff_info_id');
        if (empty($operater) && empty($staffInfoId)) {
            return $this->err(['miss param']);
        }

        //$result = HrOperateLogs::find()
        //    ->filterWhere(['operater' => $operater, 'staff_info_id' => $staffInfoId, 'type' => 'salary'])
        //    ->orderBy(['id' => SORT_DESC])
        //    ->asArray();
        $where = " type='salary' ";
        if(!empty($operater)) {
            if(!preg_match("/^[0-9]*$/",$operater)) {
                return $this->err(['param error']);
            }
            $where .= ' and operater='.$operater;
        }
        if(!empty($staffInfoId)) {
            if(!preg_match("/^[0-9]*$/",$staffInfoId)) {
                return $this->err(['param error']);
            }
            $where .= ' and staff_info_id='.$staffInfoId;
        }

        try {
            $result = Yii::$app->get('r_backyard')->createCommand("SELECT * FROM hr_operate_logs  WHERE ".$where." ORDER BY id DESC  LIMIT 0,200 ")->queryAll();
            $stores = SysStoreTemp::temp();
            $response = [];
            foreach ($result as $key => $obj) {
                $before = json_decode($obj['before'], true)['body'] ?? [];
                if(!is_array($before)) {
                    $before = json_decode($before,true);
                }
                $after = json_decode($obj['after'], true)['body'] ?? [];
                if(!is_array($after)) {
                    $after = json_decode($after,true);
                }

                if (count($before) == 0 && count($after) == 0) {
                    continue;
                }
                $changes = [];
                $tableChanges = [];
                array_filter($after, function ($afval, $afkey) use ($before, &$changes, &$tableChanges, $stores) {
                    if (in_array($afkey, ['manager_name', 'indirect_manager_name', 'updated_at', 'uuid', 'created_at', 'id', 'sub_staffs'])) {
                        return false;
                    }
                    $afkeyLang = $this->lang->get($afkey);
                    $beforeValShow = $before[$afkey] ?? '';
                    $afterValShow = $afval;

                    if ($afkey == 'sys_department_id') {
                        if (!empty($beforeValShow)) {
                            $beforeValShow = Yii::$app->sysconfig->allDepeartments[$beforeValShow] ?? $beforeValShow;
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = Yii::$app->sysconfig->allDepeartments[$afterValShow] ?? $afterValShow;
                        }
                    }

                    if ($afkey == 'job_title') {
                        if (!empty($beforeValShow)) {
                            $beforeValShow = Yii::$app->sysconfig->jobTitle[$beforeValShow] ?? $beforeValShow;
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = Yii::$app->sysconfig->jobTitle[$afterValShow] ?? $afterValShow;
                        }
                    }

                    if ($afkey == 'position_category') {
                        if (!empty($beforeValShow)) {
                            $beforeValShow = array_map(function ($v) {
                                return $this->lang->get('role_' . $v);
                            }, $beforeValShow);
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = array_map(function ($v) {
                                return $this->lang->get('role_' . $v);
                            }, $afterValShow);
                        }
                    }

                    if ($afkey == 'sys_store_id') {
                        $beforeValShow = $stores[$beforeValShow]['name'] ?? $beforeValShow;
                        $afterValShow = $stores[$afterValShow]['name'] ?? $afterValShow;
                    }

                    if (in_array($afkey, ['state', 'payment_state', 'sex', 'payment_markup'])) {
                        $beforeValShow = $this->lang->get($afkey . '_' . $beforeValShow);
                        $afterValShow = $this->lang->get($afkey . '_' . $afterValShow);
                    }

                    if ($afkey == 'area_manager_strore') {
                        // var_export($afkey);die();
                        if (!empty($beforeValShow)) {
                            $beforeValShow = array_map(function ($v) use ($stores) {
                                return $stores[$v]['name'] ?? $v;
                            }, $beforeValShow);
                        }

                        if (!empty($afterValShow)) {
                            $afterValShow = array_map(function ($v) use ($stores) {

                                return $stores[$v]['name'] ?? $v;
                            }, $afterValShow);
                        }
                    }

                    // $afkeyLang = $afkey;
                    if (!isset($before[$afkey])) {
                        if (is_array($afval)) {
                            $changes[] = '[新增]: ' . $afkeyLang . ': ' . implode(' ', $afterValShow);
                            $tableChanges[$afkey] = implode(' ', $afterValShow);
                        } else if (!empty($afval)) {
                            $changes[] = '[新增]: ' . $afkeyLang . ': ' . $afterValShow;
                            $tableChanges[$afkey] = $afterValShow;
                        }
                        return false;
                    }

                    if ($before[$afkey] != $afval) {
                        if (is_array($afval)) {
                            $changes[] = '[更新]: ' . $afkeyLang . ': ' . implode(' ', $beforeValShow) . ' => ' . implode(' ', $afterValShow);
                            $tableChanges[$afkey] = implode(' ', $beforeValShow) . ' => ' . implode(' ', $afterValShow);
                        } else {
                            $changes[] = '[更新]: ' . $afkeyLang . ': ' . $beforeValShow . ' => ' . $afterValShow;
                            $tableChanges[$afkey] = $beforeValShow . ' => ' . $afterValShow;
                        }
                    }
                }, ARRAY_FILTER_USE_BOTH);
                if (empty($changes)) {
                    continue;
                }

                $operater = StaffInfo::find()->where(['staff_info_id' => $obj['operater']])->one();
                if ($operater) {
                    $odep = Yii::$app->sysconfig->allDepeartments[$operater['sys_department_id']] ?? $operater['sys_department_id'];
                    $opos = Yii::$app->sysconfig->jobTitle[$operater['job_title']] ?? $operater['job_title'];
                }

                array_unshift($changes, '');
                array_unshift($changes, '类别: ' . $obj['type']);
                array_unshift($changes, '操作者部门: ' . ($odep ?? ''));
                array_unshift($changes, '操作者职位: ' . ($opos ?? ''));
                array_unshift($changes, '操作者: ' . ($obj['operater'] == -1 ? 'System Auto' : $obj['operater']));
                array_unshift($changes, '员工号: ' . $obj['staff_info_id']);
                //array_unshift($changes, '日  期: ' . $obj['created_at']);
                array_unshift($changes, '日  期: ' . date('Y-m-d H:i:s',strtotime($obj['created_at'])+TIME_ADD_HOUR*3600));

                //$tableChanges['created_at'] = $obj['created_at'];
                $tableChanges['created_at'] = date('Y-m-d H:i:s',strtotime($obj['created_at'])+TIME_ADD_HOUR*3600);
                $tableChanges['type'] = $obj['type'];
                $tableChanges['operater'] = $obj['operater'] == -1 ? 'System Auto' : $obj['operater'];
                $tableChanges['operater_job_title'] = $opos ?? '';
                $tableChanges['operater_department'] = $odep ?? '';
                $tableChanges['staff_info_id'] = $obj['staff_info_id'];

                $one['table_change'] = $tableChanges;
                $one['change'] = $changes;
                $one['before'] = $before;
                $one['after'] = $after;
                $response[] = $one;
                if (count($response) > 10) {
                    break;
                }
            }
            return $this->succ($response);
        } catch (\Exception $e) {
            return $this->succ([]);
            Yii::$app->logger->write_log('flash salary 员工日志查询，可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
        }
    }

    public function actionSalary()
    {
        $items = StaffSalary::salary($this->fbid);
        return $this->succ($items);
    }

    // 稀哲需求 https://shimo.im/docs/gY9TGTRR8jCtq89d/read
    public function actionStaffMenus()
    {
        try {
            $project = Yii::$app->request->get('project'); //project
            //$staff_relations_business = new StaffRelations();

            $result = [];//$staff_relations_business->getRelationsList($project);
            return $this->succ($result);
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('departments，可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
            return $this->succ([]);
        }
    }

    /**
     * staffs/instructor-lis
     * @return array
     */
    //获取指定网点员工列表 辅导员
    public function actionInstructorList()
    {
        $store_id      = Yii::$app->request->get('store_id');     //网点id
        $staff_info_id = Yii::$app->request->get('staff_info_id');//员工id
        $search_name   = Yii::$app->request->get('search_name');  //员工id
        if (empty($store_id) || empty($staff_info_id) || empty($search_name)) {
            return $this->succ([]);
        }
        $staff_list = InstructorService::getInstance()->list($staff_info_id, $store_id, $search_name);
        return $this->succ($staff_list);
    }

    //批量导入部门职位网点角色
    public function actionImportStaffInfo() {
        return $this->err(['该接口不用了']);
//        try {
//
//            set_time_limit(0);
//
//            $result = Staff::ImportStaffInfo($_FILES, $this->fbid);
//
//            if($result['code'] == 1) {
//                return $this->succ($result['data']);
//            } else {
//                return $this->err([$result['msg']]);
//            }
//        } catch (\Exception $e) {
//            Yii::$app->logger->write_log('actionImportStaffDeptJobTitleStore，可能出现的问题：' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE).';行号:'.$e->getLine());
//            return $this->err(['error']);
//        }
    }

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        if (isset($action->id) && in_array($action->id, ['create'])) {
            $staffInfoId = Yii::$app->request->post('staff_info_id');
            $this->operateBeferContent = $this->actionView($staffInfoId);
        }

        return true;
    }

    public function afterAction($action, $result)
    {
        $result = parent::afterAction($action, $result);
        //if (isset($action->id) && in_array($action->id, ['create'])) {
        //    $staffInfoId = $this->newStaffInfoId ?? Yii::$app->request->post('staff_info_id');
        //    $after = $this->actionView($staffInfoId);
        //    $log = new HrOperateLogs();
        //    $log->operater = $this->fbid;
        //    $log->request_body = json_encode(Yii::$app->request->post());
        //    $log->before = json_encode($this->operateBeferContent);
        //    $log->after = json_encode($after);
        //    $log->type = 'staff';
        //    $log->staff_info_id = $after['body']['staff_info_id'] ?? 0;
        //    $r = $log->save();
        //    if (!$r) {
        //        Yii::error($log->getErrors());
        //    }
        //}
        return $result;
    }


    /**
     * @description: 获取当前用户管辖范围下的网点
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/8/30 11:38
     */

    public function actionStaffUserStores(){
        try {
            $staff_id = Yii::$app->request->get('staff_id');//网点名称
            $result =  StaffRelationsService::getInstance()->getStaffStores($staff_id,StaffRelationsService::TYPE_STAFF_MANAGEMENT);
            $result = array_column($result,null,'id');
            if(isset($result['-2'])){
                $result['-2'] = [
                    'id'=>'-2',
                    'name'=>'ALL',
                    'short_name'=>'ALL',
                    'category'=>0,
                ];
            }
            return $this->succ(array_values($result));
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('actionStaffUserStores，可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
            return $this->succ([]);
        }
    }

    /**
     * @description: 获取用户管辖部门--管辖部门选择页面使用
     * <AUTHOR> L.J
     * @time       : 2022/10/9 10:02
     */
    public function actionGetStaffRelationsDepartment(){
        $staff_id = Yii::$app->request->get('staff_info_id');//用户 id
        $type = Yii::$app->request->get('jurisdiction_type',1);//管辖类型 1 是用户管辖范围 2 是白名单管辖范围
        $result = StaffRelationsService::getInstance()->getStaffRelationsDepartment($staff_id,$type);
        return $this->succ(array_values($result));
    }

    //合作商列表导出
    public function actionExportPartner()
    {
        $param   = Yii::$app->request->get();
        $headers = Yii::$app->getRequest()->getHeaders();
        try {
            $param['user_departmentId'] = Yii::$app->user->identity->departmentId;
            $param['user_fbi_role']     = Yii::$app->user->identity->getPermission()['fbi.role'];
            $param['user_staff_id']     = $this->fbid;
            $param['header_from']       = $headers->get('From');
            $args['file_name']          = 'Cooperators_Staff_Management_' . date('YmdHms') . '.csv';
            $args['data']               = $param;
            $hcmExcelTaskExists         = HcmExcelTask::find()->where(['staff_info_id' => $this->fbid])->andWhere(['action_name' => 'staff-export/excel-partner'])->andWhere(['status' => 0])->andWhere(['is_delete' => 0])->exists();
            if ($hcmExcelTaskExists) {
                return $this->succ($this->lang->get('download_001'));
            }
            $file_name                     = Yii::$app->params['csv_file_name_prefix'] . $args['file_name'];
            $action_name                   = 'staff-export/excel-partner';
            $hcmExcelTask                  = new HcmExcelTask();
            $hcmExcelTask->staff_info_id   = (string)$this->fbid;
            $hcmExcelTask->file_name       = $file_name;//文件名前缀 $args['file_name'];
            $hcmExcelTask->executable_path = Yii::$app->basePath . '/yii';
            $hcmExcelTask->action_name     = $action_name;
            $hcmExcelTask->created_at      = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
            $hcmExcelTask->args_json       = json_encode($args);
            $hcmExcelTaskResult            = $hcmExcelTask->save();
            if ($hcmExcelTaskResult) {
                //是否切换到hcm
                if ($param['project'] == 'fbi' || $param['header_from'] == 'fbi') {
                    $export_manager_model              = new ExportManage();
                    $export_manager_model->staff_id    = $this->fbid;
                    $export_manager_model->file_name   = $file_name;
                    $export_manager_model->file_path   = '';
                    $export_manager_model->state       = ExportManage::STATUS_WAITING;
                    $export_manager_model->module_code = $action_name . '_' . $hcmExcelTask->id;
                    $export_manager_model->save();
                }
                return $this->succ($this->lang->get('download_001'));
            } else {
                return $this->err(['error']);
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('flash 合作商员工下载，可能出现的问题：' . json_encode($e->getMessage(),
                    JSON_UNESCAPED_UNICODE) . ';行号:' . $e->getLine());
            return $this->err(['error']);
        }
    }

    /**
     * 检查企业邮箱（自动生成）
     * @return array
     */
    public function actionCheckCompanyEmail()
    {
        $param   = Yii::$app->request->post();
        $result  = StaffService::getInstance()->checkCompanyEmail($param);
        return $this->succ($result);
    }


}
