<?php

namespace app\modules\v1\controllers;

use app\controllers\RestFulController;
use app\models\fle\SysDepartment;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HrEmails;
use app\models\manage\HrOperateLogs;
use app\models\manage\StaffInfo;
use app\modules\v1\business\Staff;
use Yii;
use yii\rest\Controller;

/**
 * Default controller for the `v1` module
 */
class StaffEmailController extends RestFulController
{
    public $modelClass = 'app\models\manager\HrEmail';
    public function actionIndex()
    {
        $params = Yii::$app->request->get();
        try {
            $query = HrEmails::find()->innerJoin('hr_staff_info', 'hr_emails.staff_info_id=hr_staff_info.staff_info_id');
            if (!empty($params['staff_name'])) {
                $query->andFilterWhere(
                    ['OR',
                        ['LIKE', 'hr_staff_info.staff_info_id', $params['staff_name']],
                        ['LIKE', 'hr_staff_info.name', $params['staff_name']],
                        ['LIKE', 'hr_staff_info.emp_id', $params['staff_name']],
                    ]
                );
            }

            if (!empty($params['staff_state'])) {
                $query->andFilterWhere(['hr_staff_info.state' => $params['staff_state']]);
            }
            /*
            if (!empty($params['sys_department_id'])) {
                $query->andFilterWhere(['hr_staff_info.sys_department_id' => $params['sys_department_id']]);
            }
            if (!empty($params['node_department_id'])) {
                $query->andFilterWhere(['hr_staff_info.node_department_id' => $params['node_department_id']]);
            }
            */
            /*
            if(!empty(Yii::$app->request->get('department_id'))) {
                $company_department_id = Yii::$app->request->get('department_id');
                $company_department_list = Yii::$app->sysconfig->company_department_list;
                $company_department = $company_department_list[$company_department_id] ?? [];
                if(!empty($company_department)) {
                    $whereDepartment = null;
                    if($company_department['company_id'] == $company_department_id) {
                        foreach ($company_department_list as $key => $value) {
                            if($value['ancestry'] == $company_department_id) {
                                $whereDepartment[] = $value['id'];
                            }
                        }
                        $query->andFilterWhere(['hr_staff_info.sys_department_id' => $whereDepartment]);
                    } else {
                        //判断一级部门
                        $ancestry_arr = explode('/',$company_department['ancestry_v2']);
                        if(count($ancestry_arr) == 2) {
                            $query->andFilterWhere(['hr_staff_info.sys_department_id' => $company_department_id]);
                        }
                        if(count($ancestry_arr) == 3) {
                            $query->andFilterWhere(['hr_staff_info.sys_department_id' => $ancestry_arr[1]]);
                            $query->andFilterWhere(['hr_staff_info.node_department_id' => $company_department_id]);
                        }
                    }
                }
            }
            */
            //新版查询
            $whereDepartment = Yii::$app->request->get('department_id');
            if(!empty($whereDepartment)) {
                $dept_detail = SysDepartment::find()
                    ->select(['id', 'name', 'ancestry', 'ancestry_v2', 'type', 'level', 'group_boss_id'])
                    ->where(['deleted' => 0])
                    ->andWhere(['id' => $whereDepartment])
                    ->asArray()
                    ->one();
                if(!empty($dept_detail)) {
                    $ancestry_v2 = $dept_detail['ancestry_v2'];

                    $dept_list_query = SysDepartment::find()->select(['id', 'name', 'ancestry', 'ancestry_v2', 'type', 'level', 'group_boss_id'])
                        ->where(['deleted' => 0])
                        ->andWhere(['LIKE', 'ancestry_v2', $ancestry_v2.'/%', false]);
                    if($dept_detail['type'] != 1) {
                        $dept_list_query->orWhere(['id' => $whereDepartment]);
                    }
                    $dept_list = $dept_list_query->asArray()->indexBy('id')->all();
                    $whereDepartment = array_column($dept_list,'id');

                    $query->andFilterWhere(['hr_staff_info.node_department_id' => $whereDepartment]);
                }
            }




            if (!empty($params['hire_date'])) {
                $query->andFilterWhere(['>=', 'hr_staff_info.hire_date', $params['hire_date'][0] ?? null]);
                $query->andFilterWhere(['<', 'hr_staff_info.hire_date', $params['hire_date'][1] ?? null]);
            }

            if (!empty($params['leave_date'])) {
                $query->andFilterWhere(['>=', 'hr_staff_info.leave_date', $params['leave_date'][0] ?? null]);
                $query->andFilterWhere(['<', 'hr_staff_info.leave_date', $params['leave_date'][1] ?? null]);
            }

            if (isset($params['email_state']) && $params['email_state'] !== '') {
                $params['email_state'] = (int) $params['email_state'];
                $query->andFilterWhere(['hr_emails.state' => $params['email_state'] ?? null]);
            }

            $page = $this->pageIndexAndNums();
            $data['page_count'] = (int) $query->count("*", Yii::$app->get('backyard'));
            $query->select('
        	hr_emails.state,
        	hr_emails.staff_info_id,
        	hr_staff_info.name,
        	hr_staff_info.state AS staff_state,
        	hr_emails.email,
        	hr_staff_info.leave_date,
        	hr_staff_info.hire_date,
            hr_staff_info.name_en,
            hr_staff_info.sys_department_id,
            hr_staff_info.node_department_id,
            hr_staff_info.job_title,
            hr_staff_info.stop_duties_date,
            hr_staff_info.formal
            ');

            $query->offset($page[0] * $page[1]);

            //在职时间排序
            if(!empty($params['hire_date_store'])) {
                if($params['hire_date_store'] == 0) {
                    $query->orderBy(['hr_staff_info.hire_date' => SORT_ASC]);
                } else {
                    $query->orderBy(['hr_staff_info.hire_date' => SORT_DESC]);
                }

            }
            //离职时间排序
            if(!empty($params['leave_date_store'])) {
                if($params['leave_date_store'] == 0) {
                    $query->orderBy(['hr_staff_info.leave_date' => SORT_ASC]);
                } else {
                    $query->orderBy(['hr_staff_info.leave_date' => SORT_DESC]);
                }

            }
            //停职时间排序
            if(!empty($params['stop_date_store'])) {
                if($params['stop_date_store'] == 0) {
                    $query->orderBy(['hr_staff_info.stop_duties_date' => SORT_ASC]);
                } else {
                    $query->orderBy(['hr_staff_info.stop_duties_date' => SORT_DESC]);
                }
            }

            $data['rows'] = $query->limit($page[1])->asArray()->all(Yii::$app->get('backyard'));
            $data['rows'] = array_map(function (&$row) {
                $row['sys_department_name'] = Yii::$app->sysconfig->allDepeartments[$row['sys_department_id']] ?? '';
                $row['node_department_name'] = Yii::$app->sysconfig->allDepeartments[$row['node_department_id']] ?? '';
                $row['job_title_name'] = Yii::$app->sysconfig->jobTitle[$row['job_title']] ?? '';
                return $row;
            }, $data['rows']);
            return $this->succ($data);
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('Email List异常，可能出现的问题：' . $e->getMessage() .';行号:'.$e->getLine(), 'info');
            return $this->err(['exception']);
        }
    }

    //0 待开启 1 已开启 2 已注销 3 待注销
    public function actionOnOff()
    {

        $action = Yii::$app->request->post('action');
        $staffInfoIds = Yii::$app->request->post('staff_info_ids');
        if (empty($action) || !in_array($action, ['on', 'off']) || empty($staffInfoIds)) {
            return $this->err(['miss param']);
        }
        try {
            foreach (HrEmails::find()->where(['staff_info_id' => $staffInfoIds])->all() as $email) {
                if ($action == 'on' && $email->state == 0) {
                    $email->state = 1;
                } else if ($action == 'off' && $email->state == 3) {
                    if (StaffInfo::find()->where(['staff_info_id' => $email->staff_info_id, 'state' => [1, 3]])->exists(Yii::$app->get('backyard'))) {
                        continue;
                    }
                    $email->state = 2;
                } else {
                    continue;
                }
                if ($email->save() && $action == 'on') {
                    $staff = StaffInfo::find()->select(['name', 'mobile'])->where(['staff_info_id' => $email->staff_info_id])->one();
                    $msg = "ยินดีต้อนรับพนักงาน {$email->staff_info_id}({$staff->name}) มาเป็นส่วนหนึ่งของครอบครัว FlashExpress Email ของคุณคือ {$email->email} รหัสเริ่มต้นคือ：Flash123  ขอให้เข้าสู่ระบบเพื่อเปิดใช้งานผ่านทางลิ้งค์ : <a href='flashbackyard://fe/browser?url= https://en.exmail.qq.com/'> https://en.exmail.qq.com/ </a> （กรุณาเปิดด้วยเบราว์เซอร์คอมพิวเตอร์）";

                    // https://l8bx01gcjr.feishu.cn/docs/doccnOD9KSHnlHgMrcO2qbaOSQd
                    if (YII_COUNTRY == 'TH') {
                        $shortMsg = "ยินดีต้อนรับพนักงาน {$email->staff_info_id}({$staff->name}) มาเป็นส่วนหนึ่งของครอบครัว FlashExpress Email ของคุณคือ {$email->email} รหัสเริ่มต้นคือ：Flash123  ขอให้เข้าสู่ระบบเพื่อเปิดใช้งานผ่านทางลิ้งค์ :  https://en.exmail.qq.com/ （กรุณาเปิดด้วยเบราว์เซอร์คอมพิวเตอร์）";
                    } else {
                        $shortMsg = "Welcome employee {$email->staff_info_id}({$staff->name}) to become a member of the FlashExpress family. Your email is {$email->email}. The default code is: Flash123 Please log in to activate via the link: https://en.exmail.qq.com/ (Please open with a computer browser)";
                    }

                    if (!empty($staff->mobile) && !isCountry('PH')) {
                        Yii::$app->jrpc->smsSend($staff->mobile, $shortMsg, 'staff_email');
                    }
                    Yii::$app->jrpc->backYardMessage($email->staff_info_id, $msg);
                }
            }
            return $this->succ();
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('Email OnOff异常，可能出现的问题：' . $e->getMessage() .';行号:'.$e->getLine(), 'info');
            return $this->err(['exception']);
        }
    }

    //邮箱导出
    public function actionEmailExport() {
        $params = Yii::$app->request->get();

        try {
            $query = HrEmails::find()->innerJoin('hr_staff_info', 'hr_emails.staff_info_id=hr_staff_info.staff_info_id');
            if (!empty($params['staff_name'])) {
                $query->andFilterWhere(
                    ['OR',
                        ['LIKE', 'hr_staff_info.staff_info_id', $params['staff_name']],
                        ['LIKE', 'hr_staff_info.name', $params['staff_name']],
                        ['LIKE', 'hr_staff_info.emp_id', $params['staff_name']],
                    ]
                );
            }

            if (!empty($params['staff_state'])) {
                $query->andFilterWhere(['hr_staff_info.state' => $params['staff_state']]);
            }

            if (!empty($params['sys_department_id'])) {
                $query->andFilterWhere(['hr_staff_info.sys_department_id' => $params['sys_department_id']]);
            }

            if (!empty($params['node_department_id'])) {
                $query->andFilterWhere(['hr_staff_info.node_department_id' => $params['node_department_id']]);
            }

            if (!empty($params['hire_date'])) {
                $hire_date = explode(',',$params['hire_date']);
                $query->andFilterWhere(['>=', 'hr_staff_info.hire_date', $hire_date[0] ?? null]);
                $query->andFilterWhere(['<', 'hr_staff_info.hire_date', $hire_date[1] ?? null]);
            }

            if (!empty($params['leave_date'])) {
                $leave_date = explode(',',$params['leave_date']);
                $query->andFilterWhere(['>=', 'hr_staff_info.leave_date', $leave_date[0] ?? null]);
                $query->andFilterWhere(['<', 'hr_staff_info.leave_date', $leave_date[1] ?? null]);
            }

            if (isset($params['email_state']) && $params['email_state'] !== '') {
                $params['email_state'] = (int) $params['email_state'];
                $query->andFilterWhere(['hr_emails.state' => $params['email_state'] ?? null]);
            }

            $query->select('
        	hr_emails.state,
        	hr_emails.staff_info_id,
        	hr_staff_info.name,
        	hr_staff_info.state AS staff_state,
        	hr_emails.email,
        	hr_staff_info.leave_date,
        	hr_staff_info.hire_date,
            hr_staff_info.name_en,
            hr_staff_info.sys_department_id,
            hr_staff_info.node_department_id,
            hr_staff_info.job_title,
            hr_staff_info.stop_duties_date,
            hr_staff_info.formal
            ');

            //在职时间排序
            if(!empty($params['hire_date_store'])) {
                if($params['hire_date_store'] == 0) {
                    $query->orderBy(['hr_staff_info.hire_date' => SORT_ASC]);
                } else {
                    $query->orderBy(['hr_staff_info.hire_date' => SORT_DESC]);
                }

            }
            //离职时间排序
            if(!empty($params['leave_date_store'])) {
                if($params['leave_date_store'] == 0) {
                    $query->orderBy(['hr_staff_info.leave_date' => SORT_ASC]);
                } else {
                    $query->orderBy(['hr_staff_info.leave_date' => SORT_DESC]);
                }

            }
            //停职时间排序
            if(!empty($params['stop_date_store'])) {
                if($params['stop_date_store'] == 0) {
                    $query->orderBy(['hr_staff_info.stop_duties_date' => SORT_ASC]);
                } else {
                    $query->orderBy(['hr_staff_info.stop_duties_date' => SORT_DESC]);
                }
            }

            $data['rows'] = $query->asArray()->all(Yii::$app->get('backyard'));
            $data['rows'] = array_map(function (&$row) {
                $row['sys_department_name'] = Yii::$app->sysconfig->allDepeartments[$row['sys_department_id']] ?? '';
                $row['node_department_name'] = Yii::$app->sysconfig->allDepeartments[$row['node_department_id']] ?? '';
                $row['job_title_name'] = Yii::$app->sysconfig->jobTitle[$row['job_title']] ?? '';
                return $row;
            }, $data['rows']);
            foreach ($data['rows'] as $key => $value) {
                $department_name = $value['sys_department_name'];//部门名称
                if(!empty($value['node_department_name'])) {
                    $department_name = $value['node_department_name'];//部门名称
                }

                $row[$this->lang->get('staff_info_id')] = $value['staff_info_id'];//工号
                $row[$this->lang->get('name')] = $value['name'];//姓名
                $row[$this->lang->get('name_en')] = $value['name_en'];//英文名
                $row[$this->lang->get('formal_label')] = $this->lang->get('formal_'.$value['formal']);//员工属性
                $row[$this->lang->get('department')] = $department_name;//部门名称
                $row[$this->lang->get('job_title')] = $value['job_title_name'];//职位
                $row[$this->lang->get('state')] = $this->lang->get('state_'.$value['staff_state']);//在职状态
                $row[$this->lang->get('hire_date')] = !empty($value['hire_date']) ? (date('Y-m-d', strtotime($value['hire_date']))) : '';//入职日期
                $row[$this->lang->get('leave_date')] = !empty($value['leave_date']) ? (date('Y-m-d', strtotime($value['leave_date']))) : '';//离职日期
                $row[$this->lang->get('stop_duties_date')] = !empty($value['stop_duties_date']) ? (date('Y-m-d', strtotime($value['stop_duties_date']))) : '';//离职日期
                $row[$this->lang->get('business_mailbox')] = $value['email'];//企业邮箱
                //$row[$this->lang->get('personal_email')] = $value['personal_email'];//个人邮箱
                $row[$this->lang->get('status')] = $this->lang->get('turned_'.$value['state']);//邮箱状态
                if (!isset($arr)) {
                    $arr[] = array_keys($row);
                }
                $arr[] = $row;
            }

            $file_name = 'Mailbox_Management'.date('YmdHms').'.csv';
            $upload_oss_result = Yii::$app->csv->filePut_v2($arr, $file_name);
            $result['path'] = $upload_oss_result['object_url'];
            return $this->succ($result);
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('EmailExport异常，可能出现的问题：' . $e->getMessage() .';行号:'.$e->getLine(), 'info');
            //return Yii::$app->csv->output([], $title);
            $result['path'] = '';
            return $this->succ($result);
        }
    }

    //可进行邮箱删除状态  0 待开启 2 已注销
    public function actionEmailDel() {

        $staff_emails = Yii::$app->request->post('staff_emails');
        $result = [];
        try {
            foreach ($staff_emails as $item => $value) {
                $email = HrEmails::find()->where(['staff_info_id' => $value['staff_info_id']])->andWhere(['email' => $value['email']])->one();
                if($email && (($email->state == 0 || $email->state == 2) || in_array(Yii::$app->getUser()->identity['id'] ?? 0,[10000, 21932, 30888]))) {
                    if(HrEmails::deleteAll(['staff_info_id' => $value['staff_info_id']])) {
                        $staff = BaseStaffInfo::find()->where(['staff_info_id' => $value['staff_info_id']])->one(Yii::$app->get('backyard'));
                        if (!$staff) {
                            $result[] = ['staff_info_id' => $value['staff_info_id'], 'status' => 0, 'msg' => 'staff_info_id Date Not Found'];
                            continue;
                        }
                        $staff->email = '';
                        $before = Staff::view($value['staff_info_id']);
                        if($staff->update(true, ['email'])) {
                            $op = new HrOperateLogs();
                            $after = Staff::view($value['staff_info_id']);
                            $op->operater = $this->fbid;
                            $op->type = 'staff';
                            $op->staff_info_id = $value['staff_info_id'];
                            $op->before = json_encode(['body' => $before]);
                            $op->after = json_encode(['body' => $after]);
                            $op->save();
                            $result[] = ['staff_info_id' => $value['staff_info_id'], 'status' => 1, 'msg' => 'ok',];
                        }
                    } else {
                        //删除失败
                        $result[] = ['staff_info_id' => $value['staff_info_id'], 'status' => 0, 'msg' => 'delete error'];
                    }
                }
            }
            return $this->succ($result);
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('EmailDel异常，可能出现的问题：' . $e->getMessage() .';行号:'.$e->getLine(), 'info');
            return $this->err(['exception']);
        }
    }
}
