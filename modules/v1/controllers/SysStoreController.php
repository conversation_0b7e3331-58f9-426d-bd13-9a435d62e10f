<?php

namespace app\modules\v1\controllers;

use app\models\fle\SysStore;
use app\models\manage\SysStoreTemp;
use app\services\base\SysStoreService;
use Yii;

class SysStoreController extends \app\controllers\RestFulController
{
    public function actionIndex()
    {
        return $this->succ(SysStore::find()->all());
    }

    //所有网点
    public function actionList() {
        $is_all_display = Yii::$app->request->get('is_all_display',1);

        return $this->succ(SysStoreTemp::storeList($is_all_display));
    }

    /**
     * 员工管理-模糊匹配网点
     * @return array
     */
    public function actionQueryList()
    {
        $query_name = Yii::$app->request->get('search_name', '');
        $is_all     = Yii::$app->request->get('is_all', 0);
        $page_size = Yii::$app->request->get('page_size', 20);

        $list = SysStoreService::getInstance()->getStoreList([
            'query_name' => $query_name, 'is_all' => $is_all, 'page_size' => $page_size
        ]);
        return $this->succ($list);
    }
}
