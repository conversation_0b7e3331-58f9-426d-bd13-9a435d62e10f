<?php

namespace app\modules\v1\business;

use app\libs\Enums\StoreCategoryEnums;
use app\models\backyard\Banklist;
use app\models\backyard\HrBlacklist;
use app\models\backyard\HrOutSourcingBlacklist;
use app\models\backyard\SettingEnv;
use app\models\fle\KaProfile;
use app\models\fle\StaffInfo as FleStaffInfo;
use app\models\fle\StoreDeliveryBarangayStaffInfo;
use app\models\fle\StoreReceivableBillDetail;
use app\models\fle\StoreRemittanceBill;
use app\models\fle\SysDistrict;
use app\models\fle\TicketDelivery;
use app\models\fle\TicketPickup;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HrEmails;
use app\models\manage\StaffInfo;
use app\models\manage\SysStoreTemp;
use app\models\oa\ReserveFundApply;
use app\modules\v1\config\SysConfig;
use app\services\base\SettingEnvService;
use Yii;

class StaffInfoCheck extends \yii\base\BaseObject
{
    //员工创建验证
    public static function staffCreateValidate($staff_param) {
        $param = self::combinationStaffInfo($staff_param);

        if(($res = self::rulesStaffInfo($param)) && $res !== true) {
            return $res;
        }

        //验证身份证号
        if(($res = self::validateIdentity($param['identity'])) && $res !== true) {
            return $res;
        }

        // 验证雇佣期间
        if (isset($staff_param['hire_type']) && isset($staff_param['hire_times'])
            && ($res = self::validateHireTimes($staff_param['hire_type'], $staff_param['hire_times']))
            && $res !== true
        ) {
            return $res;
        }

        //验证银行卡号
        if(($res = self::validateBankNo($param['bank_no'], $param['bank_type'])) && $res !== true) {
            return $res;
        }

        //验证手机号
        if(($res = self::validateMobile($param['mobile'])) && $res !== true) {
            return $res;
        }

        //验证邮箱
        if(($res = self::validateEmail($param['email'])) && $res !== true) {
            return $res;
        }

        //验证网点出纳
        if(($res = self::vaildatePositionManagerCashier($param['position_category'],$param['sys_store_id']))  && $res !== true) {
            return $res;
        }

        //部门职位角色验证
        if(($res = self::vaildateDepartementJobTitleRoleStore($param)) && $res !== true) {
            return $res;
        }

        //快递员车辆类型验证
        if(($res = self::vaildateStaffCarType($param)) && $res !== true) {
            return $res;
        }

        //直线上级验证
        if(($res = self::vaildateStaffManager($param)) && $res !== true) {
            return $res;
        }

        return true;
    }

    public static function validateHireTimes($hire_type, $hire_times) {
        if (in_array($hire_type, [StaffInfo::HIRE_TYPE_MONTHLY_SALARY, StaffInfo::HIRE_TYPE_UN_PAID, StaffInfo::HIRE_TYPE_PART_TIME_AGENT]) && ($hire_times > 12 || $hire_times <=0)) {
            return ["hire_times_years"];
        } else if (in_array($hire_type, [StaffInfo::HIRE_TYPE_DAILY_SALARY, StaffInfo::HIRE_TYPE_HOURLY_WAGE]) && ($hire_times > 365 || $hire_times <= 0)) {

            return ["hire_times_days"];
        }

        return true;
    }

    public static function rulesStaffInfo($staff_param) {
        //姓名
        if(empty($staff_param['name'])) {
            return ['submit_notice'];
        }

        //身份证号
        if(empty($staff_param['identity'])) {
            return ['identity', 'common_empty_error'];
        }

        //入职时间
        if(empty($staff_param['hire_date'])) {
            return ['hire_date', 'common_empty_error'];
        }

        //入职时间
        $current_date = date("Y-m-d");
        if(strtotime($current_date) > strtotime($staff_param['hire_date'])) {
            return ['hire_date_out_range'];
        }

        //手机号
        if(empty($staff_param['mobile'])) {
            return ['mobile', 'common_empty_error'];
        }
        //手机号
        if(YII_COUNTRY == 'ID') {
            if (!preg_match('/(^[0-9]{8,13}$)/', $staff_param['mobile'])) {

                return ['mobile_exception_2'];
            }
        }else if(!preg_match("/^[0-9]{10,11}$/",$staff_param['mobile'])) {
            return ['staff_mobile_length_error'];
        }

        //个人邮箱
        if(!empty($staff_param['personal_email'])) {

            if(!filter_var($staff_param['personal_email'],FILTER_VALIDATE_EMAIL)) {
                return ['personal_email','common_exception'];
            }
        }

        //企业邮箱
        if(!empty($staff_param['email'])) {
            if(!filter_var($staff_param['email'],FILTER_VALIDATE_EMAIL)) {
                return ['email','common_exception'];
            }
        }

        //部门
        if(empty($staff_param['sys_department_id'])) {
            return ['sys_department_id', 'common_empty_error'];
        }
        //职位
        if(empty($staff_param['job_title'])) {
            return ['job_title', 'common_empty_error'];
        }

        /*
        //职等
        if(YII_COUNTRY == 'TH' && empty($staff_param['job_title_level'])) {
            return ['job_title_level', 'common_empty_error'];
        }

        //职级
        if(!is_numeric($staff_param['job_title_grade'])) {
            return ['job_title_grade', 'common_empty_error'];
        }
        */

        //所属网点
        if(empty($staff_param['sys_store_id'])) {
            return ['sys_store_id', 'common_empty_error'];
        }

        //直线上级
        if (empty($staff_param['manager'])) {
            return ['imm_supervisor', 'common_empty_error'];
        }

        //健康状况
        if(empty($staff_param['health_status'])) {
            return ['health_status', 'common_empty_error'];
        }

        //残疾证号
        if($staff_param['health_status'] == 2 && empty($staff_param['disability_certificate'])) {
            return ['disability_certificate', 'common_empty_error'];
        }

        //工作天数
        if(empty($staff_param['week_working_day'])) {
            return ['week_working_day' ,'common_empty_error'];
        }

        return true;
    }

    //格式化员工数据
    public static function combinationStaffInfo($staff_param) {
        $attr = [
                'staff_info_id',//工号
                'name',//姓名
                'sex',//性别
                'identity',//身份证号
                'mobile',//手机号
                'email',//企业邮箱
                'personal_email',//个人邮箱
                'bank_type',//银行卡类型
                'bank_no_name',//持卡人姓名
                'bank_no',//银行卡号
                'staff_car_type',//车辆类型
                'staff_car_no',//车牌号
                'vehicle_use_date',//车辆开始使用时间
                'vehicle_source',//车辆来源
                'personal_email',//个人邮箱
                'health_status',//健康状况
                'disability_certificate',//残疾证号
                'manage_area_name',//区域经理管辖区域
                'formal',//员工属性
                'state',//在职状态
                'sys_department_id',//部门id
                'node_department_id',//子部门
                'sys_store_id',//网点id
                'job_title',//职位id
                'position_category',//角色
                'email_suffix',//企业邮箱后缀类型
                'manager',//直线上级
                'indirect_manager',//虚线上级
                'week_working_day',//每周工作天数
                'job_title_grade',//职级
                'job_title_level',//职等
                'hire_date',//入职时间
            ];
        foreach ($attr as $key => $value) {
            if(!isset($staff_param[$value])) {
                $staff_param[$value] = null;
            }
        }
        return $staff_param;
    }

    //验证身份证号 在职验证 验证该身份证号是否被开除
    public static function validateIdentity($identity)
    {
        $where = ['identity' => $identity, 'state' => [BaseStaffInfo::STATE_ON_JOB, BaseStaffInfo::STATE_SUSPENSION], 'is_sub_staff' => BaseStaffInfo::IS_SUB_STAFF_NO];
        $create_resume_verify = SettingEnvService::getInstance()->getSetVal('create_resume_verify');
        if(!empty($create_resume_verify) && strtoupper($create_resume_verify) === 'N' && YII_COUNTRY == 'PH') {
            $where['formal'] = [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE];
        }

        if(BaseStaffInfo::find()->where($where)->exists()) {
            return ['identity', 'common_exists'];
        }

        //验证是否被开除
        if(BaseStaffInfo::find()->where(['identity' => $identity, 'leave_reason' => 2])->exists()) {
            return ['black_staff'];
        }
        return true;
    }

    //验证银行卡类型指定银行卡号长度
    public static function validateBankNoStingLen($bank_no, $bank_type,$tip_code = 'bank_no')
    {
        if(empty($bank_type)) {
            return ['bank_card_type',Yii::$app->lang->get('not_select')];
        }
        $bank_list = Banklist::find()->select([
            'bank_id',
            'bank_name',
            'max_length',
            'min_length',
        ])->where(['bank_id' => $bank_type])->orderBy(['sort_num' => SORT_DESC,'bank_id' => SORT_DESC])->one();
        if (empty($bank_list)){
            return [$tip_code,Yii::$app->lang->get('bank_no_err_4')];
        } elseif (empty($bank_no) || !preg_match("/^\d{1,30}$/", $bank_no)) {
            return [$tip_code,Yii::$app->lang->get('bank_no_err_1')];
        } else {
            $bank_no_len = strlen($bank_no);
            if (($bank_no_len < $bank_list->min_length || $bank_no_len > $bank_list->max_length) && $bank_list->min_length != $bank_list->max_length) {
                return [$tip_code,str_replace('%max_length%',$bank_list->max_length,str_replace('%min_length%',$bank_list->min_length,Yii::$app->lang->get('bank_no_err_3')))];
            } elseif (($bank_no_len < $bank_list->min_length || $bank_no_len > $bank_list->max_length) && $bank_list->min_length == $bank_list->max_length) {
                return [$tip_code,str_replace('%min_length%',$bank_list->min_length,Yii::$app->lang->get('bank_no_err_2'))];
            }
        }
        return true;
    }

    //验证银行卡号 在职验证 验证该银行卡好是否被开除
    //验证银行卡号，位数与对应类型是否匹配
    public static function validateBankNo($bank_no, $bank_type) {
        if (!empty($bank_no) && !empty($bank_type)){
            if(($res = self::validateBankNoStingLen($bank_no, $bank_type)) && $res !== true) {
                return $res;
            }
        }
        if(!empty($bank_no)) {
            if (StaffInfo::find()->where(['bank_no' => $bank_no, 'state' => [1, 3], 'is_sub_staff' => 0])->exists()) {
                return ['bank_no', 'common_exists'];
            }

            //验证是否被开除
            if(BaseStaffInfo::find()->where(['bank_no' => $bank_no, 'leave_reason' => 2])->exists()) {
                return ['black_staff'];
            }
        }
        return true;
    }

    //验证手机号是否存在
    public static function validateMobile($mobile)
    {
        $where = ['mobile' => $mobile, 'state' => [BaseStaffInfo::STATE_ON_JOB, BaseStaffInfo::STATE_SUSPENSION], 'is_sub_staff' => BaseStaffInfo::IS_SUB_STAFF_NO];
        $create_resume_verify = SettingEnvService::getInstance()->getSetVal('create_resume_verify');
        if(!empty($create_resume_verify) && strtoupper($create_resume_verify) === 'N' && YII_COUNTRY == 'PH') {
            $where['formal'] = [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE];
        }

        if (BaseStaffInfo::find()->where($where)->exists()) {
            return ['mobile_existed'];
        } else{
            if(YII_COUNTRY == 'ID') {
                if (!preg_match('/(^[0-9]{8,13}$)/', $mobile)) {

                    return ['mobile_exception_2'];
                }
            }elseif (!preg_match('/(^[0-9]{10,11}$)/', $mobile)) {
                return ['staff_mobile_length_error'];
            } else {
                return true;
            }



        }
    }

    //验证邮箱是否已经存在
    public static function validateEmail($email) {
        if(!empty($email) && HrEmails::find()->where(['email' => $email])->andWhere(['!=','state',2])->exists()) {
            return ['mail_exists'];
        }
        return true;
    }

    //1 未完成的揽件任务，如有则报错：该员工有未完成的揽件任务,不能变更所属网点，请联系网点经理或主管
    //2 未完成的派件任务，如有则报错：该员工有未完成的派件任务不能变更所属网点，请联系网点经理或主管
    //3 未回款的快递员公款，如有则报错：该员工有未结清的公款，不能变更所属网点，请联系网点经理或主管
    //4 若员工绑定了客户，则提示：该员工被签约客户指定揽件，不能变更所属网点，请联系管理员
    //5 若员工绑定了片区，则提示：该员工是片区负责人，不能变更所属网点，请通知网点经理或主管重新设置片区负责人
    //9.当前员工有未向总部汇款项，不能变更所属网点
    public static function validateStoreByStaffInfoId($staff_info_id,$store_id) {
        $store = Yii::$app->sysconfig->getStore($store_id);

        if ($store['state'] != 1) {
            return ['store', 'state error'];
        }

        $staff_info = BaseStaffInfo::find()->where(['staff_info_id' => $staff_info_id])->asArray()->one();
        if($staff_info['sys_store_id'] != $store_id) {
            $validate_code = [
                'hris_validate_ticket_pickup', //验证是否有未完成的揽件任务 0不验证1验证
                'hris_validate_ticket_delivery', //验证是否有未完成的派件任务 0不验证1验证
                'hris_validate_store_receivable_bill_detail', //验证未回款的快递员公款 0不验证1验证
                'hris_validate_ka_profile', //验证员工绑定了ka客户0不验证 1验证
                'hris_validate_store_delivery_barangay_staff_info', //菲律宾/马来 验证员工已绑定派送码0不验证 1验证
                'hris_validate_sys_district', //非菲律宾/马来 该员工是片区负责人 0不验证 1验证
                'hris_validate_store_remittance_bill', //验证是否有未向总部汇款项 0不验证 1验证
                'hris_validate_reserve_fund_apply' //验证备用金是否归还 0不验证1验证
            ];
            $setting_env = SettingEnv::find()->where(['code' => $validate_code])->asArray()->indexBy('code')->all();
            $hris_validate_ticket_pickup = $setting_env['hris_validate_ticket_pickup']['set_val'] ?? 1;
            $hris_validate_ticket_delivery = $setting_env['hris_validate_ticket_delivery']['set_val'] ?? 1;
            $hris_validate_store_receivable_bill_detail = $setting_env['hris_validate_store_receivable_bill_detail']['set_val'] ?? 1;
            $hris_validate_ka_profile = $setting_env['hris_validate_ka_profile']['set_val'] ?? 1;
            $hris_validate_store_delivery_barangay_staff_info = $setting_env['hris_validate_store_delivery_barangay_staff_info']['set_val'] ?? 1;
            $hris_validate_sys_district = $setting_env['hris_validate_sys_district']['set_val'] ?? 1;
            $hris_validate_store_remittance_bill = $setting_env['hris_validate_store_remittance_bill']['set_val'] ?? 1;
            $hris_validate_reserve_fund_apply = $setting_env['hris_validate_reserve_fund_apply']['set_val'] ?? 1;


            // 1 未完成的揽件任务，如有则报错：该员工有未完成的揽件任务,不能变更所属网点，请联系网点经理
            if ($hris_validate_ticket_pickup == 1 && TicketPickup::find()->where(['staff_info_id' => $staff_info_id])->andWhere(['state' => 1])->exists()) {
                return ['store', 'store_change_tip1'];
            }

            // 2 未完成的派件任务，如有则报错：该员工有未完成的派件任务不能变更所属网点，请联系网点经理
            if ($hris_validate_ticket_delivery == 1 && TicketDelivery::find()->where(['staff_info_id' => $staff_info_id])->andWhere(['state' => 0])->exists()) {
                return ['store', 'store_change_tip2'];
            }

            // 3.未回款的快递员公款，如有则报错：该员工有未结清的公款，不能变更所属网点，请联系网点经理
            if ($hris_validate_store_receivable_bill_detail == 1 && StoreReceivableBillDetail::find()->where(['store_id' => $staff_info['sys_store_id']])->andWhere(['staff_info_id' => $staff_info_id])->andWhere(['state' => [0, 3]])->exists()) {
                return ['store', 'store_change_tip3'];
            }

            // 4.若员工绑定了客户，则提示：该员工被签约客户指定揽件，不能变更所属网点，请联系管理员
            if ($hris_validate_ka_profile == 1 && KaProfile::find()->where(['courier_id' => $staff_info_id])->exists()) {
                return ['store', 'store_change_tip4'];
            }

            // 5.该员工是片区负责人，不能变更所属网点，请通知网点经理重新设置片区负责人
            //if ($hris_validate_sys_district == 1 && SysDistrict::find()->where(['courier_id' => $staff_info_id, 'deleted' => 0])->exists()) {
            //    return ['store', 'store_change_tip5'];
            //}
            if(in_array(YII_COUNTRY, ['PH', 'MY'])) {
                //菲律宾马来 所属网点 该员工已绑定派送码，不能变更所属网点，请通知网点主管重新设置派送码设置及快递员绑定
                if($hris_validate_store_delivery_barangay_staff_info == 1 && StoreDeliveryBarangayStaffInfo::find()->where(['staff_info_id' => $staff_info_id, 'deleted' => 0])->exists()) {
                    return ['store', 'store_change_tip8'];
                }
            } else {
                // 该员工是片区负责人，不能变更所属网点，请通知网点经理重新设置片区负责人
                if ($hris_validate_sys_district == 1 && SysDistrict::find()->where(['courier_id' => $staff_info_id, 'deleted' => 0])->exists()) {
                    return ['store', 'store_change_tip5'];
                }
            }


            // 9.当前员工有未向总部汇款项，不能变更所属网点
            //select * from store_remittance_bill where store_id = '' AND collector_id = '' AND (parcel_state = 0 or cod_state = 0);
            //https://l8bx01gcjr.feishu.cn/docs/doccn5krVYyPEDKcH8UNDGOGk4d#vB5RNP
            if ($hris_validate_store_remittance_bill == 1 && StoreRemittanceBill::find()
                                   ->where(['store_id' => $staff_info['sys_store_id'], 'collector_id' => $staff_info_id ])
                                   ->andWhere(['OR',['parcel_state' => 0],['cod_state' => 0]])
                                   ->exists()) {
                return ['store', 'store_change_tip9'];
            }

            //验证是否有未归还备用金
            if($hris_validate_reserve_fund_apply == 1 && ReserveFundApply::find()->where(['create_id' => $staff_info_id])->andWhere(['return_status' => [1,2,4]])->andWhere(['create_store_id' => $staff_info['sys_store_id']])->exists()) {
                return ['store', 'store_change_tip10'];
            }


        }
        return true;
    }

    //网点经理 网点出纳 验证
    public static function vaildatePositionManagerCashier($positionCategory,$store_id) {
        $pos = array_intersect($positionCategory ?? [], [3, 4]);
        if (empty($pos)) {
            return true;
        }
        //获取网点信息
        $store = SysStoreTemp::temp()[$store_id] ?? '';
        $store_category = $store['category'] ?? '';

        $positionExists = FleStaffInfo::find()
                                    ->select(['COUNT(1) AS count', 'staff_info_position.position_category AS position_category', 'staff_info_position.staff_info_id'])
                                    ->innerJoin('staff_info_position', 'staff_info_position.staff_info_id = staff_info.id')
                                    ->where(['staff_info.organization_type' => 1])
                                    ->andWhere(['staff_info.organization_id' => $store_id])
                                    ->andWhere(['staff_info_position.position_category' => $pos])
                                    ->andWhere(['staff_info.state' => 1])
                                    ->groupBy('staff_info_position.position_category')
                                    ->asArray()
                                    ->all();

        $notice = self::checkStoreCashier($positionExists, $store_category, true);
        if (!empty($notice)) {
            return $notice;
        }

        // hris校验
        $positionExists = StaffInfo::find()
                                    ->select(['COUNT(1) AS count', 'hr_staff_info_position.position_category AS position_category'])
                                    ->innerJoin('hr_staff_info_position', 'hr_staff_info_position.staff_info_id = hr_staff_info.staff_info_id')
                                    ->where(['hr_staff_info.sys_store_id' => $store_id])
                                    ->andWhere(['hr_staff_info_position.position_category' => $pos])
                                    ->andWhere(['hr_staff_info.state' => 1])
                                    ->groupBy('hr_staff_info_position.position_category')
                                    ->asArray()
                                    ->all();

        $notice = self::checkStoreCashier($positionExists, $store_category, true);
        if (!empty($notice)) {
            return $notice;
        }

        return true;
    }

    /**
     * 校验网点出纳 & 网点经理
     * @param $positionExists
     * @param $storeCategory
     * @param bool $isResultArray 是否返回数组
     * @return mixed
     */
    public static function checkStoreCashier($positionExists, $storeCategory, bool $isResultArray = false)
    {
        foreach ($positionExists as $one) {
            //网点经理校验
            if ($one['count'] > 0 && $one['position_category'] == 3) {
                return $isResultArray ? ['store_ceo_role_limit'] : 'store_ceo_role_limit';
            }
            //网点出纳校验
            if ($one['position_category'] == 4) {
                if (in_array($storeCategory, [
                    StoreCategoryEnums::SP,
                    StoreCategoryEnums::DC,
                    StoreCategoryEnums::BDC,
                    StoreCategoryEnums::PDC,
                    StoreCategoryEnums::CDC,
                ])) {
                    if ($one['count'] > 3) {
                        //DC/SP/BDC/PDC/CDC网点，判断“网点出纳”最多：4名
                        return $isResultArray ? ['store_cashier_role_limit_4'] : 'store_cashier_role_limit_4';
                    }
                } elseif (in_array($storeCategory, [4, 5, 7])) {
                    if ($one['count'] > 1) {
                        //SHOP 网点，判断“网点出纳”最多：2名
                        return $isResultArray ? ['store_cashier_role_limit_2'] : 'store_cashier_role_limit_2';
                    }
                } elseif ($one['count'] > 0) {
                    //c.其他网点，判断不变，“网点出纳”最多：1名
                    return $isResultArray ? ['store_cashier_role_limit_1'] : 'store_cashier_role_limit_1';
                }
            }
        }
        return [];
    }

    //部门职位角色网点验证
    public static function vaildateDepartementJobTitleRoleStore($param) {
        $formal = $param['formal'];//员工属性
        $position_category = $param['position_category'];//角色
        $sys_department_id = $param['sys_department_id'];//部门id
        $node_department_id = $param['node_department_id'];//部门id
        $job_title = $param['job_title'];//职位
        $sys_store_id = $param['sys_store_id'];//网点id

        //正式 实习生
        if(in_array($formal, [1, 4])) {
            $departement_id = $node_department_id == 0 ? $sys_department_id : $node_department_id;
            // 部门-职位判断
            $departs = Yii::$app->sysconfig->relationDepartmentJobTitle();
            if (!isset($departs[$departement_id])) {
                return ['sys_department_id', 'Department Error'];
            } else if (!in_array($job_title, $departs[$departement_id])) {
                return ['job_title_config_error'];
            }

            $jobTitles = Yii::$app->sysconfig->relationJobTitlePosition();
            if(empty($position_category)) {
                if (isset($jobTitles[$departement_id . '_' . $job_title])) {
                    $allowPos = $jobTitles[$departement_id . '_' . $job_title];
                    if ($sys_store_id == -1) {
                        $count = array_intersect($allowPos, array_keys(Yii::$app->sysconfig->getHeaderOfficePosition()));
                    } else {
                        $count = array_intersect($allowPos, array_keys(Yii::$app->sysconfig->getStorePosition()));
                    }

                    if (count($count) > 0) {
                        return ['role', 'cannot be blank.'];
                    }
                }
            } else {
                if(isset($jobTitles[$departement_id . '_' . $job_title])) {
                    foreach ($position_category as $key => $val) {
                        if (!in_array($val, $jobTitles[$departement_id . '_' . $job_title])) {
                            Yii::$app->logger->write_log('vaildateDepartementJobTitleRoleStore : '.json_encode($param,true) , 'info');
                            return ['Configuration Role Error'];
                        }
                    }
                }
            }
        }

        //合作商 加盟商
        if(in_array($formal, [2, 3])) {
            foreach ($position_category as $pos) {
                if (!array_key_exists($pos, \Yii::$app->sysconfig->getOfficalPartnetPosition())) {
                    return ['role', 'common_exception'];
                }
            }
        }
        return true;
    }

    //如果是快递员验证车辆类型
    public static function vaildateStaffCarType($param) {
        $job_title = $param['job_title'];//职位
        $staff_car_type = $param['staff_car_type'];//车辆类型
        $hire_date = $param['hire_date'];//入职日期
        $state = $param['state'];//在职状态
        $vehicle_source = $param['vehicle_source'];//车辆来源
        $vehicle_use_date = $param['vehicle_use_date'];//车辆开始使用时间

        if (in_array($job_title,[13,110,1000])) { //van 或bike职位验证车辆类型
            if (empty($staff_car_type)) {
                return ['staff_car_type', 'common_empty_error'];
            }

            if (!in_array($staff_car_type, ['Bike', 'Van','Boat', 'Tricycle'])) {
                return ['staff_car_type', 'common_exception'];
            }
        }

        if($state == 1 && $staff_car_type == 'Van' && $vehicle_source == 2 && !isCountry('MY')) {
            if(strtotime($hire_date) > strtotime($vehicle_use_date)) {
                return ['vehicle_source_tip1'];
            }
        }
        return true;
    }

    //验证直线上级虚线上级
    public static function vaildateStaffManager($param) {
        $manager = $param['manager'];
        $indirect_manager = $param['indirect_manager'];

        if (!empty($indirect_manager) && !BaseStaffInfo::find()->where(['formal' => 1, 'staff_info_id' => $indirect_manager])->exists()) {
            return ['sub_boss', 'Not Found'];
        }
        return true;
    }

}