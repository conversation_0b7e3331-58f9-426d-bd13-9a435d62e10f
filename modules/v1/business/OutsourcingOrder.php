<?php

namespace app\modules\v1\business;

use app\libs\BusinessException;
use app\libs\DateHelper;
use app\models\backyard\AuditApply;
use app\models\backyard\HrShift;
use app\models\backyard\HrStaffOutsourcing;
use app\models\backyard\HrStaffShift;
use app\models\backyard\HrStaffShiftMiddleDate;
use app\models\fle\StoreReceivableBillDetail;
use app\models\fle\TicketDelivery;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HrOperateLogs;
use app\models\backyard\HrOutsourcingOrder;
use app\models\backyard\HrOutsourcingOrderDetail;
use app\models\manage\StaffInfo;
use app\models\manage\SysManagePiece;
use app\services\base\OtherService;
use app\modules\v1\config\SysConfig;
use app\services\base\OutSourcingOrderService;
use app\services\base\OutSourcingService;
//use phpDocumentor\Reflection\TypeResolver;
use Yii;

class OutsourcingOrder extends \yii\base\BaseObject
{

    //车辆类型
    public static $car_type = [
        13   => 'Bike',
        110  => 'Van',
        452  => 'Boat',
        98   => 'Bike',
        1194 => 'Truck',
        1199 => 'Car',
        1652 => 'Van',
    ];
    //职位角色对应
    public static $job_position = [
        13   => [1],
        110  => [1],
        452  => [1],
        111  => [2],
        271  => [2],
        1000 => [1],
        98   => [1, 2],
        807  => [2],
        1194 => [114],
        1199 => [1],
        1652 => [1],
    ];

    //根据审批单号创建外协订单
    public static function order_create($serial_no, $employment_days, $final_audit_num) {
        try {
            $model = HrStaffOutsourcing::find()->where(['serial_no' => $serial_no])->one();
            if(!$model) {
                Yii::$app->logger->write_log([
                    'function' => 'order_create',
                    'serial_no' => $serial_no,
                    'message' => '在by库 hr_staff_outsourcing 表未查到单号'
                ]);
                return ['code' => 500,'msg' => '未找到申请单号:'.$serial_no];
            }

            $model_order = HrOutsourcingOrder::find()->where(['serial_no' => $serial_no])->one();
            if($model_order) {
                Yii::$app->logger->write_log([
                    'function' => 'order_create',
                    'serial_no' => $serial_no,
                    'message' => '在by库 hr_outsourcing_order 表单号已存在'
                ]);
                return ['code' => 500, 'msg' => '工单号已存在'];
            }
            if (!empty($model->out_company_data)){
                $re = self::order_create_batch($model,$employment_days);
                return $re;
            }

            $model->final_audit_num = !empty($final_audit_num) ? $final_audit_num : $model->final_audit_num;
            $model->employment_days = !empty($employment_days) ? $employment_days : $model->employment_days;

            if(empty($model->employment_days) || empty($model->demend_num) || empty($model->final_audit_num)) {
                Yii::$app->logger->write_log([
                    'function' => 'order_create',
                    'serial_no' => $serial_no,
                    'message' => '支援天数/申请人数/最终审批人数有为空的数据',
                    'data' => json_encode($model->toArray())
                ]);
                return ['code' => 500, 'msg' => '支援天数/申请人数/最终审批人数有为空的数据'];
            }

            if(YII_COUNTRY == 'PH' && empty($model->shift_id)) {
                $shift_start = $model->shift_begin_time;
                $shift_end = $model->shift_end_time;
                $Effective_Invalid_Date = self::getHubEffective_Invalid_Date($model->employment_date,$model->employment_days,$shift_start,$shift_end);
            } else {
                $shift = Yii::$app->sysconfig->getShiftV2($model->shift_id);
                if($model->os_type == 1 || $model->os_type == 3 ) {
                    if(YII_COUNTRY == 'PH') {
                        $Effective_Invalid_Date = self::getEffective_Invalid_Date_ph($model->employment_date,$model->employment_days,$shift['start'],$shift['end']);
                    } else {
                        $Effective_Invalid_Date = self::getEffective_Invalid_Date($model->employment_date,$model->employment_days,$shift['start'],$shift['end']);
                    }
                } else {
                    $Effective_Invalid_Date = self::getEffective_Invalid_Date_by_longtime($model->employment_date,$model->employment_days,$shift['start'],$shift['end']);
                }
            }



            $order = new HrOutsourcingOrder();
            $current_time = gmdate('Y-m-d H:i:00',time()+TIME_ADD_HOUR*3600);
            if(strtotime($current_time) >= strtotime($Effective_Invalid_Date['effective_date'])) {
                $order->status = 4;
            }

            if(in_array(YII_COUNTRY,['PH', 'MY']) && strtotime($current_time) >= strtotime($Effective_Invalid_Date['effective_date'])) {
                $order->status = 2;
            }
            if(in_array(YII_COUNTRY,['PH', 'MY']) && strtotime($current_time) >= strtotime($Effective_Invalid_Date['invalid_date'])) {
                $order->status = 4;
            }
            $order->init_serial_no   = $model->serial_no;
            $order->serial_no        = $model->serial_no;
            $order->staff_info_id    = $model->staff_id;
            $order->job_id           = $model->job_id;
            $order->department_id    = $model->department_id;
            $order->store_id         = $model->store_id;
            $order->employment_date  = $model->employment_date;
            $order->employment_days  = $model->employment_days;
            $order->effective_date   = $Effective_Invalid_Date['effective_date'];
            $order->invalid_date     = $Effective_Invalid_Date['invalid_date'];
            $order->shift_id         = $model->shift_id;
            $order->demend_num       = $model->demend_num;
            $order->final_audit_num  = $model->final_audit_num;
            $order->os_type          = $model->os_type;
            $order->source_category  = $model->source_category;
            $order->hire_os_type     = $model->hire_os_type;
            $order->out_company_id   = $model->out_company_id;
            $order->need_remark      = $model->need_remark;     //需求备注
            $order->shift_begin_time = $model->shift_begin_time;//班次开始时间
            $order->shift_end_time   = $model->shift_end_time;  //班次结束时间
            $order->approve_create_time = $model->created_at;
            if(YII_COUNTRY == 'MY') {
                $order->work_partition = $model->work_partition;
            }
            if($order->save() === false) {
                Yii::$app->logger->write_log([
                    'function' => 'svc-order_create',
                    'message' => '创建外协工单失败',
                    'order_error' => json_encode($order->getErrors(), JSON_UNESCAPED_UNICODE)
                ]);
                return ['code' => 500,'msg' => '添加失败'];
            } else {
                //车队外协 同步数据给fleet
                if($model->os_type == 3 ) {
                    $store = Staff::getStoreInfo($model->store_id);
                    $param = [
                        'company_id' => 1,
                        'customer_id' => 1,
                        'staff_info_id' => $model->staff_id,
                        'source_no' => $model->serial_no,
                        'start_date' => date('Y-m-d',strtotime($Effective_Invalid_Date['effective_date'])),
                        'end_date' => date('Y-m-d',strtotime($Effective_Invalid_Date['invalid_date'])),
                        'clock_in' => $shift['start'],
                        'clock_out' => $shift['end'],
                        'num' => $model->final_audit_num,
                        'employment_days' => $model->employment_days,
                        'schedule' => [],
                        'site_address' => $store['detail_address'] ?? '',
                        'contact_person' => $store['manager_name'] ?? '',
                        'contact_phone' => $store['manager_phone'] ?? '',
                        'site_name' => $store['name'] ?? '',
                        'lng' => $store['lng'] ?? '',
                        'lat' => $store['lat'] ?? '',
                        'province' => $store['province_code'] ?? '',
                        'city' => $store['city_code'] ?? '',
                        'district' => $store['district_code'] ?? '',
                        'area' => $store['sorting_no'] ?? '',
                        'short_name' => $store['short_name'] ?? '',
                        'source_operator_id' => $model->staff_id
                    ];
                    Yii::$app->jrpc->fleetFlashDelivery($param);//同步订单信息
                }

                //如果有外协公司，则给外协发push
                if (!empty($model->out_company_id)) {
                    OutSourcingService::getInstance()->sendPushToOsmCreate($model->out_company_id);
                }

                return ['code' => 200, 'msg' => 'ok'];
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'order_create',
                'serial_no' => $serial_no,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return ['code' => 500, 'msg' => $e->getMessage()];
        }
    }

    // 根据一个申请可创建多个外协订单 新逻辑
    public static function order_create_batch($model,$employment_days) {

        $employment_days = !empty($employment_days) ? $employment_days : $model->employment_days;
        if(empty($employment_days)) {
            return ['code' => 500, 'msg' => '支援天数为空'];
        }


        $trans = Yii::$app->backyard_main->beginTransaction();
        try{
            $out_company_data_arr = json_decode($model->out_company_data, true);
            foreach ($out_company_data_arr as $k=>$v){
                $order = new HrOutsourcingOrder();
                $_out_company_id = $v["out_company_id"] ?? 0;
                $_demend_num = $v["demend_num"] ?? 0;
                $final_audit_num = !empty($v["final_audit_num"]) ? $v["final_audit_num"] : $_demend_num;
                if (empty($_out_company_id) || empty($_demend_num)){
                    $trans->rollback();
                    return ['code' => 500, 'msg' => 'out_company_id or demend_num is null'];
                }

                if (empty($final_audit_num)){
                    $trans->rollback();
                    return ['code' => 500, 'msg' => 'out_company_id or final_audit_num is null'];
                }

                //公司id 不为空，则为hub 外协订单
                $shift = Yii::$app->sysconfig->getShiftV2($model->shift_id);
                $shift_start = $shift['start'] ?? '';
                $shift_end = $shift['end'] ?? '';
                if(YII_COUNTRY == 'PH' && empty($model->shift_id)) {
                    $shift_start = $model->shift_begin_time;
                    $shift_end = $model->shift_end_time;
                }
                $Effective_Invalid_Date = self::getHubEffective_Invalid_Date($model->employment_date,$model->employment_days,$shift_start,$shift_end);

                $current_time = gmdate('Y-m-d H:i:00',time()+TIME_ADD_HOUR*3600);
                //21342【TH MY|BY HCM OSM】修改外协审批人数&修改已审批订单
                //MY 在班次开始时间都可以申请订单。但是 生效时间，还是维持半小时，但是不会直接置为取消了。
                if(strtotime($current_time) >= strtotime($Effective_Invalid_Date['effective_date']) && isCountry('TH')) {
                    $order->status = 4;
                }
                $order->serial_no        = $model->serial_no . '-' . $_out_company_id . rand(1000000, 9999999);
                $order->init_serial_no   = $model->serial_no;
                $order->staff_info_id    = $model->staff_id;
                $order->job_id           = $model->job_id ?? 0;
                $order->department_id    = $model->department_id;
                $order->store_id         = $model->store_id;
                $order->employment_date  = $model->employment_date;
                $order->employment_days  = $model->employment_days;
                $order->effective_date   = $Effective_Invalid_Date['effective_date'];
                $order->invalid_date     = $Effective_Invalid_Date['invalid_date'];
                $order->shift_id         = $model->shift_id;
                $order->demend_num       = $_demend_num ?? 0;
                $order->final_audit_num  = $final_audit_num ?? 0;
                $order->os_type          = $model->os_type ?? 0;
                $order->hire_os_type     = $model->hire_os_type ?? 0;
                $order->out_company_id   = $_out_company_id ?? 0;
                $order->need_remark      = $model->need_remark;     //需求备注
                $order->shift_begin_time = $model->shift_begin_time;//班次开始时间
                $order->shift_end_time   = $model->shift_end_time;  //班次结束时间
                $order->approve_create_time  = $model->created_at;
                if(YII_COUNTRY == 'MY') {
                    $order->work_partition = $model->work_partition;
                }
                if($order->save() === false) {
                    $trans->rollback();
                    Yii::$app->logger->write_log([
                        'function' => 'svc-order_create',
                        'message' => '批量创建外协工单失败',
                        'order_error' => json_encode($order->getErrors(), JSON_UNESCAPED_UNICODE)
                    ]);
                    return ['code' => 500,'msg' => '创建外协工单失败'];
                }
                //如果有外协公司，则给外协发push
                if (!empty($model->out_company_id)) {
                    OutSourcingService::getInstance()->sendPushToOsmCreate($model->out_company_id);
                }
            }
            $trans->commit();
            return ['code' => 200, 'msg' => 'ok'];
        }catch (\Exception $e) {
            $trans->rollback();
            Yii::$app->logger->write_log([
                'function' => 'order_create_batch',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return ['code' => 500, 'msg' => $e->getMessage()];
        }


    }

    //backyard 订单详情
    public static function backyard_view($serial_no,$lang) {
        $model = HrOutsourcingOrder::find()->where(['serial_no' => $serial_no])->asArray()->one();
        if(!$model) {
            return [];
        }
        //部门名称
        if(!empty($model['department_id'])) {
            $model['sys_department_name'] = Yii::$app->sysconfig->depeartment[$model['department_id']] ?? '';
        }
        //网点名称
        if(!empty($model['store_id'])) {
            $model['sys_store_name'] = $model['store_id'] == '-1' ? Yii::$app->lang->get('head_office') : Staff::getStoreInfo($model['store_id'])['name'];
        }
        //职位名称
        if(!empty($model['job_id'])) {
            $model['job_title_name'] = Yii::$app->sysconfig->jobTitle[$model['job_id']] ?? '';
        }
        $model['status_text'] = Yii::$app->lang->get('configure_'.$model['status'],'',$lang);
        //班次
        $model['staff_shift'] = Yii::$app->sysconfig->getShift($model['shift_id']);
        $order_detail = HrOutsourcingOrderDetail::find()->where(['serial_no' => $serial_no])->asArray()->all();
        $model['detail'] = $order_detail;
        return $model;
    }

    //订单详情
    public static function view($serial_no,$have_del = 0) {
        $query= HrOutsourcingOrder::find();
        $query->leftJoin('sys_store', 'hr_outsourcing_order.store_id = sys_store.id');
        $query->leftJoin('sys_manage_piece', 'sys_store.manage_piece = sys_manage_piece.id');
        $model = $query->where(['serial_no' => $serial_no])->select(["hr_outsourcing_order.*", "sys_manage_piece.name"])->asArray()->one();
        if(!$model) {
            return [];
        }
        //部门名称
        if(!empty($model['department_id'])) {
            $model['sys_department_name'] = Yii::$app->sysconfig->depeartment[$model['department_id']] ?? '';
        }
        //网点名称
        if(!empty($model['store_id'])) {
            $model['sys_store_name'] = $model['store_id'] == '-1' ? Yii::$app->lang->get('head_office') : Staff::getStoreInfo($model['store_id'])['name'];
        }
        //职位名称
        if(!empty($model['job_id'])) {
            $model['job_title_name'] = Yii::$app->sysconfig->jobTitle[$model['job_id']] ?? '';
        }
        //外协雇佣类型
        $model['hire_os_type_text'] = Yii::$app->lang->get(strtolower('hire_type_' . $model['hire_os_type'])) ?? '';

        $fle_staff_info = \app\models\fle\StaffInfo::find()->where(['id' => $model['staff_info_id']])->asArray()->one();
        $model['applicant_staff_name'] = '('.$model['staff_info_id'].')'.$fle_staff_info['name'];
        //班次
        if (!empty($model['shift_id'])) {
            $shift_detail              = Yii::$app->sysconfig->getShift($model['shift_id']);
            $model['staff_shift']      = $shift_detail;
            $model['staff_shift_text'] = $shift_detail['start'] . '-' . $shift_detail['end'];
        } else {
            $model['staff_shift']      = [
                'id'    => $model['shift_id'],
                'type'  => '',
                'start' => $model['shift_begin_time'],
                'end'   => $model['shift_end_time'],
            ];
            $model['staff_shift_text'] = $model['shift_begin_time'] . '-' . $model['shift_end_time'];
        }


        $model['company_name'] = '';
        if(!empty($model['out_company_id'])) {
            $companyInfo = OutSourcingService::getInstance()->getOsCompanyInfo($model['out_company_id']);
            $model['company_name'] = $companyInfo['company_name'];
        } else {
            $model['need_remark'] = '';
        }
        $outSourcingOrderService = OutSourcingOrderService::getInstance();
        $model['approve_create_time'] = DateHelper::show_time_zone($model['approve_create_time']);
        $model['created_at'] = DateHelper::show_time_zone($model['created_at']);
        $model['updated_at'] = DateHelper::show_time_zone($model['updated_at']);
        $outsourceCompanyList = Yii::$app->jrpc->getOsCompanyList();
        $outsourceCompanyListToId = !empty($outsourceCompanyList) ? array_column($outsourceCompanyList, 'label', 'value') : [];
        $orderDetailFind = HrOutsourcingOrderDetail::find()->where(['serial_no' => $serial_no]);
        if (!$have_del) {
            $orderDetailFind->andWhere(['is_del' => 0]);
        } else {
            $orderDetailFind->orderBy('is_del');
        }
        $order_detail = $orderDetailFind->asArray()->all();
        $configuredStaff = $outSourcingOrderService->getStaffInfos(array_column($order_detail,'create_staff_info_id'));
        foreach ($order_detail as $item => $value) {
            if($model['os_type'] == 2 && in_array($model['status'],[2 ,3])) {
                //如果是长期外协工单 并且 已生效 已结束 取班次表数据
                $staff_shift = HrStaffShift::find()->where(['staff_info_id' => $value['staff_info_id']])->asArray()->one();
                $order_detail[$item]['staff_shift'] = [];
                $order_detail[$item]['staff_shift_text'] = '';
                if ($staff_shift['shift_id']) {
                    $staff_shift_detail = Yii::$app->sysconfig->getShift($staff_shift['shift_id']);
                    $order_detail[$item]['staff_shift'] = $staff_shift_detail;
                    $order_detail[$item]['staff_shift_text'] = $staff_shift_detail['start'] . '~' . $staff_shift_detail['end'];
                }
            } else {
                $order_detail[$item]['staff_shift'] = $model['staff_shift'];
                $order_detail[$item]['staff_shift_text'] = $model['staff_shift_text'];
            }
            $order_detail[$item]['create_at'] = DateHelper::show_time_zone($order_detail[$item]['create_at']);
            $order_detail[$item]['configured_staff'] = isset($configuredStaff[$value['create_staff_info_id']]) ? "({$value['create_staff_info_id']}){$configuredStaff[$value['create_staff_info_id']]}" : '';
            $order_detail[$item]['updated_at'] = DateHelper::show_time_zone($order_detail[$item]['updated_at']);
            $order_detail[$item]['bank_id_text'] = $value['bank_id'] > 0 ? Yii::$app->sysconfig->all_bank_type()[$value['bank_id']] : '';
            $order_detail[$item]['sex_text'] = $value['sex'] == 'male' ? '1' : '2';//性别
            if(!empty($value['effective_state_message']) && !is_numeric($value['effective_state_message'])) {
                $message_result = json_decode($value['effective_state_message'], true);
                $msg = [];
                if(($message_result[0] ?? false)) {
                    $msg[] = Yii::$app->lang->get($message_result[0]);
                }
                if(($message_result[1] ?? false)) {
                    $msg[] = Yii::$app->lang->get($message_result[1]);
                }
                $order_detail[$item]['effective_state_message'] = implode(',',$msg);
            } else {
                $order_detail[$item]['effective_state_message'] = $value['effective_state_message'];
            }

            $order_detail[$item]['company_name_ef'] = isset($outsourceCompanyListToId[$value['company_item_id']]) ? $outsourceCompanyListToId[$value['company_item_id']] : $value['company_name_ef'];
            $order_detail[$item]['company_item_id'] = empty($value['company_item_id']) ? NULL : intval($value['company_item_id']);

        }
        $model['detail'] = $order_detail;
        return $model;
    }


    //保存信息
    public static function save($serial_no,$staffs,$outsourcing_model,$fbid) {

        $staff_info_model = StaffInfo::find()->where(['identity' => $staffs['identity']])->andWhere(['formal' => 0])->andWhere(['staff_type' => [2,3]])->one();
        $staff_info_id = 0;
        if($staff_info_model) {
            $staff_info_id = $staff_info_model->staff_info_id;
        }
        $detail_model = new HrOutsourcingOrderDetail();
        $detail_model->serial_no = $serial_no;//订单编号
        $detail_model->staff_info_id = $staff_info_id;//员工号
        $detail_model->identity = $staffs['identity'];//身份证号
        $detail_model->staff_name = $staffs['staff_name'];//员工姓名
        $detail_model->sex =  empty($staffs['sex']) ? 0 : $staffs['sex'];//性别
        $detail_model->mobile = $staffs['mobile'];//手机号
        $detail_model->outsourcing_type = $staffs['outsourcing_type'];//外协类型
        $detail_model->pay_type = $staffs['pay_type'];//结算类型
        $detail_model->company_name_ef = $staffs['company_name_ef'] ?? '';//公司名称
        $detail_model->company_item_id = empty($staffs['company_item_id']) ? 0 : $staffs['company_item_id'];//公司名称枚举id
        $detail_model->driver_license = $staffs['driver_license'];//驾驶证号
        $detail_model->bank_id = $staffs['bank_id'];//银行卡
        $detail_model->bank_no_name = $staffs['bank_no_name'];//持卡人姓名
        $detail_model->bank_no = $staffs['bank_no'];//银行卡号
        $detail_model->car_no = $staffs['car_no'];//车牌号
        $detail_model->effective_date = $outsourcing_model->effective_date;//生效时间
        $detail_model->invalid_date = $outsourcing_model->invalid_date;//失效时间
        $detail_model->is_complement  = $staffs['is_complement'];
        $detail_model->relatives_call_name = $staffs['relatives_call_name'];
        $detail_model->relatives_mobile = $staffs['relatives_mobile'];
        $detail_model->relatives_relationship = $staffs['relatives_relationship'];
        $detail_model->residential_address = $staffs['residential_address'];
        $detail_model->personal_email = $staffs['personal_email'];//个人邮箱
        $detail_model->create_staff_info_id = $fbid;
        if($staffs['is_complement'] == 1) {
            //补充人员生效时间，如果当前班次前一小时录入的则是当天生效 如果是之后录入的则第二天班次前一小时生效
            $detail_model->is_effective = 0;//补录人员初始状态是未生效
        }

        $shift = Yii::$app->sysconfig->getShift($outsourcing_model->shift_id);

        //生效时间计算
        $current_time = gmdate('Y-m-d H:i:00',time()+TIME_ADD_HOUR*3600);//当前时间，泰国时间
        $current_date = gmdate('Y-m-d',strtotime($current_time));
        $effective_time = gmdate('Y-m-d H:i:00',strtotime($current_date.$shift['start'])-3600*2);//当天班次生效时间；当前班次-1小时

        //最后一天班次开始前一小时 当前日期+(雇佣天数-1)
        $last_effective_time = gmdate('Y-m-d H:i:s',strtotime($outsourcing_model->employment_date.$shift['start']) + 3600 * 24 * ($outsourcing_model->employment_days - 1));
        if($current_time > $last_effective_time) {
            return ['error1'];
        }

        if($current_time < $effective_time) {
            //当天生效
            $detail_model->effective_date = $effective_time;//生效时间
        } else {
            //第二天班次前生效
            $detail_model->effective_date = gmdate('Y-m-d H:i:s',strtotime($effective_time)+3600*24);//生效时间
        }

        if(!$detail_model->save()) {
            Yii::$app->logger->write_log('外协工单员工信息保存失败，可能出现的问题：' . json_encode($detail_model->getErrors(), JSON_UNESCAPED_UNICODE).';工单id：'.$serial_no);
            return array_values($detail_model->getErrors());
        } else {
            //ffm 不发短信
            if($outsourcing_model->source_category == 1){
                return true;
            }

            //发送短信 手机号/短信内容
            $param = [
                'shift_id' => $outsourcing_model->shift_id,
                'employment_date' => $outsourcing_model->employment_date,
                'staff_name' => $staffs['staff_name'],
                'identity' => $staffs['identity'],
                'driver_license' => $staffs['driver_license'],
                'bank_type' => $staffs['bank_id'],
                'bank_no_name' => $staffs['bank_no_name'],
                'bank_no' => $staffs['bank_no'],
                'effective_date' => $outsourcing_model->effective_date,
                'job_title' => $outsourcing_model->job_id,
                'sys_store_id' => $outsourcing_model->store_id,
                'serial_no' => $serial_no,
            ];
            OutSourcingOrderService::getInstance()->send_sms_staff_info($staffs['mobile'],$param);
        }
        return true;
    }

    public static function save_ph($serial_no,$staffs,$outsourcing_model,$fbid) {
        try {
            $staff_info_model = StaffInfo::find()->where(['identity' => $staffs['identity']])->andWhere(['formal' => 0])->andWhere(['staff_type' => [2,3]])->one();
            $staff_info_id = 0;
            //如果工号存在并且是普通外协
            if($staff_info_model && $outsourcing_model->hire_os_type ==BaseStaffInfo::HIRE_OS_TYPE_COMMON) {
                $staff_info_id = $staff_info_model->staff_info_id;
            }
            $staffs['sex'] =  empty($staffs['sex']) ? 0 : $staffs['sex'];//性别
            $staffs['bank_id'] = empty($staffs['bank_id']) ? 0 : $staffs['bank_id'];//银行卡
            $detail_model = new HrOutsourcingOrderDetail();
            $detail_model->serial_no = $serial_no;//订单编号
            $detail_model->staff_info_id = $staff_info_id;//员工号
            $detail_model->identity = $staffs['identity'];//身份证号
            $detail_model->staff_name = $staffs['staff_name'];//员工姓名
            $detail_model->sex =  $staffs['sex'];//性别
            $detail_model->mobile = $staffs['mobile'];//手机号
            $detail_model->outsourcing_type = $staffs['outsourcing_type'];//外协类型
            $detail_model->pay_type = $staffs['pay_type'];//结算类型
            $detail_model->company_name_ef = $staffs['company_name_ef'];//公司名称
            $detail_model->driver_license = $staffs['driver_license'];//驾驶证号
            $detail_model->bank_id = $staffs['bank_id'];//银行卡
            $detail_model->bank_no_name = $staffs['bank_no_name'];//持卡人姓名
            $detail_model->bank_no = $staffs['bank_no'];//银行卡号
            $detail_model->car_no = $staffs['car_no'];//车牌号
            $detail_model->effective_date = $outsourcing_model->effective_date;//生效时间
            $detail_model->invalid_date = $outsourcing_model->invalid_date;//失效时间
            $detail_model->is_complement  = $staffs['is_complement'];
            $detail_model->create_staff_info_id = $fbid;
            $detail_model->personal_email = $staffs['personal_email'];//个人邮箱

            $shift = Yii::$app->sysconfig->getShift($outsourcing_model->shift_id);
            if($staffs['is_complement'] == 1) {
                $detail_model->is_effective = 1;//菲律宾补录人员立即生效

                $current_time = gmdate('Y-m-d H:i:00',time()+TIME_ADD_HOUR*3600);
                $current_date = gmdate('Y-m-d',strtotime($current_time));
                $effective_time = gmdate('Y-m-d H:i:00',strtotime($current_date.$shift['start'])-3600*2);//当天班次生效时间；当前班次-1小时
                $detail_model->effective_date = $effective_time;//生效时间
            }

            $trans = $detail_model->getDb()->beginTransaction();
            if(!$detail_model->save()) {
                $trans->rollback();
                Yii::$app->logger->write_log('外协工单员工信息保存失败，可能出现的问题：' . json_encode($detail_model->getErrors(), JSON_UNESCAPED_UNICODE).';工单id：'.$serial_no);
                return array_values($detail_model->getErrors());
            }
            if($outsourcing_model->status == 2) { //工单状态是已生效的话 直接创建工号
                $effective_staff_result = self::effective_staff_ph($serial_no, $staffs, $fbid);
                if($effective_staff_result !== true) {
                    $trans->rollback();
                    return $effective_staff_result;
                }
            }

            //发送短信 手机号/短信内容
            $param = [
                'shift_id' => $outsourcing_model->shift_id,
                'employment_date' => $outsourcing_model->employment_date,
                'staff_name' => $staffs['staff_name'],
                'identity' => $staffs['identity'],
                'driver_license' => $staffs['driver_license'],
                'bank_type' => $staffs['bank_id'],
                'bank_no_name' => $staffs['bank_no_name'],
                'bank_no' => $staffs['bank_no'],
                'effective_date' => $outsourcing_model->effective_date,
                'job_title' => $outsourcing_model->job_id,
                'sys_store_id' => $outsourcing_model->store_id,
                'serial_no' => $serial_no,
            ];
            OutSourcingOrderService::getInstance()->send_sms_staff_info($staffs['mobile'],$param);

            $trans->commit();
            return true;
        } catch (\Exception $e) {
            $trans->rollback();
            Yii::$app->logger->write_log('外协工单员工信息保存失败，可能出现的问题：' . $e->getMessage() . ';行号：'.$e->getLine() . ';file:'.$e->getFile().';工单id：'.$serial_no);
            return false;
        }
    }

    //菲律宾立即生效
    public static function effective_staff_ph($serial_no, $detail_value, $fbid) {

        $order = HrOutsourcingOrder::find()->where(['serial_no' => $serial_no])->asArray()->one();
        $job_id = $order['job_id'];
        $store_id = $order['store_id'];
        $department_id = $order['department_id'];
        $os_type = $order['os_type'];

        $staff_arr = [
            'name' => $detail_value['staff_name'],
            'sex' => $detail_value['sex'],
            'identity' => $detail_value['identity'],
            'mobile' => $detail_value['mobile'],
            'personal_email' => $detail_value['personal_email'] ?? '',
            'job_title' => $job_id,//职位id
            'sys_store_id' => $store_id,//网点id
            'sys_department_id' => $department_id,//部门id
            'node_department_id' => $department_id,
            'formal' => 0,
            'state' => 1,
            'hire_date' => date('Y-m-d',strtotime($order['effective_date'])),
            'bank_no' => $detail_value['bank_no'],
            'bank_type' => $detail_value['bank_id'],
            'outsourcing_type' => $detail_value['outsourcing_type'],
            'bank_no_name' => $detail_value['bank_no_name'],
            'staff_car_no' => $detail_value['car_no'],
            'company_name_ef' => $detail_value['company_name_ef'],
            'pay_type' => $detail_value['pay_type'],
            'position_category' => OutSourcingService::$job_position_country[YII_COUNTRY][$job_id] ?? [],
            'staff_car_type' => self::$car_type[$job_id] ?? '',
            'driver_license' => $detail_value['driver_license'],
            'staff_type' => $os_type == 1 ? 2 : 3,
            'leave_date' => null,
            'hire_type'  => $order['hire_os_type'] ==  BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING  ? BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING  : BaseStaffInfo::HIRE_OS_TYPE_COMMON, //11 普通外协 12 众包外协
        ];

        $base_staff_info = BaseStaffInfo::find()->where(['identity' => $detail_value['identity']])->andWhere(['formal' => 0])->andWhere(['staff_type' => [2,3]])->one();
        if(!$base_staff_info || $order['hire_os_type'] == BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING) { // 如果不存在或者 是外包外协
            $staff_model = Staff::combination($staff_arr);//格式化
            $before = [];
        } else {
            $staff_arr['staff_info_id'] = $base_staff_info->staff_info_id;
            $staff_model = Staff::combination($staff_arr);//格式化
            $before = Staff::view($base_staff_info->staff_info_id) ?? [];
        }
        $currentOperateId = Yii::$app instanceof yii\console\Application ? '-1' : $fbid;
        $res = Staff::save($staff_model, $currentOperateId, true);
        Yii::$app->logger->write_log('effective_staff_ph: 保存员工信息 结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE) .';工单id: '.$serial_no, 'info');
        //更新员工工号
        $o_detail = HrOutsourcingOrderDetail::find()->where(['identity' => $detail_value['identity']])->andWhere(['is_del' => 0])->andWhere(['serial_no' => $serial_no])->one();
        if($res === true) {
            $after = Staff::view($staff_model->staff_info_id);
            self::setStaffOperateLogs($staff_model->staff_info_id, $before, $after,$fbid);
            $o_detail->staff_info_id = $staff_model->staff_info_id;
            $o_detail->is_effective = 1;//变更成已生效
            $o_detail->effective_state_message = (string)$staff_model->staff_info_id;
            $os_detail_result = $o_detail->save();//更新员工工号
            if($os_detail_result !== true) {
                Yii::$app->logger->write_log('effective_staff_ph: 工号: ' . $staff_model->staff_info_id .'---'. json_encode($os_detail_result) . json_encode($o_detail->getErrors()) .';工单id: '.$serial_no);
            } else {
                Yii::$app->logger->write_log('effective_staff_ph: 工号: ' . $staff_model->staff_info_id .';工单id: '.$serial_no, 'info');
            }
            //更新员工班次
            self::setStaffShift($staff_model->staff_info_id, $order['shift_id'], $order['effective_date'],$order['employment_date'],$order['employment_days']);
        }else{
            $o_detail->effective_state_message = json_encode($res,JSON_UNESCAPED_UNICODE);
            $os_detail_result = $o_detail->save();//更新员工工号
        }
        return $res;
    }

    //订单修雇佣日期。雇佣天数。班次修改的时候发送短信通知已配置人员
    public static function send_sms_order_update($serial_no,$param) {
        //$param 网点编码/雇佣日期/工作开始时间/工作结束时间
        $detail_list = HrOutsourcingOrderDetail::find()->where(['serial_no' => $serial_no])->asArray()->all();
        if(count($detail_list) > 0) {
            //网点名称
            $sys_store_name = '';
            if(!empty($param['sys_store_id'])) {
                $sys_store_name = $param['sys_store_id'] == '-1' ? Yii::$app->lang->get('head_office') : Staff::getStoreInfo($param['sys_store_id'])['name'];
            }
            $shift = $param['start'].'-'.$param['end'];
            $employment_date = $param['employment_date'];
            $sms_content = "สวัสดี สาขาทำงานหรือกะทำงานของคุณมีการเปลี่ยนแปลง กรุณาเข้างาน $employment_date - $shift ที่ $sys_store_name";
            $sms_content_en = "Hello, your work branch or work shift have been changed. Please go to work at $sys_store_name on $employment_date - $shift";
            $sms_content_my = "Helo, cawangan kerja atau syif kerja anda telah ditukar. Sila pergi bekerja di  $sys_store_name pada $employment_date - $shift";
            $sms_content_la = "ສະບາຍດີ, ສະຖານທີ່ເຮັດວຽກ ຫຼື ກະເຂົ້າວຽກຂອງທ່ານໄດ້ມີການປ່ຽນແປງແລ້ວ , ກະລຸນາໄປວຽກທີ່ $sys_store_name $employment_date - $shift";

            switch (YII_COUNTRY) {
                case 'TH':
                    $send_content = $sms_content;
                    break;
                case 'MY':
                    $send_content = $sms_content_my;
                    break;
                case "PH":
                    $send_content = $sms_content_en;
                    break;
                case 'LA':
                    $send_content = $sms_content_la;
                    break;
                default:
                    $send_content = $sms_content_en;
            }
            foreach ($detail_list as $item => $value) {
                Yii::$app->jrpc->smsSend($value['mobile'], $send_content, 'out_sourcing');
            }
        }
    }
    //订单生效失效时间
    public static function getEffective_Invalid_Date($employment_date,$employment_days,$shift_start,$shift_end) {
        $effective_date = '';//生效时间
        $invalid_date = '';//失效时间
        $effective_date = gmdate('Y-m-d H:i:s',strtotime('-2 hour',strtotime($employment_date.$shift_start)));//生效时间 = 雇用日期 + 工作班次起始时间 - 0.5小时 //需求改为提前1小时生效
        //失效时间 = 雇用日期  + 工作班次结束时间 + (雇用天数-1)  + 4小时，如果是跨天的班次，则 失效时间 = 雇用日期 + 雇用天数 + 工作班次结束时间 + 4小时
        //邮件需求 失效时间改为工作班次结束时间 + 5.5小时
        if($shift_start > $shift_end) {
            $invalid_date = gmdate('Y-m-d H:i:s',strtotime("+$employment_days day +330 minute ",strtotime($employment_date.' '.$shift_end)));
        } else {
            $employment_days = $employment_days -1;
            $invalid_date = gmdate('Y-m-d H:i:s',strtotime("+$employment_days day +330 minute ",strtotime($employment_date.' '.$shift_end)));
        }
        return [
                'effective_date' =>$effective_date,
                'invalid_date' => $invalid_date
            ];
    }

    //hub 订单生效失效时间--班次结束时间后的5小时
    public static function getHubEffective_Invalid_Date($employment_date, $employment_days, $shift_start, $shift_end)
    {
        $effective_date = '';//生效时间
        $invalid_date   = '';//失效时间
        $effective_date = gmdate('Y-m-d H:i:s', strtotime($employment_date .' '. $shift_start) - 30 * 60);

        if(isCountry('MY')) {
            $effective_date = gmdate('Y-m-d H:i:s', strtotime($employment_date .' '. $shift_start));
        }

        //失效时间 =工作班次结束时间
        if ($shift_start > $shift_end) {//跨天
            $invalid_date = gmdate('Y-m-d H:i:s',
                strtotime("+$employment_days day +330 minute  ", strtotime($employment_date.' '.$shift_end)));
        } else {
            $employment_days = $employment_days - 1;//不跨天
            $invalid_date    = gmdate('Y-m-d H:i:s',
                strtotime("+$employment_days day +330 minute  ", strtotime($employment_date.' '.$shift_end)));
        }
        return [
            'effective_date' => $effective_date,
            'invalid_date'   => $invalid_date,
        ];
    }

    //订单生效失效时间-菲律宾
    public static function getEffective_Invalid_Date_ph($employment_date,$employment_days,$shift_start,$shift_end) {
        //https://l8bx01gcjr.feishu.cn/docs/doccnGcRAXzbLJiuE3JvrDsEONf
        $effective_date = '';//生效时间
        $invalid_date = '';//失效时间
        $effective_date = gmdate('Y-m-d H:i:s',strtotime('-2 hour',strtotime($employment_date.$shift_start)));
        if($shift_start > $shift_end) {
            $invalid_date = gmdate('Y-m-d H:i:s',strtotime("+$employment_days day +480 minute ",strtotime($employment_date.' '.$shift_end)));
        } else {
            $employment_days = $employment_days -1;
            $invalid_date = gmdate('Y-m-d H:i:s',strtotime("+$employment_days day +480 minute ",strtotime($employment_date.' '.$shift_end)));
        }
        return [
            'effective_date' =>$effective_date,
            'invalid_date' => $invalid_date
        ];
    }

    //长期工单生效失效时间
    public static function getEffective_Invalid_Date_by_longtime($employment_date,$employment_days,$shift_start,$shift_end) {
        $effective_date = '';//生效时间
        $invalid_date = '';//失效时间
        $effective_date = gmdate('Y-m-d H:i:s',strtotime('-2 hour',strtotime($employment_date.$shift_start)));//生效时间 = 雇用日期 + 工作班次起始时间 - 0.5小时 //需求改为提前1小时生效
        //失效时间 = 工作订单的失效时间为雇用日期+雇用天数（自然日）的次日中午12:00 失效时间=雇用日期 + （雇用天数 +1）+12小时
        $employment_days = $employment_days +1;
        $invalid_date = gmdate('Y-m-d H:i:s',strtotime("+$employment_days day +12 hour ",strtotime($employment_date)));

        return [
            'effective_date' =>$effective_date,
            'invalid_date' => $invalid_date
        ];
    }

    //验证工号是否与其他工单冲突
    public static function validate_staff_conflict($serial_no, $effective_date, $identity) {
        //判断员工是否与其他工单时间冲突
        $order_detail = HrOutsourcingOrderDetail::find()
                                ->leftJoin('hr_outsourcing_order','hr_outsourcing_order_detail.serial_no=hr_outsourcing_order.serial_no')
                                ->select('hr_outsourcing_order_detail.*')
                                ->where(['hr_outsourcing_order_detail.identity' => $identity])
                                ->andWhere(['hr_outsourcing_order_detail.is_del' => 0])
                                ->andWhere(['>','hr_outsourcing_order_detail.invalid_date',$effective_date])
                                ->andWhere(['hr_outsourcing_order.status' => [1,2]])
                                ->andWhere(['!=','hr_outsourcing_order.serial_no',$serial_no])
                                ->asArray()
                                ->all();
        if(count($order_detail) > 0) {
            $serial_no_arr = array_column($order_detail, 'serial_no');
            $serial_no_str = implode(',',$serial_no_arr);
            return $serial_no_str;
        }
        return true;
    }

    //验证员工在职状态，未完成派件任务未回公款
    public static function validate_staff_info($staff_info) {
        //张帆 去掉在职验证 员工离职状态验证是否有未回款 未完成的派件任务
        //判断是否在职 如果在职返回falser
        if($staff_info['state'] == 1) {
            //return ['outsourcing_tip6'];
            return true;
        }

        //未完成的派件任务
        if (TicketDelivery::find()->where(['staff_info_id' => $staff_info['staff_info_id']])->andWhere(['state' => 0])->exists()) {
            return ['outsourcing_tip7'];
        }

        //未回款的快递员公款
        if (StoreReceivableBillDetail::find()->where(['store_id' => $staff_info['sys_store_id']])->andWhere(['staff_info_id' => $staff_info['staff_info_id']])->andWhere(['state' => [0, 3]])->exists()) {
            return ['outsourcing_tip8'];
        }
        return true;
    }

    //验证工单状态
    public static function validate_outsourcing_order($order_model) {
        if(!$order_model) {
            return ['outsourcing_tip9'];//未找到工单
        }

        //验证工单状态
        if(in_array($order_model->status, [2, 3, 4])) {
            return ['outsourcing_tip10'];
        }

        return true;
    }

    //验证改身份证号是否是被公司开除
    public static function validate_staff_leave_reason($identity) {
        if(BaseStaffInfo::find()->where(['leave_reason' => 2,'identity' => $identity])->exists()) {
            return ['outsourcing_tip15'];
        }
        return true;
    }

    //给车队创建外协信息
    public static function fleet_create_staff($param,$lang) {
        $serial_no = $param['serial_no'];
        $create_staff_info_id = $param['create_staff_info_id']??0;

        $staffs = $param['staffs'];//员工信息
        $return_param['serial_no'] = $serial_no;
        try {
            $out_sourcing_model = HrOutsourcingOrder::find()->where(['serial_no' => $serial_no])->one();
            foreach ($staffs as $key => $value) {

                //验证身份证号是否已经在工单中存在
                $identity_detail = HrOutsourcingOrderDetail::find()->where(['serial_no' => $serial_no])->andWhere(['identity' => $value['identity']])->andWhere(['is_del' => 0])->one();
                if($identity_detail && !empty($identity_detail->staff_info_id)) {
                    $return_param['staffs'][] = ['identity' => $value['identity'],'staff_info_id' => $identity_detail->staff_info_id];
                    continue;
                }

                //验证工单总人数
                $detail_count = HrOutsourcingOrderDetail::find()->where(['serial_no' => $serial_no])->count();
                if($detail_count >= $out_sourcing_model->final_audit_num) {
                    $return_param['staffs'][] = ['identity' => $value['identity'],'staff_info_id' => 0, 'msg' => Yii::$app->lang->get('outsourcing_tip13','',$lang)];
                    continue;
                }

                //验证身份证号是否在黑名单
                $validate_out_sourcing_identity = OutSourcingService::getInstance()->validateOutSourcingIdentity($value['identity']);
                if($validate_out_sourcing_identity !== true) {
                    $return_param['staffs'][] = ['identity' => $value['identity'],'staff_info_id' => 0, 'msg' => Yii::$app->lang->get($validate_out_sourcing_identity[0],'',$lang)];
                    continue;
                }

                //验证提交字段
                $validate_field = OutSourcingService::getInstance()->validateOutSourcingField($value,$out_sourcing_model->job_id);
                if($validate_field !== true) {
                    $msg = [];
                    if(($validate_field[0] ?? false)) {
                        $msg[] = Yii::$app->lang->get($validate_field[0],'',$lang);
                    }
                    if(($validate_field[1] ?? false)) {
                        $msg[] = Yii::$app->lang->get($validate_field[1],'',$lang);
                    }
                    $return_param['staffs'][] = ['identity' => $value['identity'],'staff_info_id' => 0, 'msg' => implode(',',$msg)];
                    continue;
                }

                //判断身份证号是否与其他工单冲突
                $result_conflict = self::validate_staff_conflict($serial_no,$out_sourcing_model->effective_date,$value['identity']);
                if($result_conflict !== true) {
                    $return_param['staffs'][] = ['identity' => $value['identity'],'staff_info_id' => 0, 'msg' => Yii::$app->lang->get('outsourcing_tip3','',$lang)];
                    continue;
                }

                $staff_info_model = StaffInfo::find()->where(['identity' => $value['identity']])->andWhere(['formal' => 0])->andWhere(['staff_type' => [2,3]])->one();
                $staff_info_id = 0;
                if($staff_info_model) {
                    $staff_info_id = $staff_info_model->staff_info_id;
                }

                $detail_model = new HrOutsourcingOrderDetail();
                $detail_model->serial_no = $serial_no;//订单编号
                $detail_model->staff_info_id = $staff_info_id;//员工号
                $detail_model->identity = $value['identity'];//身份证号
                $detail_model->staff_name = $value['staff_name'];//员工姓名
                $detail_model->sex =  $value['sex'];//性别
                $detail_model->mobile = $value['mobile'];//手机号
                $detail_model->outsourcing_type = $value['outsourcing_type'];//外协类型
                $detail_model->pay_type = $value['pay_type'];//结算类型
                $detail_model->company_name_ef = $value['company_name_ef'];//公司名称
                $detail_model->driver_license = $value['driver_license'];//驾驶证号
                $detail_model->bank_id = $value['bank_id'];//银行卡
                $detail_model->bank_no_name = $value['bank_no_name'];//持卡人姓名
                $detail_model->bank_no = $value['bank_no'];//银行卡号
                $detail_model->car_no = $value['car_no'];//车牌号
                $detail_model->effective_date = $out_sourcing_model->effective_date;//生效时间
                $detail_model->invalid_date = $out_sourcing_model->invalid_date;//失效时间
                $detail_model->create_staff_info_id = $out_sourcing_model->staff_info_id;//车队与员工创建默认是申请人工号

                //创建员工信息 在职
                $staff_arr = [
                    'name' => $value['staff_name'] ?? '', //q
                    'sex' => $value['sex'] ?? 0, //q
                    'identity' => $value['identity'] ?? '', //q
                    'mobile' => $value['mobile'] ?? '', //q
                    'personal_email' => $value['personal_email'] ?? '',
                    'job_title' => (string)$out_sourcing_model->job_id,//职位id
                    'sys_store_id' => $out_sourcing_model->store_id,//网点id
                    'sys_department_id' => (string)$out_sourcing_model->department_id,//部门id
                    'formal' => 0,
                    'state' => 1,
                    'hire_date' => date('Y-m-d',strtotime($out_sourcing_model->effective_date)),
                    'bank_no' => $value['bank_no'] ?? '',
                    'bank_type' => $value['bank_id'] ?? 0,
                    'outsourcing_type' => $value['outsourcing_type'] ?? 'company', //q
                    'bank_no_name' => $value['bank_no_name'] ?? '', //q
                    'staff_car_no' => $value['car_no'], //q
                    'company_name_ef' => $value['company_name_ef'], //q
                    'pay_type' => $value['pay_type'], //q
                    'position_category' => [1],
                    'staff_car_type' => 'Bike',
                    'driver_license' => $value['driver_license'], //q
                    'staff_type' =>$out_sourcing_model->os_type == 1 ? 2 : 3,
                    'leave_date' => null,
                    'hire_type'  => $out_sourcing_model->hire_os_type ==  BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING  ? BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING  : BaseStaffInfo::HIRE_OS_TYPE_COMMON, //11 普通外协 12 众包外协

                ];
                //创建员工信息

                if($staff_info_id == 0) {
                    $staff_model = Staff::combination($staff_arr);//格式化
                    $before = [];
                } else {
                    $staff_arr['staff_info_id'] = $staff_info_id;
                    $staff_model = Staff::combination($staff_arr);//格式化
                    $before = Staff::view($staff_info_id) ?? [];
                }
                $res = Staff::save($staff_model,'-1',false);//保存员工信息

                if($res === true) {
                    $after = Staff::view($staff_model->staff_info_id);
                    self::setStaffOperateLogs($staff_model->staff_info_id,$before,$after,$create_staff_info_id);//保存操作日志
                    self::setStaffShift($staff_model->staff_info_id,$out_sourcing_model->shift_id,$out_sourcing_model->effective_date,$out_sourcing_model->employment_date,$out_sourcing_model->employment_days);//更新外协员工班次

                    HrOutsourcingOrderDetail::deleteAll(['serial_no' => $serial_no,'identity' => $value['identity']]);

                    $detail_model->staff_info_id = $staff_model->staff_info_id;
                    $detail_model->save();//保存到外协工单表

                    $return_param['staffs'][] = ['identity' => $value['identity'],'staff_info_id' => $staff_model->staff_info_id];
                } else {
                    $res_msg = [];
                    if(($res[0] ?? false)) {
                        $res_msg[] = Yii::$app->lang->get($res[0],'',$lang);
                    }
                    if(($res[1] ?? false)) {
                        $res_msg[] = Yii::$app->lang->get($res[1],'',$lang);
                    }
                    $return_param['staffs'][] = ['identity' => $value['identity'],'staff_info_id' => 0,'msg' => implode(',',$res_msg)];
                    Yii::$app->logger->write_log('fleet_create_staff,创建工号失败，参数：'.json_encode($staff_arr).',结果：'.json_encode($res),'info');
                }
            }
            $result = [
                    'code' => 1,
                    'msg' => 'success',
                    'data' => $return_param
                    ];
            return $result;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('fleet_create_staff，参数：'.json_encode($param).',可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
            $return_param['staffs'] = [];
            $result = [
                'code' => 0,
                'msg' => 'error',
                'data' => $return_param
            ];
            return $result;
        }
    }

    //更新外协员工班次
    public static function setStaffShift($staff_info_id,$shift_id,$effective_date,$employment_date,$employment_days) {
        $staff_shift_model = HrStaffShift::findOne($staff_info_id);
        $shift = Yii::$app->sysconfig->getShift($shift_id);
        if(!$staff_shift_model) {
            $staff_shift_model = new HrStaffShift();
            $staff_shift_model->staff_info_id = $staff_info_id;
        }
        $staff_shift_model->start = $shift['start'];
        $staff_shift_model->end = $shift['end'];
        $staff_shift_model->shift_type = $shift['type'];
        $staff_shift_model->shift_id = $shift_id;
        $staff_shift_model->effective_date = $effective_date;//生效时间
        $staff_shift_model->last_start = $shift['start'];//上一班次开始时间
        $staff_shift_model->last_end = $shift['end'];//上一班次结束时间
        $staff_shift_model->last_shift_type = $shift['type'];//上一班次类型
        $staff_shift_model->last_shift_id = $shift_id;//上一班次id
        $result = $staff_shift_model->save();

        OutSourcingOrderService::getInstance()->addStaffShiftToMiddle($staff_info_id, $employment_date, $employment_days, $shift);
        return $result;
    }

    //设置员工操作日志
    public static function setStaffOperateLogs($staff_info_id,$before,$after,$fbid = 0) {
        $log = new HrOperateLogs();
        $log->operater = $fbid ?:'-1';
        $log->request_body = molten_get_traceid();
        $log->before = json_encode(['body' => $before ?? []]);
        $log->after = json_encode(['body' => $after]);
        $log->type = 'staff';
        $log->staff_info_id = $staff_info_id;
        $result = $log->save();
        return $result;
    }

    //批量导入工单信息
    public static function ImportOrder($excel_data, $permission_role, $fbid) {
        try {
            //0 工作订单id//1 身份证号//2 姓名//3 性别//4 手机号//5 外协员工类型//6 结算类型//7 外协公司//8 驾驶证号//9 车牌号//10 银行//11 银行卡持卡人//12 银行卡号
            $serial_no_arr = array_column($excel_data, '0');
            $excel_data_by_serial_no = [];
            $currentDate = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);//当前泰国时间
            foreach ($excel_data as $key => $value) {
                if (!empty($value[0])) {
                    $excel_data_by_serial_no[$value[0]][] = true;
                }
            }

            $order_list = HrOutsourcingOrder::find()->where(['serial_no' => $serial_no_arr])->indexBy('serial_no')->asArray()->all();



            $outsourceCompanyList = Yii::$app->jrpc->getOsCompanyList();
            $outsourceCompanyListToId = !empty($outsourceCompanyList) ? array_column($outsourceCompanyList,  'label','value') : [];
            $outsourceCompanyListId = !empty($outsourceCompanyListToId) ? array_keys($outsourceCompanyListToId) : [];

            $order_count = HrOutsourcingOrderDetail::find()
                ->select(['serial_no','count(*) as count'])
                ->where(['serial_no' => $serial_no_arr])
                ->andWhere(['is_del' => 0])
                ->groupBy('serial_no')
                ->indexBy('serial_no')
                ->asArray()
                ->all();
            //身份证号相关
            $identities = array_column($excel_data, '1');
            $identities = array_values(array_unique(array_filter($identities)));
            $existIdentities = OutSourcingOrderService::getInstance()->getConfigOrConfiguredOrderForIdentity($identities);
            $excel_data_error_result = [];
            $success_count = 0;
            $error_count = 0;
            foreach ($excel_data as $key => $value) {
                $serial_no = $value[0]; //外协员工工作订单ID
                $identity = trim($value[1]);  //身份证号
                $outsourcing_type = 0; //外协员工类型
                switch ($value[5]) {
                    case 1:
                        $outsourcing_type = 'individual';
                        break;
                    case 2:
                        $outsourcing_type = 'company';
                        break;
                    default:
                        $outsourcing_type = $value[5];
                }
                $pay_type = 0; //结算类型
                switch ($value[6]) {
                    case 1:
                        $pay_type = 'BY_DAY';
                        break;
                    case 2:
                        $pay_type = 'BY_MONTH';
                        break;
                    case 3:
                        $pay_type = 'CASH_PAY';
                        break;
                    default:
                        $pay_type = $value[6];
                }

                if(!isset($order_list[$serial_no])) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip9'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                $staff['serial_no'] = $serial_no; //外协员工工作订单ID
                $staff['identity'] = $identity;  //身份证号
                $staff['staff_name'] = $value[2];  //员工姓名
                $staff['sex'] = $value[3];  //性别
                $staff['mobile'] = $value[4];  //手机号
                $staff['outsourcing_type'] = $outsourcing_type;  //外协员工类型
                $staff['pay_type'] = $pay_type;  //结算类型
                if(isCountry('TH')) {
                    $staff['company_item_id'] = $value[7];  //外协公司---枚举。
                } else {
                    $staff['company_name_ef'] = $value[7];  //外协公司旧
                }
                $staff['driver_license'] = $value[8];  //驾驶证号
                $staff['car_no'] = $value[9];  //车牌号
                $staff['bank_id'] = $value[10]; //银行
                $staff['bank_no_name'] = $value[11]; //持卡人姓名
                $staff['bank_no'] = $value[12]; //银行卡号
                $staff['personal_email'] = trim($value[13]); //个人邮箱

                //工单状态不正确 ffm的 可以生效中 编辑 其他类型 只能带生效
                if (
                    ($order_list[$serial_no]['source_category'] != 1 && $order_list[$serial_no]['status'] != 1)
                    || ($order_list[$serial_no]['source_category'] == 1 && !in_array($order_list[$serial_no]['status'],[1,2]))
                ) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip10'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }


                //订单已经超过可配置时间 ffm订单 不验证
                if (strtotime($order_list[$serial_no]['effective_date']) < strtotime($currentDate)
                    && $order_list[$serial_no]['source_category'] != 1
                ) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip19'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                //验证权限
                if(!self::validate_edit_order($permission_role, $order_list[$serial_no]['job_id'])) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip20'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                //验证订单可配置人数是否超过文件订单人数
                $final_audit_num = $order_list[$serial_no]['final_audit_num'];
                $order_count_exist = $order_count[$serial_no]['count'] ?? 0;
                if (count($excel_data_by_serial_no[$serial_no]) > ($final_audit_num - $order_count_exist)) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip21'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                //验证提交字段
                $validate_result = OutSourcingService::getInstance()->validateOutSourcingField($staff, $order_list[$serial_no]['job_id'], $order_list[$serial_no]['hire_os_type']);
                if ($validate_result !== true) {
                    $msg = '';
                    if (($validate_result[0] ?? false)) {
                        $msg .= Yii::$app->lang->get($validate_result[0]);
                    }
                    if (($validate_result[1] ?? false)) {
                        $msg .= Yii::$app->lang->get($validate_result[1]);
                    }
                    array_push($value, $msg);
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                //验证身份证号是否已经在工单中存在
                $identity_exists = HrOutsourcingOrderDetail::find()->where(['serial_no' => $serial_no])->andWhere(['identity' => $identity])->andWhere(['is_del' => 0])->exists();
                if($identity_exists) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip1'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                //校验身份证号是否在黑名单
                $validate_out_sourcing_identity = OutSourcingService::getInstance()->validateOutSourcingIdentity($identity);
                if($validate_out_sourcing_identity !== true) {
                    array_push($value, Yii::$app->lang->get($validate_out_sourcing_identity[0]));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                if (!empty($staff['bank_no']) && !empty($staff['bank_id'])){
                    if(($res = StaffInfoCheck::validateBankNoStingLen($staff['bank_no'], $staff['bank_id'])) && $res !== true) {
                        array_push($value, ($res[0] ?? '') . ($res[1] ?? ''));
                        $excel_data_error_result[] = $value;
                        $error_count++;
                        continue;
                    }
                }

                //验证身份证号是否跟其他工号冲突
                $identitySerialNos = OutSourcingOrderService::getInstance()->checkEffectiveTimeDuplicated($existIdentities[$identity] ?? [],
                    $order_list[$serial_no]['effective_date'], $order_list[$serial_no]['invalid_date']);
                if ($identitySerialNos) {
                    $value[]                   = Yii::$app->lang->get('outsourcing_tip3'). implode(',', $identitySerialNos);
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }
                $existIdentities[$identity][] = [
                    'start'     => $order_list[$serial_no]['effective_date'],
                    'end'       => $order_list[$serial_no]['invalid_date'],
                    'serial_no' => $serial_no,
                ];

                if (YII_COUNTRY == 'TH') {
                    // 获取生效的众包任务
                    $crowd_sourcing_check_list = OutSourcingOrderService::getInstance()->getCrowdSourcingTasks($value, $order_list[$serial_no]);

                    // 验证生效的众包任务
                    $effective_date = $order_list[$serial_no]['effective_date'];//生效时间
                    $invalid_date   = $order_list[$serial_no]['invalid_date'];  //失效时间
                    //判断是否在众包外协已经报名
                    $crowd_sourcing_key    = $identity . '-' . strtotime($effective_date) . '-' . strtotime($invalid_date);
                    if (empty($crowd_sourcing_check_list)) {
                        $crowd_sourcing_exists = false;
                    } else {
                        $crowd_sourcing_exists = $crowd_sourcing_check_list[$crowd_sourcing_key]['exist'] ?? false;
                    }
                    if ($crowd_sourcing_exists === true) {
                        array_push($value, Yii::$app->lang->get('outsourcing_tip28'));
                        $excel_data_error_result[] = $value;
                        $error_count++;
                        continue;
                    }
                    //验证外协公司枚举
                    if(!empty($staff['company_item_id']) && !in_array($staff['company_item_id'], $outsourceCompanyListId)) {
                        array_push($value, Yii::$app->lang->get('outsourcing_tip_not_os_compay'));
                        $excel_data_error_result[] = $value;
                        $error_count++;
                        continue;
                    }

                    //premier公司 身份证 必须为 13位数字
                    if($staff['company_item_id'] == OutSourcingService::COMPANY_ITEM_PREMIER && !preg_match(OutSourcingService::PREG_MATCH_OS_COMPANY_PREMIER, $staff['identity'])) {
                        array_push($value, Yii::$app->lang->get('os_staff_identity_length_premier'));
                        $excel_data_error_result[] = $value;
                        $error_count++;
                        continue;
                    }
                }

                //判断身份证号是否在职未完成派件未回公款
                /*
                $staff_info = StaffInfo::find()->where(['identity' => $identity])->andWhere(['formal' => 0])->andFilterWhere(['staff_type' => [2,3]])->asArray()->one();
                if($staff_info) {
                    $result_staff = self::validate_staff_info($staff_info);
                    if($result_staff !== true) {
                        array_push($value, Yii::$app->lang->get($result_staff[0]));
                        $excel_data_result[] = $value;
                        $excel_data_error_result[] = $value;
                        $error_count++;
                        continue;
                    }
                }
                */

                $detail_model = new HrOutsourcingOrderDetail();
                $detail_model->serial_no = $serial_no;//订单编号
                $detail_model->staff_info_id = 0;//员工号
                $detail_model->identity = $staff['identity'];//身份证号
                $detail_model->staff_name = $staff['staff_name'];//员工姓名
                $detail_model->sex = $staff['sex'];//性别
                $detail_model->personal_email = $staff['personal_email'];//个人邮箱
                $detail_model->mobile = $staff['mobile'];//手机号
                $detail_model->outsourcing_type = $staff['outsourcing_type'];//外协类型
                $detail_model->pay_type = $staff['pay_type'];//结算类型
                if(isCountry('TH')) {
                    $detail_model->company_item_id = empty($staff['company_item_id']) ? 0 : $staff['company_item_id'];//公司名称枚举(新)
                } else {
                    $detail_model->company_name_ef = $staff['company_name_ef'];//公司名称
                }

                $detail_model->driver_license = $staff['driver_license'];//驾驶证号
                $detail_model->bank_id = $staff['bank_id'];//银行卡
                $detail_model->bank_no_name = $staff['bank_no_name'];//持卡人姓名
                $detail_model->bank_no = $staff['bank_no'];//银行卡号
                $detail_model->car_no = $staff['car_no'];//车牌号
                $detail_model->effective_date = $order_list[$serial_no]['effective_date'];//生效时间
                $detail_model->invalid_date = $order_list[$serial_no]['invalid_date'];//失效时间
                $detail_model->is_complement = 0;
                $detail_model->create_staff_info_id = $fbid;

                if (!$detail_model->save()) {
                    //$msg = 'error';
                    $msg = json_encode($detail_model->getErrors(), JSON_UNESCAPED_UNICODE);
                    array_push($value, $msg);
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    Yii::$app->logger->write_log('外协工单员工信息保存失败，可能出现的问题：' . $msg . ';工单id：' . $serial_no, 'info');
                } else {
                    //发送短信 手机号/短信内容
                    $param = [
                        'shift_id' => $order_list[$serial_no]['shift_id'],
                        'employment_date' => $order_list[$serial_no]['employment_date'],
                        'staff_name' => $staff['staff_name'],
                        'identity' => $staff['identity'],
                        'driver_license' => $staff['driver_license'],
                        'bank_type' => $staff['bank_id'],
                        'bank_no_name' => $staff['bank_no_name'],
                        'bank_no' => $staff['bank_no'],
                        'effective_date' => $order_list[$serial_no]['effective_date'],
                        'job_title' => $order_list[$serial_no]['job_id'],
                        'sys_store_id' => $order_list[$serial_no]['store_id'],
                        'serial_no' => $serial_no
                    ];
                    OutSourcingOrderService::getInstance()->send_sms_staff_info($staff['mobile'], $param);

                    $msg = 'success';
                    array_push($value, $msg);
                    $success_count++;
                }
            }

            $excel_result = [
                'success_count' => $success_count,
                'error_count' => $error_count,
                'excel_data_error_result' => $excel_data_error_result
            ];
            /*
            Yii::$app->logger->write_log([
                'function' => '批量导入外协工单',
                'success_count' => $success_count,
                'error_count' => $error_count,
                'excel_data_error_result' => $excel_data_error_result,
                'operator_id' => $fbid
            ],'info');
            */
            return $excel_result;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('外协工单批量导入失败，可能出现的问题：' . $e->getMessage().';行号：'.$e->getLine());
            return [
                'success_count' => 0,
                'error_count' => 0,
            ];
        }
    }

    public static function ImportOrder_PH($excel_data, $permission_role, $fbid) {
        try {
            //0 工作订单id//1 身份证号//2 姓名//3 性别//4 手机号//5 外协员工类型//6 结算类型//7 外协公司//8 驾驶证号//9 车牌号//10 银行//11 银行卡持卡人//12 银行卡号
            $serial_no_arr = array_column($excel_data, '0');
            $excel_data_by_serial_no = [];
            //$currentDate = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
            //$currentDate = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
            foreach ($excel_data as $key => $value) {
                if (!empty($value[0])) {
                    $excel_data_by_serial_no[$value[0]][] = true;
                }

            }
            $order_list = HrOutsourcingOrder::find()->where(['serial_no' => $serial_no_arr])->indexBy('serial_no')->asArray()->all();

            $order_count = HrOutsourcingOrderDetail::find()
                ->select(['serial_no','count(*) as count'])
                ->where(['serial_no' => $serial_no_arr])
                ->andWhere(['is_del' => 0])
                ->groupBy('serial_no')
                ->indexBy('serial_no')
                ->asArray()
                ->all();
            //身份证号相关
            $identities = array_column($excel_data, '1');
            $identities = array_values(array_unique(array_filter($identities)));
            $existIdentities = OutSourcingOrderService::getInstance()->getConfigOrConfiguredOrderForIdentity($identities);
            //个人邮箱相关
            $personalEmails = array_column($excel_data, '13');
            $personalEmails = array_values(array_unique($personalEmails));
            $existPersonalEmail = OutSourcingOrderService::getInstance()->getConfigOrConfiguredOrder($personalEmails);
            //end个人邮箱相关
            $excel_data_error_result = [];
            $success_count = 0;
            $error_count = 0;
            foreach ($excel_data as $key => $value) {
                $serial_no = $value[0]; //外协员工工作订单ID
                $identity = trim($value[1]);  //身份证号
                $outsourcing_type = 0; //外协员工类型
                switch ($value[5]) {
                    case 1:
                        $outsourcing_type = 'individual';
                        break;
                    case 2:
                        $outsourcing_type = 'company';
                        break;
                    default:
                        $outsourcing_type = $value[5];
                }
                $pay_type = 0; //结算类型
                switch ($value[6]) {
                    case 1:
                        $pay_type = 'BY_DAY';
                        break;
                    case 2:
                        $pay_type = 'BY_MONTH';
                        break;
                    case 3:
                        $pay_type = 'CASH_PAY';
                        break;
                    default:
                        $pay_type = $value[6];
                }

                if(!isset($order_list[$serial_no])) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip9'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }
                //ph [1652]Mobile DC 个人邮箱必填
                if (empty($value[13]) && $order_list[$serial_no]['job_id']== 1652) {
                    $value[]             = Yii::$app->lang->get('personal_email_not_empty');
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }
                $staff['serial_no'] = $serial_no; //外协员工工作订单ID
                $staff['identity'] = $identity;  //身份证号
                $staff['staff_name'] = $value[2];  //员工姓名
                $staff['sex'] = empty($value[3]) ? 0 : $value[3];   //性别
                $staff['mobile'] = $value[4];  //手机号
                $staff['outsourcing_type'] = $outsourcing_type;  //外协员工类型
                $staff['pay_type'] = $pay_type;  //结算类型
                $staff['company_name_ef'] = $value[7];  //外协公司
                $staff['driver_license'] = $value[8];  //驾驶证号
                $staff['car_no'] = $value[9];  //车牌号
                $staff['bank_id'] = empty($value[10]) ? 0 : $value[10]; //银行
                $staff['bank_no_name'] = $value[11]; //持卡人姓名
                $staff['bank_no'] = $value[12]; //银行卡号
                $staff['personal_email'] = trim($value[13]); //个人邮箱

                //工单状态不正确 待生效 已生效
                if (!in_array($order_list[$serial_no]['status'], [1, 2])) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip10'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                /*
                //订单已经超过可配置时间
                if (strtotime($order_list[$serial_no]['effective_date']) < strtotime($currentDate)) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip19'));
                    $excel_data_result[] = $value;
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }
                */

                //验证权限
                if(!self::validate_edit_order($permission_role, $order_list[$serial_no]['job_id'])) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip20'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                //验证订单可配置人数是否超过文件订单人数
                $final_audit_num = $order_list[$serial_no]['final_audit_num'];
                $order_count_exist = $order_count[$serial_no]['count'] ?? 0;
                if (count($excel_data_by_serial_no[$serial_no]) > ($final_audit_num - $order_count_exist)) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip21'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                //验证提交字段
                $validate_result = OutSourcingService::getInstance()->validateOutSourcingField($staff, $order_list[$serial_no]['job_id'],$order_list[$serial_no]['hire_os_type']);
                if ($validate_result !== true) {
                    $msg = '';
                    if (($validate_result[0] ?? false)) {
                        $msg .= Yii::$app->lang->get($validate_result[0]);
                    }
                    if (($validate_result[1] ?? false)) {
                        $msg .= Yii::$app->lang->get($validate_result[1]);
                    }
                    array_push($value, $msg);
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                //验证身份证号是否已经在工单中存在
                $identity_exists = HrOutsourcingOrderDetail::find()->where(['serial_no' => $serial_no])->andWhere(['identity' => $identity])->andWhere(['is_del' => 0])->exists();
                if($identity_exists) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip1'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                //校验身份证号是否在黑名单
                $validate_out_sourcing_identity = OutSourcingService::getInstance()->validateOutSourcingIdentity($identity);
                if($validate_out_sourcing_identity !== true) {
                    array_push($value, Yii::$app->lang->get($validate_out_sourcing_identity[0]));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }

                if (!empty($staff['bank_no']) && !empty($staff['bank_id'])){
                    if(($res = StaffInfoCheck::validateBankNoStingLen($staff['bank_no'], $staff['bank_id'])) && $res !== true) {
                        array_push($value, ($res[0] ?? '') . ($res[1] ?? ''));
                        $excel_data_error_result[] = $value;
                        $error_count++;
                        continue;
                    }
                }

                //验证身份证号是否跟其他工号冲突
                $identitySerialNos = OutSourcingOrderService::getInstance()->checkEffectiveTimeDuplicated($existIdentities[$identity] ?? [],
                    $order_list[$serial_no]['effective_date'], $order_list[$serial_no]['invalid_date']);
                if ($identitySerialNos) {
                    $value[]                   = Yii::$app->lang->get('outsourcing_tip3'). implode(',', $identitySerialNos);
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }
                $existIdentities[$identity][] = [
                    'start'     => $order_list[$serial_no]['effective_date'],
                    'end'       => $order_list[$serial_no]['invalid_date'],
                    'serial_no' => $serial_no,
                ];
                //判断身份证号是否在职未完成派件未回公款
                /*
                $staff_info = StaffInfo::find()->where(['identity' => $identity])->andWhere(['formal' => 0])->andFilterWhere(['staff_type' => [2,3]])->asArray()->one();
                if($staff_info) {
                    $result_staff = self::validate_staff_info($staff_info);
                    if($result_staff !== true) {
                        array_push($value, Yii::$app->lang->get($result_staff[0]));
                        $excel_data_result[] = $value;
                        $excel_data_error_result[] = $value;
                        $error_count++;
                        continue;
                    }
                }
                */
                $is_complement = 0;
                $effective_date = $order_list[$serial_no]['effective_date'];//生效时间
                if($order_list[$serial_no]['status'] == 2) {
                    $is_complement = 1;
                    $shift = Yii::$app->sysconfig->getShift($order_list[$serial_no]['shift_id']);
                    $current_time = gmdate('Y-m-d H:i:00',time()+TIME_ADD_HOUR*3600);
                    $current_date = gmdate('Y-m-d',strtotime($current_time));
                    $effective_time = gmdate('Y-m-d H:i:00',strtotime($current_date.$shift['start'])-3600*2);//当天班次生效时间；当前班次-1小时
                    $effective_date = $effective_time;//生效时间
                }
                $invalid_date = $order_list[$serial_no]['invalid_date'];//失效时间

                //判断是否在众包外协已经报名
                $crowd_sourcing_check_list = OutSourcingOrderService::getInstance()->getCrowdSourcingTasks($value, $order_list[$serial_no]);

                $crowd_sourcing_key = $identity . '-' . strtotime($effective_date) . '-' . strtotime($invalid_date);
                if (empty($crowd_sourcing_check_list)) {
                    $crowd_sourcing_exists = false;
                } else {
                    $crowd_sourcing_exists = $crowd_sourcing_check_list[$crowd_sourcing_key]['exist'] ?? false;
                }
                if($crowd_sourcing_exists === true) {
                    array_push($value, Yii::$app->lang->get('outsourcing_tip28'));
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    continue;
                }
                //个人邮箱有值校验重复
                if ($staff['personal_email']) {
                    $personalEmailSerialNos = OutSourcingOrderService::getInstance()->checkEffectiveTimeDuplicated($existPersonalEmail[$staff['personal_email']] ?? [],
                        $order_list[$serial_no]['effective_date'], $order_list[$serial_no]['invalid_date']);
                    if ($personalEmailSerialNos) {
                        $value[]                   = str_replace('{{no}}', implode(',', $personalEmailSerialNos),
                            Yii::$app->lang->get('out_personal_email_exist'));
                        $excel_data_error_result[] = $value;
                        $error_count++;
                        continue;
                    }
                    $existPersonalEmail[$staff['personal_email']][] = [
                        'start'     => $order_list[$serial_no]['effective_date'],
                        'end'       => $order_list[$serial_no]['invalid_date'],
                        'serial_no' => $serial_no,
                    ];
                }

                $detail_model = new HrOutsourcingOrderDetail();
                $detail_model->serial_no = $serial_no;//订单编号
                $detail_model->staff_info_id = 0;//员工号
                $detail_model->identity = $staff['identity'];//身份证号
                $detail_model->staff_name = $staff['staff_name'];//员工姓名
                $detail_model->sex = $staff['sex'];//性别
                $detail_model->personal_email = $staff['personal_email'];//个人邮箱
                $detail_model->mobile = $staff['mobile'];//手机号
                $detail_model->outsourcing_type = $staff['outsourcing_type'];//外协类型
                $detail_model->pay_type = $staff['pay_type'];//结算类型
                $detail_model->company_name_ef = $staff['company_name_ef'];//公司名称
                $detail_model->driver_license = $staff['driver_license'];//驾驶证号
                $detail_model->bank_id = $staff['bank_id'];//银行卡
                $detail_model->bank_no_name = $staff['bank_no_name'];//持卡人姓名
                $detail_model->bank_no = $staff['bank_no'];//银行卡号
                $detail_model->car_no = $staff['car_no'];//车牌号
                $detail_model->effective_date = $effective_date;//生效时间
                $detail_model->invalid_date = $invalid_date;//失效时间
                $detail_model->is_complement = $is_complement;
                $detail_model->create_staff_info_id = $fbid;

                if (!$detail_model->save()) {
                    $msg = json_encode($detail_model->getErrors(), JSON_UNESCAPED_UNICODE);
                    array_push($value, $msg);
                    $excel_data_error_result[] = $value;
                    $error_count++;
                    Yii::$app->logger->write_log('外协工单员工信息保存失败，可能出现的问题：' . $msg . ';工单id：' . $serial_no, 'info');
                } else {
                    $param = [
                        'shift_id' => $order_list[$serial_no]['shift_id'],
                        'employment_date' => $order_list[$serial_no]['employment_date'],
                        'staff_name' => $staff['staff_name'],
                        'identity' => $staff['identity'],
                        'driver_license' => $staff['driver_license'],
                        'bank_type' => $staff['bank_id'],
                        'bank_no_name' => $staff['bank_no_name'],
                        'bank_no' => $staff['bank_no'],
                        'effective_date' => $effective_date,
                        'job_title' => $order_list[$serial_no]['job_id'],
                        'sys_store_id' => $order_list[$serial_no]['store_id'],
                        'serial_no' => $serial_no
                    ];
                    if($order_list[$serial_no]['status'] == 2) {
                        //菲律宾立即生效
                        $effective_staff_result = self::effective_staff_ph($serial_no, $staff, $fbid);
                        if($effective_staff_result !== true) {
                            $msg = [];
                            if(($effective_staff_result[0] ?? false)) {
                                $msg[] = Yii::$app->lang->get($effective_staff_result[0]);
                            }
                            if(($effective_staff_result[1] ?? false)) {
                                $msg[] = Yii::$app->lang->get($effective_staff_result[1]);
                            }
                            array_push($value, implode(',',$msg));
                            $excel_data_error_result[] = $value;
                            $error_count++;
                            Yii::$app->logger->write_log('外协工单员工信息立即生效，可能出现的问题：' . json_encode($msg) . ';工单id：' . $serial_no . ';参数：'.json_encode($staff), 'info');
                        } else {
                            //发送短信 手机号/短信内容
                            OutSourcingOrderService::getInstance()->send_sms_staff_info($staff['mobile'], $param);

                            $msg = 'success';
                            array_push($value, $msg);
                            $success_count++;
                        }
                    } else {
                        //发送短信 手机号/短信内容
                        OutSourcingOrderService::getInstance()->send_sms_staff_info($staff['mobile'], $param);

                        $msg = 'success';
                        array_push($value, $msg);
                        $success_count++;
                    }
                }
            }
            $excel_result = [
                'success_count' => $success_count,
                'error_count' => $error_count,
                'excel_data_error_result' => $excel_data_error_result
            ];
            Yii::$app->logger->write_log([
                'function' => '批量导入外协工单',
                'success_count' => $success_count,
                'error_count' => $error_count,
                'excel_data_error_result' => $excel_data_error_result,
                'operator_id' => $fbid
            ],'info');
            return $excel_result;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('外协工单批量导入失败，可能出现的问题：' . $e->getMessage().';行号：'.$e->getLine());
            return [
                'success_count' => 0,
                'error_count' => 0,
                'excel_data_result' => []
            ];
        }
    }

    public static function validate_edit_order($permission_role, $job_id) {
        if(in_array('OUTSOURCING_EDIT_A', $permission_role) && in_array($job_id, [13, 110, 452, 1000, 1199, 1194, 1652])) {
            return true;
        }
        if(in_array('OUTSOURCING_EDIT_B', $permission_role) && in_array($job_id, [98, 111, 37,300])) { //DC 仓管
            return true;
        }
        if(in_array('OUTSOURCING_EDIT_C', $permission_role) && in_array($job_id, [271, 807, 1474, 1475]) ) { //Hub 仓管
            return true;
        }
        if(in_array('SUPER_ADMIN', $permission_role)) {
            return true;
        }
        return false;
    }

    /**
     * @description: 获取片区和已配置人数
     * <AUTHOR> L.J
     * @time       : 2022/8/22 17:03
     */
    public static function getPackagingItems($list=[]): array {
        $result = ['config_count_list'=>[],'piece'=>[],'shift_list'=>''];
        if(empty($list)){
            return $result;
        }
        //获取已配置人数
        //获取所有的审批编号
        $serial_no_arr = array_values(array_unique(array_column($list, 'serial_no')));
        $serial_no_arr = array_chunk($serial_no_arr, 400);
        foreach($serial_no_arr  as $serial_no ){
            //查询所有的订单
            $res = HrOutsourcingOrderDetail::find()
                                                         ->select(['COUNT(id) AS count', 'serial_no'])
                                                         ->where(['IN', 'serial_no', $serial_no])
                                                         ->andWhere(['is_del' => 0])
                                                         ->groupBy('serial_no')
                                                         ->asArray()
                                                         ->all();
            if (!empty($res)) {
                $result['config_count_list'] = array_merge($result['config_count_list'], array_column($res, 'count','serial_no'));
            }
        }

        //获取片区
        $piece = array_values(array_unique(array_column($list, 'manage_piece')));
        if (isset($piece) && $piece) {
            $piece = SysManagePiece::find()
                                   ->select(['id', 'name'])
                                   ->where(['IN', 'id', $piece])
                                   ->asArray()
                                   ->all();
            $result['piece'] = array_column($piece, 'name', 'id');
        }

        //获取班次
        $shift_group = YII_COUNTRY == 'TH' ? [HrShift::SHIFT_GROUP_FULL_DAY_SHIFT, HrShift::SHIFT_GROUP_HALF_DAY_SHIFT, HrShift::SHIFT_GROUP_HUB_OUTSOURCE_SHIFT_4, HrShift::SHIFT_GROUP_HUB_OUTSOURCE_SHIFT_5] : HrShift::SHIFT_GROUP_FULL_DAY_SHIFT;
        $shift_group = YII_COUNTRY == 'PH' ? [HrShift::SHIFT_GROUP_FULL_DAY_SHIFT, HrShift::SHIFT_GROUP_HALF_DAY_SHIFT] : $shift_group;
        $result['shift_list'] = HrShift::find()->where(['shift_group' => $shift_group])->indexBy('id')->asArray()->all();
        return $result;

    }

    public static function getCrowdSourcingCheckList($params) {
        try {
            $url = Yii::$app->params['fms_svc_url'] . '/svc/crowdsourcing/check/tasks';
            $res = Yii::$app->http->httpPost($url, $params);

            Yii::$app->logger->write_log([
                'function' => 'getCrowdSourcingCheckList',
                'params' => $params,
                'result' => $res,
            ],'info');

            $return_data = [];
            if(isset($res['code']) && $res['code'] == 1) {
                $data = $res['data'] ?? [];
                if(!empty($data)) {
                    foreach ($data as $key => $value) {
                        $data[$key]['key'] = trim($value['cardNum']) . '-' . $value['startTime'] . '-' . $value['endTime'];
                    }

                    $return_data = array_column($data, null, 'key');
                }
            } else {
                Yii::$app->logger->write_log([
                    'function' => 'getCrowdSourcingCheckList',
                    'message' => '调用众包校验接口数据返回异常',
                    'params' => $params,
                    'result' => $res
                ]);
            }
            return $return_data;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'getCrowdSourcingCheckList',
                'message' => '可能出现的问题:' .$e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params
            ]);
            return [];
        }

    }
}











