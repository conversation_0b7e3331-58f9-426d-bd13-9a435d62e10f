<?php

namespace app\modules\v1\config;

use app\models\backyard\Banklist;
use app\models\backyard\HrJobDepartmentRelation;
use app\models\backyard\RoleEnums;
use app\models\backyard\SettingEnv;
use app\models\backyard\StaffLeaveReason;
use app\models\fle\SysDepartment;
use app\models\fle\SysStore;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HrDepartmentJobTitleLevelGrade;
use app\models\manage\HrDepeartmentJobTitle;
use app\models\manage\HrJobTitle;
use app\models\manage\HrJobTitleRole;
use app\models\backyard\HrShift;
use app\models\manage\SysCity;
use app\models\manage\SysDistrict;
use app\models\manage\SysProvince;
use app\models\manage\SysStoreTemp;
use app\modules\v1\business\SysGroupDept;
use app\modules\v1\business\SysGroupDeptV2;
use Yii;

/**
 * @deprecated 已废弃
 */
class SysConfig extends \yii\base\BaseObject
{
    public $version = 190404;
    public $allDepeartments;
    public $allDepeartments_tree;
    public $depeartment;
    public $jobTitle;
    public $group_dept_tree_list;
    public $group_dept_tree_list_v2;

    const RESIDENCE_BOOKLET_AUDIT = 121;//导出字段：户口簿审核状态
    const POSITION_TYPE = 123;//职位性质
    const COST_TYPE = 124;//成本类型
    const EXPORT_FIELD_PROJECT_NUM = 125;
    const EXPORT_FIELD_VAN_CONTAINER_STATE = 126;//快递员车厢状态
    const EXPORT_FIELD_VAN_CONTAINER_START = 127;//快递员车厢补贴开始时间
    const EXPORT_FIELD_VAN_CONTAINER_END = 128;//快递员车厢补贴结束时间
    const EXPORT_FIELD_CONTRACT_COMPANY = 129;//合同公司
    const EXPORT_FIELD_AGE = 130;//年龄

    public $areaManager;
    public $depeartmentManager;
    public function all_bank_type()
    {
        $list = Banklist::find()->select(['bank_id','bank_name'])->orderBy(['sort_num' => SORT_DESC,'bank_id' => SORT_DESC])->asArray()->all();
        return array_column($list,'bank_name','bank_id');
    }

    //银行卡类型
    public $bank_type = [
        '1' => 'TTB',
        '2' => 'SCB'
    ];

    public $bank_type_ph = [
        '21' => 'UB',//菲律宾 UB
    ];

    // 老挝
    public $bank_type_la = [
        '53' => 'BCEL'
    ];

    // 马来西亚
    public $bank_type_my = [
        '15' => 'CIMB',
        '54' => 'Maybank',
        '83' => 'BSN',
    ];

    // 印度尼西亚-银行卡选项
    public $bank_type_id = [
        '55' => 'BCA',//印尼银行卡
    ];

    // 印度尼西亚-银行卡选项
    const BANK_TYPE_ID_BCA = 55;//BCA
    const BANK_TYPE_ID_OCBC = 78;//OCBC

//    public $outsourcing_search_job_title = [13, 98, 110, 111, 271, 452, 473];

    public $outsourcing_search_job_title_ph = [13, 110, 111, 452, 807, 812, 1000];

    public $outsourcing_search_job_title_my = [13, 98, 110, 111, 271, 452, 473, 1000, 1199];

    public $outsourcing_search_job_title_la = [13, 110, 111, 271, 812];

    //邮箱后缀
    public $config_email_suffix = [
        '1' => '@flashexpress.com',
        '2' => '@flashpay.com',
        '3' => '@flashlogistics.co.th',
        '4' => '@flashfulfillment.co.th',
        '5' => '@flashfin.com',
        '6' => '@flashmoney.co.th',
//        '7' => '@flashfulfillment.co.th',
        '8' => '@flashfulfillment.my',
        '9' => '@flashfulfillment.ph',
        '10' => '@flashfulfillment.vn',
        '11' => '@flashexpress.id',
        '12' => '@flashexpress.la',
        '13' => '@flashexpress.my',
        '14' => '@flashexpress.ph',
        '15' => '@flashexpress.vn',
        '16' => '@futurecommerce.cn',
        '17' => '@flashdesk.ai',
        '18' => '@flashhome.co.th',
    ];

    // ph 菲律宾
    public $config_email_ph_suffix = [
        '1' => '@flashexpress.ph',
        '2' => '@flashfulfillment.ph',
        '3' => '@flashexpress.com',
        '4' => '@flashdesk.ai',
    ];

    // la 老挝
    public $config_email_la_suffix = [
        '1' => '@flashexpress.la',
        '2' => '@flashexpress.com',
        '3' => '@flashdesk.ai',
    ];

    // my 马来西亚
    public $config_email_my_suffix = [
        '1' => '@flashexpress.my',
        '2' => '@flashfulfillment.my',
        '3' => '@flashexpress.com',
        '4' => '@flashdesk.ai',
    ];

    // vn 越南
    public $config_email_vn_suffix = [
        '1' => '@flashexpress.vn',
        '2' => '@flashfulfillment.vn',
        '3' => '@flashexpress.com',
        '4' => '@flashdesk.ai',
    ];
     // id 印度尼西亚
    public $config_email_id_suffix = [
        '1' => '@flashexpress.id',
        '2' => '@flashexpress.com',
        '3' => '@flashdesk.ai',
    ];


    //职等
    public $config_job_title_level = [
        1 => 'Staff',
        2 => 'Supervisor',
        3 => 'Manager',
        4 => 'Executive'
    ];

    public $config_job_title_grade_v2 = [
        0 => 'F0',
        12 => 'F12',
        13 => 'F13',
        14 => 'F14',
        15 => 'F15',
        16 => 'F16',
        17 => 'F17',
        18 => 'F18',
        19 => 'F19',
        20 => 'F20',
        21 => 'F21',
        22 => 'F22',
        23 => 'F23',
        24 => 'F24'
    ];



    //种族 id值和winhr保持一直
    public $staff_race = [
        3 => 'race_3', //马来人
        2 => 'race_2', //华人
        14 => 'race_14', //印度人
        99 => 'race_99', //其他
    ];

    //宗教 id值和winhr保持一直
    public $staff_religion = [
        1 => 'religion_1', //佛教
        2 => 'religion_2', //伊斯兰教
        3 => 'religion_3', //天主教
        4 => 'religion_4', //基督教
        6 => 'religion_6', //印度教
        5 => 'religion_5', //其他
    ];

    public $out_sourcing_courier_job_title;//外协快递员职位
    public $out_sourcing_store_keeper_job_title;//外协仓管员职位

    /**
     * 注意 不要再增加 查询了 !!!!!!!!!!!
     */
    public function init()
    {
        //初始化leave_type_reasson，查表，替代公共属性
        //增加缓存层
        $this->leave_type_reasson = Yii::$app->cache->getOrSet('conf.leave_type_reasson', function ($cache) {
            $list = StaffLeaveReason::find()->select(['type','code'])->where(['deleted'=>0])->asArray()->all();
            $res = [];
            foreach($list as $key=>$val){
                $res[$val['type']][] = (int)$val['code'];
            }
            return $res;
        }, YII_ENV == 'dev' ? 5 : 60 * 60);
        //
        [$dept_list,$all_dept_list] = Yii::$app->cache->getOrSet('conf.all_department_list', function ($cache) {
            //return SysGroupDept::getAllDepartmentList();
            return SysGroupDeptV2::getAllDepartmentList();
        }, YII_ENV == 'dev' ? 5 : 10 * 60);

        //
        $this->group_dept_tree_list = Yii::$app->cache->getOrSet('conf.group_dept_tree_list', function ($cache) {
            return SysGroupDept::getGroupDeptList();
        }, YII_ENV == 'dev' ? 5 : 10 * 60);

        $this->group_dept_tree_list_v2 = Yii::$app->cache->getOrSet('conf.group_dept_tree_list_v2', function ($cache) {
            return SysGroupDeptV2::getGroupDeptList();
        }, YII_ENV == 'dev' ? 5 : 10 * 60);

        //以后要废弃
        $this->allDepeartments_tree = Yii::$app->cache->getOrSet('conf.allDepeartments_tree', function ($cache) {
            return SysGroupDept::getAllDepartmentTree();
        }, YII_ENV == 'dev' ? 5 : 10 * 60);

        $this->allDepeartments = $all_dept_list;
        $this->depeartment = $dept_list;

        $managers = Yii::$app->cache->getOrSet('conf.default.department.manager', function ($cache) {
            $ids = array_merge(array_values(AreaManager::$department), array_values(AreaManager::$config));
            $staffInfos = BaseStaffInfo::find()->where(['staff_info_id' => $ids])->indexBy('staff_info_id')->asArray()->all();

            $department = array_filter(AreaManager::$department, function ($v) use ($staffInfos) {
                return !isset($staffInfos[$v]) || $staffInfos[$v]['state'] == 1;
            });

            $area = array_filter(AreaManager::$department, function ($v) use ($staffInfos) {
                return !isset($staffInfos[$v]) || $staffInfos[$v]['state'] == 1;
            });

            return ['department' => $department, 'area' => $area];
        }, 10 * 60);

        // 部门主管
        $this->depeartmentManager = $managers['department'] ?? [];
        // 区域主管
        $this->areaManager = $managers['area'];
        // 职位
        $this->jobTitle = Yii::$app->cache->getOrSet('conf.job.title', function ($cache) {
            return HrJobTitle::find()
                ->select('job_name')
                ->where(['status' => 1])
                ->indexBy('id')
                ->orderBy(['job_name' => SORT_ASC])
                ->asArray()
                ->column();
        }, 10 * 60);

        //外协快递员职位
        $this->out_sourcing_courier_job_title = Yii::$app->cache->getOrSet('conf.out_sourcing.courier.job_title', function ($cache) {
            $out_sourcing_courier = SettingEnv::find()->where(['code' => 'out_sourcing_courier'])->one();
            $job_title_ids = [];
            if(!empty($out_sourcing_courier) && $out_sourcing_courier->set_val){
                $job_title_ids = explode(',',$out_sourcing_courier->set_val);
            }
            return $job_title_ids;
        }, 5 * 60);
        //外协仓管员职位
        $this->out_sourcing_store_keeper_job_title = Yii::$app->cache->getOrSet('conf.out.sourcing.store.keeper.job.title', function ($cache) {
            $out_sourcing_courier = SettingEnv::find()->where(['code' => 'out_sourcing_store_keeper'])->one();
            $job_title_ids = [];
            if(!empty($out_sourcing_courier) && $out_sourcing_courier->set_val){
                $job_title_ids = explode(',',$out_sourcing_courier->set_val);
            }
            return $job_title_ids;
        }, 5 * 60);

    }

    public function _treeNode($data,$pk = 'ancestry',$parentId = 0) {
        $node = [];
        foreach ($data as $key => $value) {
            if($parentId == $value[$pk]) {
                $node[] = [
                    'value' => $value['id'],
                    'label' => $value['name'],
                    'children' => $this->_treeNode($data,$pk,$value['id'])
                ];
            }
        }
        return $node;
    }

    public function _getNodeLevel($data,$pk = 'ancestry',$id = 0,&$r) {
        foreach ($data as $key => $value) {
            if($value['id'] == $id && $value[$pk] != 1) {
                $r[] = (int)$value[$pk];
                $this->_treeNode($data,$pk,$value[$pk],$r);
            }
        }
        return $r;
    }

    public function getIdRole($role = null)
    {
        if (is_null($role)) {
            return array_keys($this->getPosition());
        }
        foreach ($this->getPosition() as $ind => $pos) {
            if ($pos == $role) {
                return $ind;
            }
        }
        return null;
    }

    public function relationDepartmentJobTitle($refresh = false)
    {
        $relations = [];
        foreach (HrDepeartmentJobTitle::find()->all() as $key => $row) {
            $relations[$row->sys_depeartment_id][] = $row->job_title_id;
        }
        return $relations;
    }

    public function relationDepartmentJobTitleV2() {
        $list = HrJobDepartmentRelation::find()->all();
        $relations = [];
        foreach ($list as $key => $row) {
            $relations[$row->department_id][] = $row->job_id;
        }
        return $relations;
    }

    public function relationJobTitlePosition($refresh = false)
    {
        $relations = [];
        foreach (HrJobTitleRole::find()->all() as $key => $row) {
            $relations[$row->sys_depeartment_id . '_' . $row->job_title_id][] = $row->role_id;
        }
        return $relations;
    }

    public function relationJobTitleLevelAndGrade($refresh = false)
    {

        $relations = [];
        foreach (HrDepartmentJobTitleLevelGrade::find()->all() as $key => $row) {
            $relations[$row->department_id . '_' . $row->job_title_id]['level'] = $row->job_title_level;
            $relations[$row->department_id . '_' . $row->job_title_id]['grade'] = $row->job_title_grade;
        }
        return $relations;
    }

    public function getIdJobTitle($name)
    {
        $name = trim(strtolower(str_replace(' ', '', $name)));
        foreach ($this->jobTitle as $key => $jt) {
            $jt = trim(strtolower(str_replace([' '], '', $jt)));
            if ($name == $jt) {
                return $key;
            }
        }
        return false;
    }

    public function getIdDepartment($name)
    {
        $name = trim(strtolower(str_replace(' ', '', $name)));
        foreach ($this->allDepeartments as $key => $jt) {
            $jt = trim(strtolower(str_replace([' '], '', $jt)));
            if ($name == $jt) {
                return $key;
            }
        }
        return false;
    }

    public function getAreas()
    {
        return AreaManager::$areas;
    }

    //马来区域
    public function getAreasMy()
    {
        return AreaManager::$areasMy;
    }
    //老挝区域
    public function getAreasLa()
    {
        return AreaManager::$areasLa;
    }

    // 获取网点所属区域 没有调用了
    public function getAreaByStore($storeId)
    {
        $province = '';
        foreach (SysStoreTemp::temp() as $sid => $store) {
            if ($sid == $storeId) {
                $province = $store['province_code'];
                break;
            }
        }

        if (!$province) {
            return '';
        }

        foreach (AreaManager::getAreaProvince() as $id => $provinces) {
            if (in_array($province, $provinces)) {
                return ['id' => $id, 'name' => AreaManager::$areas[$id]];
            }
        }
        return [];
    }

    //根据省编码获取所属区域
    public function getAreaByProvinceCode($province_code) {
        foreach (AreaManager::getAreaProvince() as $id => $provinces) {
            if (in_array($province_code, $provinces)) {
                return ['id' => $id, 'name' => AreaManager::$areas[$id]];
            }
        }
        return [];
    }

    // 根据区域获取网点列表
    public function getStoresByArea(...$str)
    {
        $ths = [];
        foreach ($str as $name) {
            $ths = array_merge($ths, AreaManager::getAreaProvince($name));
        }

        $stores = [];
        foreach (SysStoreTemp::temp() as $storeId => $store) {
            if (in_array($store['province_code'], $ths)) {
                $stores[] = $storeId;
            }
        }
        return $stores;
    }

    /**
     * my,la,根据区域获取网点
     * @param $area
     * @param $str
     * @return array
     */
    public function getStoresBySortingNo($area,...$str)
    {
        $ths = [];
        foreach ($str as $name) {
            if (isset($area[$name])) {
                array_push($ths, $area[$name]);
            }
        }
        $stores = [];
        foreach (SysStoreTemp::temp() as $storeId => $store) {
            if (in_array($store['sorting_no'], $ths)) {
                $stores[] = $storeId;
            }
        }
        return $stores;
    }

    /**
     * my,la,根据区域获取网点
     * @param $area
     * @param $str
     * @return array
     */
    public function getStoresBySortingNoV2($area,$str)
    {
        $ths = [];
        foreach ($str as $name) {
            if (isset($area[$name])) {
                array_push($ths, $area[$name]);
            }
        }
        $stores = [];
        foreach (SysStoreTemp::temp() as $storeId => $store) {
            if (in_array($store['sorting_no'], $ths)) {
                $stores[] = $storeId;
            }
        }
        return $stores;
    }

    // 根据区域获取网点列表 一个参数
    public function getStoresByAreaV2($str)
    {
        $ths = [];
        foreach ($str as $name) {
            $ths = array_merge($ths, AreaManager::getAreaProvince($name));
        }

        $stores = [];
        foreach (SysStoreTemp::temp() as $storeId => $store) {
            if (in_array($store['province_code'], $ths)) {
                $stores[] = $storeId;
            }
        }
        return $stores;
    }



    // 根据网点名称获取网点ID
    public function getStoreIdByName($name)
    {
        foreach (SysStoreTemp::temp() as $row) {
            $pov = strtolower(str_replace(' ', '', $row['name']));
            $search = strtolower(str_replace(' ', '', $name));
            if ($pov == $search || strpos($pov, $search) === 0) {
                return $row['id'];
            }
        }
        return '';
    }

    // 根据网点获取网点详情
    public function getStore($id)
    {
        return SysStoreTemp::temp()[$id] ?? [];
    }

    // 获取班次

    /**
     * @param int $jobId
     * @return array
     */
    public function getShifts($jobId = 0)
    {
        $result = [];
        //2021 10 29 取消限制 刘佳雪
//        if ($jobId == 452) {
//            $notInIds = [35,36,37,38,39,40,41,42,43,44,45,46,47,48,49];
//        } else {
//            $notInIds = [35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50];
//        }
        $shift_list = HrShift::find()->where(['shift_group' => HrShift::SHIFT_GROUP_FULL_DAY_SHIFT])->orderBy('start')->all();
        foreach ($shift_list as $obj) {
            $result[$obj->type][] = [
                'start' => $obj->start,
                'end' => $obj->end,
                'id' => $obj->id,
                'type' => $obj->type,
                'markup' => '(' . Yii::$app->lang->get('shift_' . $obj->type) . ') ' . $obj->start . '-' . $obj->end,
            ];
        }
        ksort($result);
        return $result;
    }

    //获取所有班次
    // 移至  ShiftService::getInstance()->getAllShifts()
    public function getAllShifts() {
        $result = [];
        $shift_list = HrShift::find()->where(['shift_group' => HrShift::SHIFT_GROUP_FULL_DAY_SHIFT])->orderBy('start')->all();
        foreach ($shift_list as $obj) {
            $result[$obj->type][] = [
                'start' => $obj->start,
                'end' => $obj->end,
                'id' => $obj->id,
                'type' => $obj->type,
                'markup' => '(' . Yii::$app->lang->get('shift_' . $obj->type) . ') ' . $obj->start . '-' . $obj->end,
            ];
        }
        ksort($result);
        return $result;
    }

    // 获取班次详情
    public function getShift($id)
    {
        $shift = HrShift::findOne($id);
        return $shift->getAttributes();
    }

    public function getShiftV2($id) {
        $shift = HrShift::find()->where(['id' => $id])->asArray()->one();
        return $shift;
    }

    //企业邮箱后缀列表
    public function getEmailSuffixList() {
        $email_suffix = [];
        $country_code = strtolower(YII_COUNTRY);
        $config_email_suffix = $this->config_email_suffix;
        $reflectClass = new \ReflectionClass($this);
        $propeties = $reflectClass->getProperties();
        foreach ($propeties as $property) {
            if ('config_email_' . $country_code . '_suffix' == $property->getName()) {
                $config_email_suffix = $property->getValue($this);
            }
        }
        foreach ($config_email_suffix as $item=>$value) {
            $email_suffix[] = ['key' => $item, 'value' => $value];
        }
        return $email_suffix;
    }

    //获取当前部门的一级部门ID
    public function getDeparmentLevel1Departmentid($id){
        $detail = SysDepartment::find()->where(['id' => $id])->asArray()->one();
        if(empty($detail)) return 0;
        $deparment_index_map = explode('/', $detail['ancestry_v2']);// 数组键值对关系：部门级别（索引位置）=>部门ID
        if($detail['type']== 2){
            return $deparment_index_map[1];
        }elseif($detail['type'] == 3){
            return $deparment_index_map[0];
        }
        return 0;

    }


    // 获取区域管辖网点通过部门
    // a.FLASH区域经理的全部管辖网点排除掉FH（加盟商网点）和3PL；
    // b.SHOP PROJECT部门的区域经理只能看到SHOP-PICKUP ONLY（shop揽件网点市场）和SHOP-PICKUP&DELIVERY（shop收派件网点）；
    // c.OPERATION部门的区域经理只能看到DC（分拨中心）和SP（收派件网点）；
    // d.U PROJECT部门的区域经理只能看到 U PROJECT（大学门店网点你）；
    // e.HUB部门的区域经理只能看见HUB网点；
    // f. Admin & Procurement和Line haul & Transportation部门的区域经理，管辖规则业务需求待定，此版本不做变更；
    public function getAreaStroresByDepartment($departmentId, ...$areaIds)
    {
        $stores = [];
        $departmentStores = Yii::$app->cache->getOrSet('area.stores.by.department'.$departmentId, function ($cache) use ($departmentId) {
            $departmentStores = [];
            foreach (SysStoreTemp::temp() as $storeId => $store) {
                // a.FLASH区域经理的全部管辖网点排除掉FH（加盟商网点）和3PL；
                if (in_array($store['category'], [6]) || $store['id'] == 'TH01080102') {
                    continue;
                }

                foreach (AreaManager::getAreaProvince() as $areadId => $provinces) {
                    switch ($departmentId) {
                        case 13: // SHOP PROJECT部门的区域经理只能看到SHOP-PICKUP ONLY（shop揽件网点市场）和SHOP-PICKUP&DELIVERY（shop收派件网点）； U PROJECT部门的区域经理只能看到 U PROJECT（大学门店网点你）
                            if (in_array($store['category'], [4, 5, 7]) && in_array($store['province_code'], $provinces)) {
                                $departmentStores[$departmentId][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;
                        case 4: //OPERATION部门的区域经理只能看到DC（分拨中心）和SP（收派件网点）；
                            if (in_array($store['category'], [1, 2]) && in_array($store['province_code'], $provinces)) {
                                $departmentStores[$departmentId][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;
                        case 14: // U PROJECT部门的区域经理只能看到 U PROJECT（大学门店网点你）；
                            if (in_array($store['category'], [7]) && in_array($store['province_code'], $provinces)) {
                                $departmentStores[$departmentId][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;
                        case 25: // .HUB部门的区域经理只能看见HUB网点；
                            if (in_array($store['category'], [8]) && in_array($store['province_code'], $provinces)) {
                                $departmentStores[$departmentId][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;
                        default:
                            $departmentStores[$departmentId][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            break;
                    }
                }
            }
            return $departmentStores;
        }, 1);
        foreach ($areaIds as $areaId) {
            $stores = array_merge($stores, $departmentStores[$departmentId][$areaId] ?? []);
        }
        return $stores;
    }

    /**
     * 获取属于区域经理管辖区域内且属于其所在一级部门对应网点类型下的所有网点
     * @param $departmentId 用户所属部门ID
     * @param mixed ...$areaIds 区域ID列表
     * @return array
     */
    public function getAreaStroresByDepartmentv2($departmentId, ...$areaIds)
    {
        $stores = [];
        //当前部门ID对应的一级节点部门ID
        //$level1_depatment_id = $this->getDeparmentLevel1Departmentid($departmentId);
        $level1_depatment_id = SysGroupDeptV2::SearchSysDeptId($departmentId);

        $departmentStores = Yii::$app->cache->getOrSet('area.stores.by.department'.$level1_depatment_id, function ($cache) use ($level1_depatment_id) {
            $departmentStores = [];
            $sysStores = SysStoreTemp::temp();
            foreach ($sysStores as $storeId => $store) {

                foreach (AreaManager::getAreaProvince() as $areadId => $provinces) {
                    switch ($level1_depatment_id) {

                        case 4: //Network management 部门的区域经理只能看到 SP[1]、DC[2]、BDC[10] 网点类型下的网点；
                            if (in_array($store['category'], [1, 2, 10]) && in_array($store['province_code'], $provinces)) {
                                //$departmentStores[$dpId][$areadId][] = $store['id'];
                                $departmentStores[$level1_depatment_id][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;
                        case 13: // Shop management 部门的区域经理只能看到Shop（pickup only）[4]、Shop（Pickup &Delivery）[5]、Ushop[7] 网点类型下的网点
                            if (in_array($store['category'], [4, 5, 7]) && in_array($store['province_code'], $provinces)) {
                                //$departmentStores[$dpId][$areadId][] = $store['id'];
                                $departmentStores[$level1_depatment_id][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;

                        case 25: // Fulfillment部门部门的区域经理只能看见HUB 网点类型下的网点；
                            if (in_array($store['category'], [8]) && in_array($store['province_code'], $provinces)) {
                                //$departmentStores[$dpId][$areadId][] = $store['id'];
                                $departmentStores[$level1_depatment_id][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;
                        case 15: // U PROJECT部门的区域经理只能看到 Fulfillment[11] 网点类型下的网点；
                            if (in_array($store['category'], [11]) && in_array($store['province_code'], $provinces)) {
                                //$departmentStores[$dpId][$areadId][] = $store['id'];
                                $departmentStores[$level1_depatment_id][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;

                         case 18: // Flash home部门的区域经理只能看到 FH[6] 网点类型下的网点；
                            if (in_array($store['category'], [6]) && in_array($store['province_code'], $provinces)) {
                                //$departmentStores[$dpId][$areadId][] = $store['id'];
                                $departmentStores[$level1_depatment_id][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;
                         case 95: // Onsite management部门的区域经理只能看到 OS[9] 网点类型下的网点；
                            if (in_array($store['category'], [9]) && in_array($store['province_code'], $provinces)) {
                                //$departmentStores[$dpId][$areadId][] = $store['id'];
                                $departmentStores[$level1_depatment_id][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;
                        case 65: //Flash Freight Hub部门的区域经理只能看到 B-Hub[12] 网点类型下的网点；
                            if (in_array($store['category'], [12]) && in_array($store['province_code'], $provinces)) {
                                //$departmentStores[$dpId][$areadId][] = $store['id'];
                                $departmentStores[$level1_depatment_id][$areadId][$store['id']] = ["id" => $store['id'],'name' => $store['name']];
                            }
                            break;
                        default:
                            break;
                    }
                }
            }

            return $departmentStores;
        }, 600);
        //循环出区域经理管理区域中符合网点类型的区域下的网点
        foreach ($areaIds as $areaId) {
            $stores = array_merge($stores, $departmentStores[$level1_depatment_id][$areaId] ?? []);
        }
        return $stores;
    }
    /**
     * 获取属于大区经理或片区经理管辖区域内且属于其所在一级部门对应网点类型下的所有网点
     * @param $departmentId 用户所属部门ID
     * @param mixed ...$areaIds 区域ID列表
     * @return array
     */
    public function getCategoryStroresByDepartment($departmentId)
    {

        //当前部门ID对应的一级节点部门ID
        //$level1_depatment_id = $this->getDeparmentLevel1Departmentid($departmentId);
        $level1_depatment_id = SysGroupDeptV2::SearchSysDeptId($departmentId);
        $departmentStores = [];
        $sysStores = SysStoreTemp::temp();
        foreach ($sysStores as $storeId => $store) {
            switch ($level1_depatment_id) {

                case 4: //Network management 部门的区域经理只能看到 SP[1]、DC[2]、BDC[10] 网点类型下的网点；
                    if (in_array($store['category'], [1, 2,10])) {
                        $departmentStores[$storeId] = ["id" => $store['id'],'name' => $store['name']];
                    }
                    break;
                case 13: // Shop management 部门的区域经理只能看到Shop（pickup only）[4]、Shop（Pickup &Delivery）[5]、Ushop[7] 网点类型下的网点
                    if (in_array($store['category'], [4, 5, 7])) {
                        $departmentStores[$storeId] = ["id" => $store['id'],'name' => $store['name']];
                    }
                    break;

                case 25: // Fulfillment部门部门的区域经理只能看见HUB 网点类型下的网点；
                    if (in_array($store['category'], [8])) {
                        $departmentStores[$storeId] = ["id" => $store['id'],'name' => $store['name']];
                    }
                    break;
                case 15: // U PROJECT部门的区域经理只能看到 Fulfillment[11] 网点类型下的网点；
                    if (in_array($store['category'], [11]) ) {
                        $departmentStores[$storeId] = ["id" => $store['id'],'name' => $store['name']];
                    }
                    break;

                case 18: // Flash home部门的区域经理只能看到 FH[6] 网点类型下的网点；
                    if (in_array($store['category'], [6])) {
                        $departmentStores[$storeId] = ["id" => $store['id'],'name' => $store['name']];
                    }
                    break;
                case 95: // Onsite management部门的区域经理只能看到 OS[9] 网点类型下的网点；
                    if (in_array($store['category'], [9])) {
                        $departmentStores[$storeId] = ["id" => $store['id'],'name' => $store['name']];
                    }
                    break;
                case 65: //Flash Freight Hub部门的区域经理只能看到 B-Hub[12] 网点类型下的网点；
                    if (in_array($store['category'], [12])) {
                        $departmentStores[$storeId] = ["id" => $store['id'],'name' => $store['name']];
                    }
                    break;
                default:
                    break;
            }
        }

        return $departmentStores;
    }
    public $leave_type_reasson = [
                1 => [1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 26, 27, 28, 29, 32, 99],
                2 => [18, 19, 20, 21, 25],
                3 => [22, 23, 24],
                4 => [30, 31],
            ];
    public function getLeaveTypeReason($is_agent = false) {
        $r = [];
        $list = StaffLeaveReason::find()->select(['type','code','t_key','deleted','group_type'])->asArray()->all();
        $leave_type_reasson = array_values(array_unique(array_column($list,'type')));
        foreach($leave_type_reasson as $key=>$val){
            $leave_type = [
                'key' => (int)$val,
                'value' => Yii::$app->lang->get('leave_type_' . $val)
            ];
            foreach ($list as $k=>$v){
                if ($is_agent){
                    if ($val==$v['type'] && in_array($v['group_type'],array_merge(StaffLeaveReason::$agent_leave_reason,[StaffLeaveReason::GROUP_TYPE_DEFAULT])) && $val == StaffLeaveReason::STAFF_LEAVE_TYPE_1){
                        $leave_type['child'][] = [
                            'key' => (int)$v['code'],
                            'value' => Yii::$app->lang->get( $v['t_key'])
                        ];
                    }elseif ($val==$v['type'] && $val != StaffLeaveReason::STAFF_LEAVE_TYPE_1 && !in_array($v['group_type'],StaffLeaveReason::$agent_leave_reason)){
                        $leave_type['child'][] = [
                            'key' => (int)$v['code'],
                            'value' => Yii::$app->lang->get( $v['t_key'])
                        ];
                    }
                }else{
                    if ($val==$v['type'] && !in_array($v['group_type'],StaffLeaveReason::$agent_leave_reason)){
                        $leave_type['child'][] = [
                            'key' => (int)$v['code'],
                            'value' => Yii::$app->lang->get( $v['t_key'])
                        ];
                    }
                }
            }
            $r[] = $leave_type;
        }
        return $r;
    }

    /**
     * 获取未删除的离职原因
     * @return array
     */
    public function getLeaveTypeReasonNoDel($is_agent = false) {
        $r = [];
        $list = StaffLeaveReason::find()->select(['type','code','t_key','deleted','group_type'])->where(['deleted' => 0])->orderBy(['sort' => SORT_ASC])->asArray()->all();
        $leave_type_reasson = array_values(array_unique(array_column($list,'type')));
        foreach($leave_type_reasson as $key=>$val){
            $leave_type = [
                'key' => (int)$val,
                'value' => Yii::$app->lang->get('leave_type_' . $val)
            ];
            foreach ($list as $k=>$v){
                if ($is_agent){
                    if ($val==$v['type'] && in_array($v['group_type'],StaffLeaveReason::$agent_leave_reason) && $val == StaffLeaveReason::STAFF_LEAVE_TYPE_1){
                        $leave_type['child'][] = [
                            'key' => (int)$v['code'],
                            'value' => Yii::$app->lang->get( $v['t_key'])
                        ];
                    }elseif ($val==$v['type'] && $val != StaffLeaveReason::STAFF_LEAVE_TYPE_1 && !in_array($v['group_type'],StaffLeaveReason::$agent_leave_reason)){
                        $leave_type['child'][] = [
                            'key' => (int)$v['code'],
                            'value' => Yii::$app->lang->get( $v['t_key'])
                        ];
                    }
                }else{
                    if ($val==$v['type'] && !in_array($v['group_type'],StaffLeaveReason::$agent_leave_reason)){
                        $leave_type['child'][] = [
                            'key' => (int)$v['code'],
                            'value' => Yii::$app->lang->get( $v['t_key'])
                        ];
                    }
                }
            }
            $r[] = $leave_type;
        }
        return $r;
    }

    public function getNationality() {
        return [
            ['key' => 1,'value' => Yii::$app->lang->get('nationality_1')],
            ['key' => 2,'value' => Yii::$app->lang->get('nationality_2')],
            ['key' => 3,'value' => Yii::$app->lang->get('nationality_3')],
            ['key' => 4,'value' => Yii::$app->lang->get('nationality_4')],
            ['key' => 5,'value' => Yii::$app->lang->get('nationality_5')],
            ['key' => 6,'value' => Yii::$app->lang->get('nationality_6')],
            ['key' => 7,'value' => Yii::$app->lang->get('nationality_7')],
            ['key' => 8,'value' => Yii::$app->lang->get('nationality_8')],
            ['key' => 9,'value' => Yii::$app->lang->get('nationality_9')],
            ['key' => 10,'value' => Yii::$app->lang->get('nationality_10')],
            ['key' => 11,'value' => Yii::$app->lang->get('nationality_11')],
            ['key' => 99,'value' => Yii::$app->lang->get('nationality_99')],
        ];
    }

    public function getWorkingCountry() {
        return [
            ['key' => 1,'value' => Yii::$app->lang->get('working_country_1')],
            ['key' => 2,'value' => Yii::$app->lang->get('working_country_2')],
            ['key' => 3,'value' => Yii::$app->lang->get('working_country_3')],
            ['key' => 4,'value' => Yii::$app->lang->get('working_country_4')],
            ['key' => 5,'value' => Yii::$app->lang->get('working_country_5')],
            ['key' => 6,'value' => Yii::$app->lang->get('working_country_6')],
            ['key' => 7,'value' => Yii::$app->lang->get('working_country_7')],
            ['key' => 8,'value' => Yii::$app->lang->get('working_country_8')],
            ['key' => 99,'value' => Yii::$app->lang->get('working_country_99')],
        ];
    }

    public function getEducation() {
        return [
            ['key' => 1,'value' => Yii::$app->lang->get('education_1')],
            ['key' => 2,'value' => Yii::$app->lang->get('education_2')],
            ['key' => 3,'value' => Yii::$app->lang->get('education_3')],
            ['key' => 4,'value' => Yii::$app->lang->get('education_4')],
            ['key' => 5,'value' => Yii::$app->lang->get('education_5')],
            ['key' => 6,'value' => Yii::$app->lang->get('education_6')],
        ];
    }

    public function getRelativesRelationship() {
        return [
            ['key' => 1,'value' => Yii::$app->lang->get('relatives_relationship_1')],
            ['key' => 2,'value' => Yii::$app->lang->get('relatives_relationship_2')],
            ['key' => 3,'value' => Yii::$app->lang->get('relatives_relationship_3')],
            ['key' => 4,'value' => Yii::$app->lang->get('relatives_relationship_4')],
            ['key' => 5,'value' => Yii::$app->lang->get('relatives_relationship_5')],
            ['key' => 6,'value' => Yii::$app->lang->get('relatives_relationship_6')],
        ];
    }

    public function getRace() {
        $arr = [];
        foreach ($this->staff_race as $key => $value) {
            $arr[] = ['key' => $key, 'value' => Yii::$app->lang->get($value)];
        }
        return $arr;
    }

    public function getReligion() {
        $arr = [];
        foreach ($this->staff_religion as $key => $value) {
            $arr[] = ['key' => $key, 'value' => Yii::$app->lang->get($value)];
        }
        return $arr;
    }

    public function getCallName() {
        return [
            ['key' => 1,'value' => Yii::$app->lang->get('relatives_call_name_1')],
            ['key' => 2,'value' => Yii::$app->lang->get('relatives_call_name_2')],
            ['key' => 3,'value' => Yii::$app->lang->get('relatives_call_name_3')],
        ];
    }

    public $export_field_all = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        83 => 'last_name',
        84 => 'middle_name',
        85 => 'first_name',
        86 => 'suffix_name',
        5 => 'remarks',
        6 => 'gender',
        //7 => 'mobile',
        //8 => 'mobile_company',
        9 => 'idtentity',
        10 => 'personal_email',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        38 => 'bank_card_type',
        39 => 'bank_no',
        92 => "backup_bank_no_name",    // 备用持卡人信息
        93 => "backup_bank_type",   // 备用银行卡类型
        94 => "backup_bank_no",     // 备用银行卡号
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        45 => 'deduction_type',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        //52 => 'health_status',
        53 => 'disability_certificate',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        57 => 'birthday', //出生日期
        58 => 'permanent_address', //户口所在地地址
        59 => 'present_address', //居住地地址
        60 => 'relatives_relationship_1', //父亲名、姓
        61 => 'relatives_relationship_2', //母亲名、姓
        62 => 'relatives_relationship', //紧急联系人亲属关系
        63 => 'emergency_contact', //紧急联系人称呼姓名
        64 => 'relatives_mobile', //紧急联系人联系电话
        65 => 'education', //最高学历
        66 => 'graduate_school', //毕业学校
        67 => 'major', //专业
        68 => 'graduate_time', //毕业时间
        69 => 'nick_name', //昵称
        70 => 'nationality', //国籍
        71 => 'is_disability', //是否残疾
        72 => 'working_country', //工作所在国家
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        76 => 'social_security_num', //社保号
        105 => 'social_security_state',  // 社保号审核状态
        77 => 'fund_num', //公积金号
        106 => 'fund_state', //公积金号审核状态
        78 =>  YII_COUNTRY == 'PH' ? 'medical_insurance_num_ph' : 'medical_insurance_num', //医疗保险号
        107 => 'medical_insurance_state',   // 医疗保险号 审核状态
        79 => 'race', //种族
        80 => 'religion', //宗教
        109 => 'beneficiary_relation', //保险受益人与本人关系
        110 => 'beneficiary_name', //保险受益人姓名
        111 => 'beneficiary_identity', //保险受益人证件号
        112 => 'beneficiary_mobile', //保险受益人手机号
        81 => 'staff_province_code', //工作所在州 只有 工作地点为-1 才有此字段
        82 => 'vehicle_type_category', //马来在用
        100 => 'permanent_address_sub_item',   // 户口所在地地址(分项)
        101 => 'present_address_sub_item',    // 居住所在地地址(分项)
        88 => 'bank_branch_name',//银行分行名称
        89 => 'household_registration',//户籍照号
        90 => 'ptkp_state',//PTKP状态
        91 => 'tax_card',//税卡号
        108 => 'tax_card_state', // 税卡号审核状态
        102 => "backup_bank_card_audit_state",  // 备用银行账号审核状态 PH
        103 => "bank_card_audit_state",  // 银行账号审核状态 TH
        113 => 'signing_date',// my 签约日期
        122 => 'conversion_permanent_date',    // 转正式员工日期 PH
        120 => 'social_security_leave_date',    // 社保离职日期
        121 => 'residence_booklet_audit',    // 户口簿审核状态
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型

        self::EXPORT_FIELD_PROJECT_NUM => 'project_num',//项目期数
        self::EXPORT_FIELD_VAN_CONTAINER_STATE => 'van_container_state',
        self::EXPORT_FIELD_VAN_CONTAINER_START => 'van_container_start',
        self::EXPORT_FIELD_VAN_CONTAINER_END => 'van_container_end',
        self::EXPORT_FIELD_CONTRACT_COMPANY => 'contract_company',
        self::EXPORT_FIELD_AGE => 'age',

    ];

    public $export_field = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        6 => 'gender',
        //7 => 'mobile',
        //8 => 'mobile_company',
        9 => 'idtentity',
        10 => 'personal_email',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        self::EXPORT_FIELD_VAN_CONTAINER_STATE => 'van_container_state',
        self::EXPORT_FIELD_VAN_CONTAINER_START => 'van_container_start',
        self::EXPORT_FIELD_VAN_CONTAINER_END => 'van_container_end',
        self::EXPORT_FIELD_PROJECT_NUM => 'project_num',//项目期数
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        38 => 'bank_card_type',
        39 => 'bank_no',
        103 => "bank_card_audit_state",       // 银行卡审核状态
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        45 => 'deduction_type',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        //52 => 'health_status',
        53 => 'disability_certificate',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        57 => 'birthday', //出生日期
        58 => 'permanent_address', //户口所在地地址(组合)
        59 => 'present_address', //居住地地址(组合)
        60 => 'relatives_relationship_1', //父亲名、姓
        61 => 'relatives_relationship_2', //母亲名、姓
        62 => 'relatives_relationship', //紧急联系人亲属关系
        63 => 'emergency_contact', //紧急联系人称呼姓名
        64 => 'relatives_mobile', //紧急联系人联系电话
        65 => 'education', //最高学历
        66 => 'graduate_school', //毕业学校
        67 => 'major', //专业
        68 => 'graduate_time', //毕业时间
        69 => 'nick_name', //昵称
        70 => 'nationality', //国籍
        71 => 'is_disability', //是否残疾
        72 => 'working_country', //工作所在国家
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        //76 => 'social_security_num', //社保号
        //77 => 'fund_num', //公积金号
        //78 => 'medical_insurance_num', //医疗保险号
        //79 => 'race', //种族
        //80 => 'religion', //宗教
        100 => 'permanent_address_sub_item',   // 户口所在地地址(分项)
        101 => 'present_address_sub_item',    // 居住所在地地址(分项)
        120 => 'social_security_leave_date',    // 社保离职日期
        121 => 'residence_booklet_audit',    // 户口簿审核状态
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',
        self::EXPORT_FIELD_CONTRACT_COMPANY => 'contract_company',


    ];

    public $export_basic_field = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        //8 => 'mobile_company',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        self::EXPORT_FIELD_VAN_CONTAINER_STATE => 'van_container_state',
        self::EXPORT_FIELD_VAN_CONTAINER_START => 'van_container_start',
        self::EXPORT_FIELD_VAN_CONTAINER_END => 'van_container_end',
        self::EXPORT_FIELD_PROJECT_NUM => 'project_num',//项目期数
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        120 => 'social_security_leave_date',    // 社保离职日期
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',
        self::EXPORT_FIELD_CONTRACT_COMPANY => 'contract_company',

    ];

    public $export_field_ph = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        83 => 'last_name',
        84 => 'middle_name',
        85 => 'first_name',
        86 => 'suffix_name',
        5 => 'remarks',
        6 => 'gender',
        //7 => 'mobile',
        //8 => 'mobile_company',
        9 => 'idtentity',
        10 => 'personal_email',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        //16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        38 => 'bank_card_type',
        39 => 'bank_no',
        103 => "bank_card_audit_state",  // 银行账号审核状态 
        92 => "backup_bank_no_name",    // 备用持卡人信息
        93 => "backup_bank_type",   // 备用银行卡类型
        94 => "backup_bank_no",     // 备用银行卡号
        102 => "backup_bank_card_audit_state",  // 备用银行账号审核状态 PH
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        45 => 'deduction_type',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        //52 => 'health_status',
        53 => 'disability_certificate',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        57 => 'birthday', //出生日期
        58 => 'permanent_address', //户口所在地地址
        59 => 'present_address', //居住地地址
        60 => 'relatives_relationship_1', //父亲名、姓
        61 => 'relatives_relationship_2', //母亲名、姓
        62 => 'relatives_relationship', //紧急联系人亲属关系
        63 => 'emergency_contact', //紧急联系人称呼姓名
        64 => 'relatives_mobile', //紧急联系人联系电话
        65 => 'education', //最高学历
        66 => 'graduate_school', //毕业学校
        67 => 'major', //专业
        68 => 'graduate_time', //毕业时间
        69 => 'nick_name', //昵称
        70 => 'nationality', //国籍
        71 => 'is_disability', //是否残疾
        72 => 'working_country', //工作所在国家
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        76 => 'social_security_num', //社保号
        105 => 'social_security_state',  // 社保号审核状态
        77 => 'fund_num', //公积金号
        106 => 'fund_state', //公积金号审核状态
        78 => 'medical_insurance_num_ph', //医疗保险号
        107 => 'medical_insurance_state',   // 医疗保险号 审核状态
        91 => 'tax_card',
        108 => 'tax_card_state', // 税卡号审核状态
        100 => 'permanent_address_sub_item',   // 户口所在地地址(分项)
        101 => 'present_address_sub_item',    // 居住所在地地址(分项)
        122 => 'conversion_permanent_date',    // 转正式员工日期
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',
    ];

    public $export_basic_field_ph = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        83 => 'last_name',
        84 => 'middle_name',
        85 => 'first_name',
        86 => 'suffix_name',
        5 => 'remarks',
        //8 => 'mobile_company',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        //16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        76 => 'social_security_num', //社保号
        105 => 'social_security_state',  // 社保号审核状态
        77 => 'fund_num', //公积金号
        106 => 'fund_state', //公积金号审核状态
        78 => 'medical_insurance_num_ph', //医疗保险号
        107 => 'medical_insurance_state',   // 医疗保险号 审核状态
        91 => 'tax_card',
        108 => 'tax_card_state', // 税卡号审核状态
        122 => 'conversion_permanent_date',    // 转正式员工日期
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',

    ];

    public $export_field_my = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        6 => 'gender',
        //7 => 'mobile',
        //8 => 'mobile_company',
        9 => 'idtentity',
        10 => 'personal_email',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        82 => 'vehicle_type_category',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        38 => 'bank_card_type',
        39 => 'bank_no',
        103 => "bank_card_audit_state",  // 银行账号审核状态
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        45 => 'deduction_type',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        //52 => 'health_status',
        53 => 'disability_certificate',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        57 => 'birthday', //出生日期
        58 => 'permanent_address', //户口所在地地址
        59 => 'present_address', //居住地地址
        60 => 'relatives_relationship_1', //父亲名、姓
        61 => 'relatives_relationship_2', //母亲名、姓
        62 => 'relatives_relationship', //紧急联系人亲属关系
        63 => 'emergency_contact', //紧急联系人称呼姓名
        64 => 'relatives_mobile', //紧急联系人联系电话
        65 => 'education', //最高学历
        66 => 'graduate_school', //毕业学校
        67 => 'major', //专业
        68 => 'graduate_time', //毕业时间
        69 => 'nick_name', //昵称
        70 => 'nationality', //国籍
        71 => 'is_disability', //是否残疾
        72 => 'working_country', //工作所在国家
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        //76 => 'social_security_num', //社保号
        //77 => 'fund_num', //公积金号
        //78 => 'medical_insurance_num', //医疗保险号
        79 => 'race', //种族
        80 => 'religion', //宗教
        109 => 'beneficiary_relation', //保险受益人与本人关系
        110 => 'beneficiary_name', //保险受益人姓名
        111 => 'beneficiary_identity', //保险受益人证件号
        112 => 'beneficiary_mobile', //保险受益人手机号
        81 => 'staff_province_code', //工作所在州 只有 工作地点为-1 才有此字段
        //82 => 'vehicle_type_category', 不要动
        100 => 'permanent_address_sub_item',   // 户口所在地地址(分项)
        101 => 'present_address_sub_item',    // 居住所在地地址(分项)
        113 => 'signing_date',//签约日期
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_CONTRACT_COMPANY => 'contract_company',
        self::EXPORT_FIELD_AGE => 'age',
    ];

    public $export_basic_field_my = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        //8 => 'mobile_company',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        82 => 'vehicle_type_category',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        79 => 'race', //种族
        80 => 'religion', //宗教
        109 => 'beneficiary_relation', //保险受益人与本人关系
        110 => 'beneficiary_name', //保险受益人姓名
        111 => 'beneficiary_identity', //保险受益人证件号
        112 => 'beneficiary_mobile', //保险受益人手机号
        81 => 'staff_province_code', //工作所在州 只有 工作地点为-1 才有此字段
        //82 => 'vehicle_type_category', 不要动
        113 => 'signing_date',// my 签约日期
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_CONTRACT_COMPANY => 'contract_company',
        self::EXPORT_FIELD_AGE => 'age',

    ];

    public $export_field_id = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        6 => 'gender',
        //7 => 'mobile',
        //8 => 'mobile_company',
        9 => 'idtentity',
        10 => 'personal_email',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        38 => 'bank_card_type',
        39 => 'bank_no',
        88 => 'bank_branch_name',//银行分行名称
        89 => 'household_registration',//户籍照号
        90 => 'ptkp_state',//PTKP状态
        76 => 'social_security_num', //社保号
        78 => 'medical_insurance_num', //医疗保险号
        91 => 'tax_card',//税卡号
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        45 => 'deduction_type',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        //52 => 'health_status',
        53 => 'disability_certificate',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        57 => 'birthday', //出生日期
        58 => 'permanent_address', //户口所在地地址
        59 => 'present_aaddress', //居住地地址
        60 => 'relatives_relationship_1', //父亲名、姓
        61 => 'relatives_relationship_2', //母亲名、姓
        62 => 'relatives_relationship', //紧急联系人亲属关系
        63 => 'emergency_contact', //紧急联系人称呼姓名
        64 => 'relatives_mobile', //紧急联系人联系电话
        65 => 'education', //最高学历
        66 => 'graduate_school', //毕业学校
        67 => 'major', //专业
        68 => 'graduate_time', //毕业时间
        69 => 'nick_name', //昵称
        70 => 'nationality', //国籍
        71 => 'is_disability', //是否残疾
        72 => 'working_country', //工作所在国家
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        //76 => 'social_security_num', //社保号
        //77 => 'fund_num', //公积金号
        //78 => 'medical_insurance_num', //医疗保险号
        //79 => 'race', //种族
        //80 => 'religion', //宗教
        100 => 'permanent_address_sub_item',   // 户口所在地地址(分项)
        101 => 'present_address_sub_item',    // 居住所在地地址(分项)
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',
    ];

    public $export_basic_field_id = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        //8 => 'mobile_company',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        88 => 'bank_branch_name',//银行分行名称
        89 => 'household_registration',//户籍照号
        90 => 'ptkp_state',//PTKP状态
        76 => 'social_security_num', //社保号
        78 => 'medical_insurance_num', //医疗保险号
        91 => 'tax_card',//税卡号
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',

    ];
    public $export_field_vn = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        6 => 'gender',
        //7 => 'mobile',
        //8 => 'mobile_company',
        9 => 'idtentity',
        10 => 'personal_email',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        38 => 'bank_card_type',
        39 => 'bank_no',
        76 => 'social_security_num', //社保号
        78 => 'medical_insurance_num', //医疗保险号
        91 => 'tax_card',//税卡号
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        45 => 'deduction_type',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        //52 => 'health_status',
        53 => 'disability_certificate',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        57 => 'birthday', //出生日期
        58 => 'permanent_address', //户口所在地地址
        59 => 'present_aaddress', //居住地地址
        60 => 'relatives_relationship_1', //父亲名、姓
        61 => 'relatives_relationship_2', //母亲名、姓
        62 => 'relatives_relationship', //紧急联系人亲属关系
        63 => 'emergency_contact', //紧急联系人称呼姓名
        64 => 'relatives_mobile', //紧急联系人联系电话
        65 => 'education', //最高学历
        66 => 'graduate_school', //毕业学校
        67 => 'major', //专业
        68 => 'graduate_time', //毕业时间
        69 => 'nick_name', //昵称
        70 => 'nationality', //国籍
        71 => 'is_disability', //是否残疾
        72 => 'working_country', //工作所在国家
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        //76 => 'social_security_num', //社保号
        //77 => 'fund_num', //公积金号
        //78 => 'medical_insurance_num', //医疗保险号
        //79 => 'race', //种族
        //80 => 'religion', //宗教
        100 => 'permanent_address_sub_item',   // 户口所在地地址(分项)
        101 => 'present_address_sub_item',    // 居住所在地地址(分项)
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',
    ];

    public $export_basic_field_vn = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        //8 => 'mobile_company',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        76 => 'social_security_num', //社保号
        78 => 'medical_insurance_num', //医疗保险号
        91 => 'tax_card',//税卡号
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',
    ];
    public $export_field_la = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        6 => 'gender',
        //7 => 'mobile',
        //8 => 'mobile_company',
        9 => 'idtentity',
        10 => 'personal_email',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        38 => 'bank_card_type',
        39 => 'bank_no',
        76 => 'social_security_num', //社保号
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        45 => 'deduction_type',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        //52 => 'health_status',
        53 => 'disability_certificate',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        57 => 'birthday', //出生日期
        58 => 'permanent_address', //户口所在地地址
        59 => 'present_aaddress', //居住地地址
        60 => 'relatives_relationship_1', //父亲名、姓
        61 => 'relatives_relationship_2', //母亲名、姓
        62 => 'relatives_relationship', //紧急联系人亲属关系
        63 => 'emergency_contact', //紧急联系人称呼姓名
        64 => 'relatives_mobile', //紧急联系人联系电话
        65 => 'education', //最高学历
        66 => 'graduate_school', //毕业学校
        67 => 'major', //专业
        68 => 'graduate_time', //毕业时间
        69 => 'nick_name', //昵称
        70 => 'nationality', //国籍
        71 => 'is_disability', //是否残疾
        72 => 'working_country', //工作所在国家
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        //76 => 'social_security_num', //社保号
        //77 => 'fund_num', //公积金号
        //78 => 'medical_insurance_num', //医疗保险号
        //79 => 'race', //种族
        //80 => 'religion', //宗教
        100 => 'permanent_address_sub_item',   // 户口所在地地址(分项)
        101 => 'present_address_sub_item',    // 居住所在地地址(分项)
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',
    ];

    public $export_basic_field_la = [
        1 => 'staff_no',
        2 => 'name',
        3 => 'HRID',
        4 => 'name_en',
        5 => 'remarks',
        //8 => 'mobile_company',
        11 => 'business_mailbox',
        12 => 'department',
        13 => 'job_title',
        //14 => 'job_title_level',
        15 => 'job_title_grade',
        16 => 'respective_region',
        17 => 'respective_area',
        18 => 'respective_district',
        19 => 'branch',
        20 => 'role',
        21 => 'car_type',
        22 => 'car_no',
        23 => 'vehicle_source',
        24 => 'working_day_rest_type',//每周工作天数(week_working_day)改为工作天数&轮休规则(working_day_rest_type)
        25 => 'vehicle_use_date',
        26 => 'state',
        27 => 'stop_duty_reason',
        28 => 'hire_date',
        29 => 'leave_date',
        30 => 'stop_duties_date',
        31 => 'wait_leave_date',
        32 => 'formal',
        33 => 'link_master_account',
        34 => 'link_sub_account',
        35 => 'imm_supervisor',
        36 => 'imm_supervisor_name',
        37 => 'imm_supervisor_state',
        87 => 'bank_no_name',
        76 => 'social_security_num', //社保号
        40 => 'created_at',
        41 => 'creater',
        42 => 'create_days',
        43 => 'stop_duties_count',
        44 => 'equipment_cost',
        46 => 'payment_state',
        47 => 'payment_state_block_reason',
        48 => 'staffBill_01',
        49 => 'staffBill_02',
        50 => 'staffBill_03',
        51 => 'staffBill_04',
        54 => 'leave_type',
        55 => 'leave_reason',
        56 => 'leave_reason_remark',
        73 => 'hire_type', // 雇佣类型
        74 => 'hire_times', // 雇佣期间
        75 => 'contract_expiry_date', // 合同到期日
        self::POSITION_TYPE => 'position_type',    // 职位性质
        self::COST_TYPE => 'cost_type',    // 成本类型
        self::EXPORT_FIELD_AGE => 'age',
    ];



    public function getProvince() {
        $province_list = Yii::$app->cache->getOrSet('sysarea.province', function ($cache) {
            return SysProvince::find()->select(['code', 'name'])->where(['deleted' => 0])->asArray()->all();
        }, 10 * 60);
        return $province_list;
    }

    public function getCityList() {
        $city_list = Yii::$app->cache->getOrSet('sysarea.city', function ($cache) {
            return SysCity::find()->select(['code', 'name', 'province_code'])->where(['deleted' => 0])->asArray()->all();
        }, 10 * 60);
        return $city_list;
    }

    public function getDistrict() {
        $district_code = Yii::$app->cache->getOrSet('sysarea.district', function ($cache) {
            return SysDistrict::find()->select(['code', 'name', 'city_code', 'province_code'])->where(['deleted' => 0])->asArray()->all();
        }, 10 * 60);
        return $district_code;
    }

    public function getCountryLanguage() {
        switch (YII_COUNTRY) {
            case 'TH':
                $language = 'th';
                break;
            case 'VN':
                $language = 'vn';
                break;
            case 'MY':
            case 'ID':
            case 'LA':
            case 'PH':
                $language = 'en';
                break;
            default:
                $language = 'en';
        }

        return $language;
    }

    //网点类型为 1:DC、2:SP 限制可选班次
    //按照网点的分拣区域 N NE S  C B
    public $area_job_shift = [
        'N' => [ //BKK
            '13' => [ //Bike Courier
                'EARLY' => ['3','5','6','7','24'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];24[9:30-18:30]
                ],
            '110' => [ //Van Courier
                'EARLY' => ['3','5','6','7','24'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];24[9:30-18:30]
                ],
            '15' => [ //Branch manager
                'MIDDLE' => ['9'],
                ],
            '16' => [ //Branch Supervisor
                'MIDDLE' => ['9'],
                ],
            '451' => [ //Assistant Branch Supervisor
                'MIDDLE' => ['9'],
                ],
            '-999' => [
                'MIDDLE' => ['9','10','33'],
                'EARLY' => ['3','5','6','7','24'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];  缺少 9:30-18:30
                ],
        ],
        'NE' => [
            '13' => [ //Bike Courier
                'EARLY' => ['3','5','6','7','8'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  新增3
                'MIDDLE' => ['9'],
                ],
            '110' => [ //Van Courier
                'EARLY' => ['3','5','6','7','8'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  新增3
                'MIDDLE' => ['9'],
                ],
            '15' => [ //Branch manager
                'MIDDLE' => ['9'],
                ],
            '16' => [ //Branch Supervisor
                'MIDDLE' => ['9'],
                ],
            '451' => [ //Assistant Branch Supervisor
                'MIDDLE' => ['9'],
                ],
            '-999' => [
                'EARLY' => ['1','2','3','5','6','7','8'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  新增1/2/3
                'MIDDLE' => ['9','10'],
                'NIGHT' => ['11','13','15','16','22','23'],
                ],
        ],
        'S' => [
            '13' => [ //Bike Courier
                'EARLY' => ['2','3','4','5','6','7','8'], //4[7:30-16:30];5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  2/3
                'MIDDLE' => ['10'],   //新增 /10
                ],
            '110' => [ //Van Courier
                'EARLY' => ['4','5','6','7','8'], //4[7:30-16:30];5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]
                ],
            '15' => [ //Branch manager
                'MIDDLE' => ['9'],
                ],
            '16' => [ //Branch Supervisor
                'MIDDLE' => ['9'],
                ],
            '451' => [ //Assistant Branch Supervisor
                'MIDDLE' => ['9'],
                ],
            '-999' => [
                'EARLY' => ['1','2','3','4','5','6','7','8'], //4[7:30-16:30];5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  新增1/2/3
                'MIDDLE' => ['9','10'],
                'NIGHT' => ['11','15','17','26'],
                ],
        ],
        'C' => [
            '13' => [ //Bike Courier
                'EARLY' => ['2','3','4','5','6','7','8','25'], //4[7:30-16:30];5[8:00-17:0];7[9:00-18:00]  新增班次 2/3/6/8/25
                ],
            '110' => [ //Van Courier
                'EARLY' => ['2','3','4','5','6','7','8','25'], //4[7:30-16:30];5[8:00-17:0];7[9:00-18:00]   新增班次 2/3/6/8/25
                ],
            '15' => [ //Branch manager
                'EARLY' => ['5'],
                'MIDDLE' => ['9'],
                ],
            '16' => [ //Branch Supervisor
                'EARLY' => ['5'],
                'MIDDLE' => ['9'],
                ],
            '451' => [ //Assistant Branch Supervisor
                'EARLY' => ['34','8'],
                'MIDDLE' => ['9'],
                ],
            '-999' => [ //其他职位
                'EARLY' => ['1','2','4','3','5','7','6','8','25','32','34'], //3[7:00-16:00];5[8:00-17:00];7[9:00-18:00]  新增班次 2/4/6/8/25
                'MIDDLE' => ['9','10'],
                'NIGHT' => ['12','14','15','19','20'],
                ],
        ],
        'B' => [
            '13' => [ //Bike Courier
                'EARLY' => ['1','2','3','4','5','6','7'], //4[7:30-16:30];5[8:00-17:00]  新增 班次 1/2/3/6/7
                ],
            '110' => [ //Van Courier
                'EARLY' => ['1','2','3','4','5','6','7'],  //新增 班次 1/2/3/6/7
                ],
            '15' => [ //Branch manager
                'EARLY' => ['5'],
                'MIDDLE' => ['9'],
                ],
            '16' => [ //Branch Supervisor
                'EARLY' => ['5'],
                'MIDDLE' => ['9'],
                ],
            '451' => [ //Assistant Branch Supervisor
                'EARLY' => ['34','8'],
                'MIDDLE' => ['9'],
                ],
            '-999' => [ //其他职位
                'EARLY' => ['1','2','3','5','6','7','8','34'], //3[7:00-16:00];5[8:00-17:00];7[9:00-18:00]  新增班次 1/2/6
                'MIDDLE' => ['9','10'],
                'NIGHT' => ['15','29'],
                ],
        ],
    ];

    //网点类型为 1:DC、2:SP OS_CLD-Chilindo (**********) 、OS_LAS-KA (**********) 、Adjustment（**********）、Testing（**********）专用班次
    public $area_job_shift_other = [
        'N' => [ //BKK
            '13' => [ //Bike Courier
                'EARLY' => ['5','6','7','24'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];24[9:30-18:30]
            ],
            '110' => [ //Van Courier
                'EARLY' => ['5','6','7','24'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];24[9:30-18:30]
            ],
            '15' => [ //Branch manager
                'EARLY' => ['2','4','5','6','25'], //6[8:30-17:30]  新增班次 2/4/5/25
            ],
            '16' => [ //Branch Supervisor
                'EARLY' => ['2','4','5','6','25'], //6[8:30-17:30]  新增班次 2/4/5/25
                'MIDDLE' => ['9'],
            ],
            '451' => [ //Assistant Branch Supervisor
                'EARLY' => ['2','4','5','6','25'], //6[8:30-17:30]  新增班次 2/4/5/25
                'MIDDLE' => ['9'],
            ],
            '-999' => [
                'MIDDLE' => ['9','10','33'],
                'EARLY' => ['3','5','6','7','24'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];  缺少 9:30-18:30
            ],
        ],
        'NE' => [
            '13' => [ //Bike Courier
                'EARLY' => ['3','5','6','7','8'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  新增3
                'MIDDLE' => ['9'],
            ],
            '110' => [ //Van Courier
                'EARLY' => ['3','5','6','7','8'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  新增3
                'MIDDLE' => ['9'],
            ],
            '15' => [ //Branch manager
                'EARLY' => ['3','5','7'], //7[9:00-18:00] 新增3/5
            ],
            '16' => [ //Branch Supervisor
                'EARLY' => ['3','5','7'], //7[9:00-18:00]  新增3/5
                'MIDDLE' => ['9'],
            ],
            '451' => [ //Assistant Branch Supervisor
                'EARLY' => ['3','5','7'], //7[9:00-18:00]  新增3/5
                'MIDDLE' => ['9'],
            ],
            '-999' => [
                'EARLY' => ['1','2','3','5','6','7','8'], //5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  新增1/2/3
                'MIDDLE' => ['9','10'],
                'NIGHT' => ['11','13','15','16','22','23'],
            ],
        ],
        'S' => [
            '13' => [ //Bike Courier
                'EARLY' => ['2','3','4','5','6','7','8'], //4[7:30-16:30];5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  2/3
                'MIDDLE' => ['10'],   //新增 /10
            ],
            '110' => [ //Van Courier
                'EARLY' => ['4','5','6','7','8'], //4[7:30-16:30];5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]
            ],
            '15' => [ //Branch manager
                'EARLY' => ['3','5','7'], //7[9:00-18:00]  新增 3/5
            ],
            '16' => [ //Branch Supervisor
                'EARLY' => ['3','5','7'], //7[9:00-18:00]   新增 3/5
                'MIDDLE' => ['9'],
            ],
            '451' => [ //Assistant Branch Supervisor
                'EARLY' => ['3','5','7'], //7[9:00-18:00]   新增 3/5
                'MIDDLE' => ['9'],
            ],
            '-999' => [
                'EARLY' => ['1','2','3','4','5','6','7','8'], //4[7:30-16:30];5[8:00-17:0];6[8:30-17:30];7[9:00-18:00];8[10:00-19:00]  新增1/2/3
                'MIDDLE' => ['9','10'],   //新增 /10
                'NIGHT' => ['11','15','17','26'],
            ],
        ],
        'C' => [
            '13' => [ //Bike Courier
                'EARLY' => ['2','3','4','5','6','7','8','25'], //4[7:30-16:30];5[8:00-17:0];7[9:00-18:00]  新增班次 2/3/6/8/25
            ],
            '110' => [ //Van Courier
                'EARLY' => ['2','3','4','5','6','7','8','25'], //4[7:30-16:30];5[8:00-17:0];7[9:00-18:00]   新增班次 2/3/6/8/25
            ],
            '15' => [ //Branch manager
                'EARLY' => ['2','4','5','6','25'], //6[8:30-17:30]  新增班次 2/4/5/25
            ],
            '16' => [ //Branch Supervisor
                'EARLY' => ['2','4','5','6','25'], //4[7:30-16:30];5[8:00-17:0]     新增班次 2/4/5/25
                'MIDDLE' => ['9'],
            ],
            '451' => [ //Assistant Branch Supervisor
                'EARLY' => ['2','4','5','6','25'], //4[7:30-16:30];5[8:00-17:0]     新增班次 2/4/5/25
                'MIDDLE' => ['9'],
            ],
            '-999' => [ //其他职位
                'EARLY' => ['1','2','4','3','5','7','6','8','25','32'], //3[7:00-16:00];5[8:00-17:00];7[9:00-18:00]  新增班次 2/4/6/8/25
                'MIDDLE' => ['9','10'],
                'NIGHT' => ['12','14','15','19','20'],
            ],
        ],
        'B' => [
            '13' => [ //Bike Courier
                'EARLY' => ['1','2','3','4','5','6','7'], //4[7:30-16:30];5[8:00-17:00]  新增 班次 1/2/3/6/7
            ],
            '110' => [ //Van Courier
                'EARLY' => ['1','2','3','4','5','6','7'],  //新增 班次 1/2/3/6/7
            ],
            '15' => [ //Branch manager
                'EARLY' => ['1','2','3','5','6','7'], //5[8:00-17:00]    新增 班次 1/2/3/6/7
                'NIGHT' => ['15'],
            ],
            '16' => [ //Branch Supervisor
                'EARLY' => ['1','2','3','5','6','7'], //5[8:00-17:00]    新增 班次 1/2/3/6/7
                'MIDDLE' => ['9'],
                'NIGHT' => ['15'],
            ],
            '451' => [ //Assistant Branch Supervisor
                'EARLY' => ['1','2','3','5','6','7'], //5[8:00-17:00]    新增 班次 1/2/3/6/7
                'MIDDLE' => ['9'],
                'NIGHT' => ['15'],
            ],
            '-999' => [ //其他职位
                'EARLY' => ['1','2','3','5','6','7'], //3[7:00-16:00];5[8:00-17:00];7[9:00-18:00]  新增班次 1/2/6
                'MIDDLE' => ['9','10'],
                'NIGHT' => ['15','29'],
            ],
        ],
    ];

    /**
     * 网点类型 category
     * 这个不用了  移步到  by 库 -->sys_store_type表  读id ,name
     * @var string[]
     */
    public static $store_categories = [
        1 => 'SP',
        2 => 'DC',
        4 => 'SHOP(pickup-only)',
        5 => 'SHOP(pickup&delivery)',
        6 => 'FH',
        7 => 'USHOP',
        8 => 'Hub',
        9 => 'OS',
        10 => 'BDC',
        11 => 'FULFILLMENT',
        12 => 'B-Hub',
        13 => 'CDC',
    ];

    /**
     * 【ID】PTKP状态：单选
     * @var string[]
     */
    public function getPTKPState() {
          return [
            ['key' => "1",'value' => Yii::$app->lang->get('ptkp_state_1')],
            ['key' => "2",'value' => Yii::$app->lang->get('ptkp_state_2')],
            ['key' => "3",'value' => Yii::$app->lang->get('ptkp_state_3')],
            ['key' => "4",'value' => Yii::$app->lang->get('ptkp_state_4')],
            ['key' => "5",'value' => Yii::$app->lang->get('ptkp_state_5')],
            ['key' => "6",'value' => Yii::$app->lang->get('ptkp_state_6')],
            ['key' => "7",'value' => Yii::$app->lang->get('ptkp_state_7')],
            ['key' => "8",'value' => Yii::$app->lang->get('ptkp_state_8')],
        ];
    }

    /**
     * 获取总部角色 和之前返回结构一样
     * @return array
     */
    public function getHeaderOfficePosition()
    {
        $list = RoleEnums::find()->select(['role_id','role_name'])->where(['type' => RoleEnums::TYPE_HEAD_OFFICE])->orderBy(['role_id' => SORT_ASC])->asArray()->all();
        $position = array_column($list,'role_name','role_id');
        if (YII_ENV != 'pro') {
            $position[99] = 'SUPER_ADMIN';
        }
        return $position;
    }

    /**
     * 获取网点角色 和之前返回结构一样
     * @return array
     */
    public function getStorePosition()
    {
        $list = RoleEnums::find()->select(['role_id','role_name'])->where(['type' => RoleEnums::TYPE_STORE])->orderBy(['role_id' => SORT_ASC])->asArray()->all();
        $position = array_column($list,'role_name','role_id');
        if (YII_ENV != 'pro') {
            $position[99] = 'SUPER_ADMIN';
        }
        return $position;
    }

    /**
     * 获取合作商角色 和之前返回结构一样
     * @return array
     */
    public function getOfficalPartnetPosition()
    {
        $list = RoleEnums::find()->select(['role_id','role_name'])->where(['type' => RoleEnums::TYPE_OFFICAL_PARTNET_POSITION])->orderBy(['role_id' => SORT_ASC])->asArray()->all();
        $position = array_column($list,'role_name','role_id');
        if (YII_ENV != 'pro') {
            $position[99] = 'SUPER_ADMIN';
        }
        return $position;
    }

    /**
     * 获取全部角色 和之前返回结构一样
     * @return array
     */
    public function getPosition()
    {
        $list = RoleEnums::find()->select(['role_id','role_name'])->where(['type' => RoleEnums::TYPE_All])->orderBy(['role_id' => SORT_ASC])->asArray()->all();
        $position = array_column($list,'role_name','role_id');
        if (YII_ENV != 'pro') {
            $position[99] = 'SUPER_ADMIN';
        }
        return $position;
    }

    /**
     * 获取Fbi角色 和之前返回结构一样
     * @return array
     */
    public function getRolesFbi()
    {
        $list = RoleEnums::find()->select(['role_id','role_name'])->where(['type' => RoleEnums::TYPE_FBI])->orderBy(['role_id' => SORT_ASC])->asArray()->all();
        $position = array_column($list,'role_name','role_id');
        return $position;
    }


}
