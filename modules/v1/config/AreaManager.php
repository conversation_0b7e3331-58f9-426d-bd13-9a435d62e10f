<?php
namespace app\modules\v1\config;

use app\models\fle\SysProvince;
use Yii;

class AreaManager extends \yii\base\BaseObject
{
    // 部门主管-》直线主管
    // 变更记录 7=>21358 变更为 31211
    public static $department = [
        2 => 20254,
        3 => 17471,
        4 => 17194,
        5 => 17247,
        7 => 31211,
        8 => 19522,
        9 => 19522,
        10 => 17008,
        11 => 17624,
        12 => 17190,
        13 => 17109,
        14 => 17117,
        16 => 19991,
        17 => 20254,
        18 => 19677,
        19 => 19990,
        20 => 17573,
        21 => 19900,
        22 => 17212,
    ];

    // 按区域 主管
    public static $config = [
        'TH01' => 21536,
        'TH04' => 21536,
        'TH02' => 21536,
        'TH03' => 21536,
        'TH11' => 21536,
        'TH07' => 21536,
        'TH10' => 21536,
        'TH22' => 21536,
        'TH24' => 21536,
        'TH20' => 21536,
        'TH23' => 21536,
        'TH25' => 21536,
        'TH21' => 21536,
        'TH26' => 21536,
        'TH13' => 21536,
        'TH15' => 21536,
        'TH05' => 21536,
        'TH12' => 21536,
        'TH17' => 21536,
        'TH16' => 21536,
        'TH08' => 21536,
        'TH14' => 21536,
        'TH06' => 21536,
        'TH18' => 21536,
        'TH19' => 21536,
        'TH47' => 17062,
        'TH54' => 17062,
        'TH63' => 17062,
        'TH51' => 17062,
        'TH55' => 17062,
        'TH58' => 17062,
        'TH09' => 17062,
        'TH59' => 17062,
        'TH56' => 17062,
        'TH52' => 17062,
        'TH53' => 17062,
        'TH62' => 17062,
        'TH61' => 17062,
        'TH49' => 17062,
        'TH48' => 17062,
        'TH60' => 17062,
        'TH50' => 17062,
        'TH57' => 17062,
        'TH39' => 16883,
        'TH43' => 16883,
        'TH37' => 16883,
        'TH33' => 16883,
        'TH45' => 16883,
        'TH27' => 16883,
        'TH35' => 16883,
        'TH28' => 16883,
        'TH41' => 16883,
        'TH46' => 16883,
        'TH32' => 16883,
        'TH42' => 16883,
        'TH30' => 16883,
        'TH44' => 16883,
        'TH29' => 16883,
        'TH40' => 16883,
        'TH36' => 16883,
        'TH34' => 16883,
        'TH38' => 16883,
        'TH31' => 16883,
        'TH65' => 24165,
        'TH70' => 24165,
        'TH73' => 24165,
        'TH64' => 24165,
        'TH77' => 24165,
        'TH75' => 24165,
        'TH66' => 24165,
        'TH74' => 24165,
        'TH67' => 24165,
        'TH76' => 24165,
        'TH69' => 24165,
        'TH71' => 24165,
        'TH72' => 24165,
        'TH68' => 24165,
    ];

    // 泰国区域定义
    public static $areas = [
        1 => 'BKK',
        2 => 'Cen+East',
        3 => 'Cen+West',
        4 => 'North',
        5 => 'North East',
        6 => 'South',
        7 => 'Cen',
    ];
    //马来所属区域
    public static $areasMy = [
        1 => 'N',
        2 => 'C',
        3 => 'S',
        4 => 'EC',
        5 => 'EM',
    ];

    //老挝所属区域
    public static $areasLa = [
        1 => 'N',
        2 => 'C',
        3 => 'S',
    ];

    /**
     * 获取所属区域
     * @param $manage_geography_code
     * @return array|mixed
     */
    public static function getAreaProvince($manage_geography_code=null)
    {
        return Yii::$app->cache->getOrSet('are.manage.getAreaProvince.' . $manage_geography_code, function ($cache)  use($manage_geography_code) {
            $query = SysProvince::find();
            $query->select('code,manage_geography_code');
            $query->where(['manage_geography_code' => array_keys(self::$areas)]);
            $list = $query->asArray()->all();
            $result = [];
            foreach ($list as $item){
                $result[$item['manage_geography_code']][]= $item['code'];
            }
            if(!empty($manage_geography_code)){
                return $result[$manage_geography_code]??[];
            }
            return $result;
        },300);
    }

}
