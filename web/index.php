<?php

require __DIR__ . '/../vendor/autoload.php';
ini_set('memory_limit', '-1');
set_time_limit(600);

//加载环境变量
$dotenv = new Dotenv\Dotenv(dirname(__DIR__));
$dotenv->load();

// comment out the following two lines when deployed to production
defined('YII_DEBUG') or define('YII_DEBUG', env('debug'));
defined('YII_ENV') or define('YII_ENV', env('runtime'));
defined('TIMEZONE') or define('TIMEZONE', env('timeZone'));
defined('TIMEZONE_GMT') or define('TIMEZONE_GMT', env('timeZoneGMT'));
defined('TIME_ADD_HOUR') or define('TIME_ADD_HOUR', env('add_hour'));
defined('YII_COUNTRY') or define('YII_COUNTRY', env('country'));

//生产不报PHP错误
$level = YII_ENV == 'pro' ? 0 : E_ALL;
error_reporting($level);

require __DIR__ . '/../libs/mns/mns-autoloader.php';
require __DIR__ . '/../libs/phpexcel/PHPExcel.php';
require __DIR__ . '/../vendor/yiisoft/yii2/Yii.php';

$config = require __DIR__ . '/../config/web.php';
(new yii\web\Application($config))->run();