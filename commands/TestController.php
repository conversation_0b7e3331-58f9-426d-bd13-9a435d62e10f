<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\commands;

use app\models\backyard\OsStaffInfoExtend;
use app\libs\RocketMQ;
use app\models\backyard\TmpStaffJobTransfer;
use app\models\fle\SysStore;
use app\models\manage\HrOperateLogs;
use app\models\manage\HrStaffInfoPosition;
use app\models\manage\BaseStaffInfo;
use app\models\manage\StaffInfo;
use app\models\manage\StaffItems;
use app\models\manage\SysManagePiece;
use app\models\manage\SysManageRegion;
use app\modules\v1\business\Staff;
use app\modules\v1\business\StaffManager;
use app\modules\v1\business\StaffSalary;
use app\modules\v1\business\SysGroupDeptV2;
use app\modules\v1\config\SysConfig;
use app\services\base\CompanyService;
use app\services\base\CrowdSourcingOutsourcing;
use app\services\base\InstructorService;
use app\services\base\OutSourcingOrderService;
use app\services\base\SalaryService;
use app\services\base\StaffRelationsService;
use app\services\base\StaffService;
use app\services\base\StaffSyncService;
use app\services\base\SysConfigService;
use app\services\base\SysStoreService;
use Yii;
use yii\db\Exception;

/**
 * This command echoes the first argument that you have entered.
 *
 * This command is provided as an example for you to learn how to create console commands.
 *
 * <AUTHOR> Xue <<EMAIL>>
 * @since 2.0
 */
class TestController extends ConsoleController
{

    public function actionT22()
    {
        $send_email_result = Yii::$app->mailerLnt->compose()
            ->setSubject('lnt')
            ->setHtmlBody('lnt')
            ->setTo('<EMAIL>')
            ->setBcc(['<EMAIL>','<EMAIL>'])
            ->send();
        dd($send_email_result);
    }


    public function actionFixContractCompany(){
        $staff = CompanyService::getInstance()->getSpecialCompanyStaff();
        foreach ($staff as $staff_id => $company_id) {
           Yii::$app->get('backyard')->createCommand("UPDATE hr_staff_info SET contract_company_id =:contract_company_id ,updated_at=updated_at where staff_info_id=:staff_info_id ")
                ->bindValue(':contract_company_id', $company_id)
                ->bindValue(':staff_info_id', $staff_id)
                ->execute();
        }
    }

    public function actionInsert()
    {
        StaffInfo::insertMonthlyProbation(29316,'2025-02-13 00:00:00');
    }
    public function actionTestLeaveNum()
    {
        $data = [
            'identity'=>147204391540,
        ];
        $num = StaffService::getInstance()->getStaffLeaveNum($data);
        print_r($num);die;
    }

    public function actionContract()
    {
       $a =  StaffInfo::isContractV3('2025-02-15');
       var_dump($a);
    }
    public function actionOnJob()
    {
        $messageBody      = [
            'state_date'    => date('Y-m-d'),
            'staff_info_id' => 111,
            'before'        => ['state' =>1],
            'after'         => ['state' =>2],
            'created_at'    => date('Y-m-d H:i:s'),
        ];
        $mq               = new RocketMQ('staff-on-job-change');
        $sendData['data'] = $messageBody;
        $sendData['type'] = RocketMQ::TAG_NAME_STAFF_ON_JOB_CHANGE;
        $mq->setShardingKey(111);
        $result           = $mq->sendOrderlyMsg($sendData);
        var_dump($result);
    }
    public function actionEntry(){


        $sql = "SELECT staff_info_id from hr_staff_info where staff_info_id in (2003819,145113,145121) and  hire_date >= '2024-05-30' and formal in (1,4) and is_sub_staff = 0";
        $staff_list = Yii::$app->getDb()->createCommand($sql)->queryAll();
        $i = 74;

        foreach ($staff_list as $key => $value) {

            if(in_array($value['staff_info_id'],[2003792,2003793])){
                continue;
            }
            $i ++;
            $op = HrOperateLogs::find()->where(['staff_info_id' => $value['staff_info_id']])->orderBy('id asc')->limit(1)->one();


            //第二条before 是第1条的afer
            $data  = json_decode($op->after,true)['body'];
            $of = $data;
            unset($data[0],$data[1],$data[2],$data[3],$data[4],$data[5],$data[6]);
            $op->after = json_encode(['body' => $data], JSON_UNESCAPED_UNICODE); ;
            $op->type = 'staff';
            $res  = $op->save();


            $add = new HrOperateLogs();
            $add->id = $i;
            $add->staff_info_id = $value['staff_info_id'];
            $add->operater = $op->operater;
            $add->type = 'staff_shift_weekly';
            $add->before = '{"body":[]}';
            $add->after = json_encode(['body' => [$of[0],$of[1],$of[2],$of[3],$of[4],$of[5],$of[6]]], JSON_UNESCAPED_UNICODE);
            $res  = $add->save();


        }

    }


    public function actionTt()
    {
//        for ($i = 1; $i < 10; $i++) {
            $messageBody = [
                'staff_info_id' => 121112,
                'email'         => 'test2324.' . 10 .'@flashexpress.ph',
            ];

            $rmq                       = new RocketMQ('update-staff-info');
            $sendData['jsonCondition'] = json_encode($messageBody, JSON_UNESCAPED_UNICODE);
            $sendData['handleType']    = RocketMQ::TAG_HR_STAFF_UPDATE;
            $rmq->setShardingKey(121112);
            $result = $rmq->sendOrderlyMsg($sendData, 5);
//        }
        die;

        print_r($result);die;

        $res = SysGroupDeptV2::getDepartmentManager(1214);
        print_r($res);die;
//        $region['region_id'] = 24;
//        $region['manager_id'] = 26667;
//        $region['operator_id'] = 10000;
//        StaffRelationsService::getInstance()->addStaffJurisdictionRegion($region);die;

        $region['piece_id'] = 34;
        $region['manager_id'] = 26667;
        $region['operator_id'] = 10000;
        StaffRelationsService::getInstance()->addStaffJurisdictionPiece($region);die;
        $osExtendInfo = OsStaffInfoExtend::find()->select(['staff_info_id', 'company_item_id', 'is_complete_address'])->where(['IN', 'staff_info_id', '10000'])->asArray()->indexBy('staff_info_id')->all();
        print_r($osExtendInfo);die;
        (new StaffManager())->updateStaffLeaveManager(28001, 10000, [['id' =>'TH01010266']]);die;

    }


    public function actionSalary($staffId){
        (new StaffSalary())->defaultCreateHrStaffSalary($staffId);
    }
    //
    public function actionFixName() {
        $sql = "SELECT i.staff_info_id,i.name,it.value as bank_no_name from hr_staff_info as i inner join hr_staff_items as it on i.staff_info_id = it.staff_info_id and it.item = 'BANK_NO_NAME'  where formal in (1,4) and !( i.name like '% %') and i.name !=it.`value` and REPLACE(it.`value`,  ' ', '') = i.name ";
        $staff_list = Yii::$app->getDb()->createCommand($sql)->queryAll();
        foreach ($staff_list as $key => $value) {
            Staff::$update_items_is_save_operate_log = false;
            Staff::updateItems($value['staff_info_id'],['name'=>$value['bank_no_name']],'-1');
            StaffItems::getDb()->createCommand()->update('hr_staff_items', ['value' => $value['bank_no_name']], "staff_info_id={$value['staff_info_id']} and item = 'BACKUP_BANK_NO_NAME'")->execute();
        }
    }


    public function actionF(){
    }


    public function actionFix(){

        $val = Staff::view(27869);

        $val['name'] = $val['staff_info_id'].' test mq 66';
        $val['state'] = 1;
        $val['leave_date'] = null;
        $re = StaffSyncService::getInstance()->saveStaffInfo($val, 1);
        dd($re);
    }


    public function actionIndex()
    {
        $hrStaffInfoPosition =  HrStaffInfoPosition::find()
            ->select(['staff_info_id', 'group_concat(position_category) as position_category'])
            ->where(['IN', 'staff_info_id', [56780,17245]])
            ->indexBy('staff_info_id')
            ->groupBy('staff_info_id')
            ->asArray()
            ->all(Yii::$app->get('r_backyard'));
        var_dump($hrStaffInfoPosition);die;



        $mail->setTo(['<EMAIL>']);
        var_dump($mail->send());die;


        $s = new \DateTime('now', new \DateTimeZone('+0700'));
        echo $s->format('Y-m-d H:i:s');die;

        $date = Yii::$app->dateTh->sub(new \DateInterval('P2D'))->format('Y-m-d 18:00:00');
        //$date = (new \DateTime('now', new \DateTimeZone('+0700')))->format('Y-m-d');
        echo $date;die;
        $callbackParams = [
            'new_staff_info_id'=>25898,
            'admin_id'=>10000,
            'entry_id'=>1885,
        ];
        $res = Yii::$app->redis->lpush(Staff::WINHR_CREATE_STAFF_LIST,json_encode($callbackParams));
        var_dump($res);die;
    }


    public function actionTest()
    {
        $strores = $result = SysConfigService::getInstance()->outsourcing_bank_type();
        print_r($strores);die;
    }

    public function actionCls()
    {

        StaffService::init()->test();
        SalaryService::init()->test();

        SalaryService::getInstance()->testSingle();
        StaffService::getInstance()->testSingle();
        SalaryService::getInstance()->testSingle();
        StaffService::getInstance()->testSingle();
        SalaryService::getInstance()->testSingle();
        StaffService::getInstance()->testSingle();


    }

    public function actionUp(){
        $result = Staff:: updateItems(27273,['sys_store_id'=>'TH01010102','job_title'=>110,'state'=>3,'week_working_day'=>6,'rest_type'=>1,'stop_duties_date'=>'2023-12-18','stop_duty_reason' => '111']);
        var_dump($result);
    }

    /**
     * 刷个人代理的企业号码
     * 一次性脚本 1250 条数据
     * @throws Exception
     */
    public function actionUpdateCompanyMobile() {
        $staffList = StaffInfo::find()
            ->where(['hire_type' => 13])
            ->andWhere(['>', 'mobile_company', 0])
            ->asArray()
            ->all();

        foreach ($staffList as $key => $value) {
            Staff::updateItems($value['staff_info_id'],['mobile_company' => ''],'-1');
            $this->echo('SuccessOne-' .$value['staff_info_id']);
        }

        $this->echo('Success');
    }

    /**
     * 刷it部门员工数据
     * 43条数据
     * @throws \Exception
     */
    public function actionUpdateJobTitleItMobile($params) {
        $staffIds = [
            64248,63509,43364,56123,67085,85980, 86942, 600516, 17779, 55791, 87087, 87088, 87093, 87109, 87116,
            87134, 87140, 87141, 87154, 87163, 87164, 87168, 87181, 87239, 87304, 87311, 87336, 87337, 87387,
            87388, 87389, 87390, 87391, 87393, 87403, 87405, 87414, 87415, 87420, 87421, 87424, 87425, 87426,
        ];

        //测试环境数据
        if (YII_ENV_DEV) {
            $staffIds = [$params];
        }

        foreach ($staffIds as $value) {
            Staff::$update_items_is_save_operate_log = false;
            Staff::updateItems($value, ['job_title' => 1243], '-1');
            $this->echo('SuccessOne-' . $value);
        }

        $this->echo('Success');
    }
    
    public function actionAddProbation()
    {
        $message = (new \app\models\manage\StaffInfo)->insertMonthlyProbationRefresh();
        $this->echo('Success');
    }

    /**
     * 补全历史 probation 数据 ph
     * @return void
     */
    public function actionFixedProbation()
    {
        if (YII_COUNTRY != 'PH'){
            return ;
        }
        $message = (new \app\models\manage\StaffInfo)->fixedHrProbationPhHistory();
        $this->echo($message);
    }
}
