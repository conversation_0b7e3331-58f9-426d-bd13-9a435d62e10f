<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\commands;

use app\commands\ConsoleController;
use app\libs\Enums\SuspendReasonEnums;
use app\libs\RedisListKeyEnums;
use app\models\backyard\AsyncImportTask;
use app\models\backyard\HrJobDepartmentRelation;
use app\models\backyard\HrStaffShift;
use app\models\backyard\LeaveManageLog;
use app\models\backyard\SettingEnv;
use app\models\backyard\StaffFaceBlacklistHitRecord;
use app\models\backyard\StaffResign;
use app\models\backyard\TmpStaffJobTransfer;
use app\models\fle\StaffInfo as MsStaffInfo;
use app\models\fle\SysDepartment;
use app\models\fle\SysStore;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HrDepartmentJobTitleLevelGrade;
use app\models\manage\HrDepeartmentJobTitle;
use app\models\manage\HrJobTitle;
use app\models\manage\HrJobTitleRole;
use app\models\manage\HrOperateLogs;
use app\models\backyard\HrOutsourcingOrderDetail;
use app\models\manage\HrStaffInfoPosition;
use app\models\manage\HrStaffItems;
use app\models\manage\HrStaffSalary;
use app\models\manage\HrStaffTransfer;
use app\models\manage\HrStaffTransferLog;
use app\models\manage\StaffInfo;
use app\models\manage\StaffItems;
use app\models\manage\SysManagePiece;
use app\models\manage\SysStoreTemp;
use app\modules\v1\business\Staff;
use app\models\manage\HrEmails;
use app\modules\v1\business\StaffManager;
use app\modules\v1\business\SysGroupDeptV2;
use app\services\base\AfterEntryService;
use app\services\base\AsyncImportTaskService;
use app\services\base\SettingEnvService;
use app\services\base\ShiftManageConfigService;
use app\services\base\StaffAttendanceService;
use app\services\base\StaffService;
use app\services\base\StaffSupportService;
use app\services\base\StaffSyncService;
use Yii;
use Exception;

/**
 * This command echoes the first argument that you have entered.
 *
 * This command is provided as an example for you to learn how to create console commands.
 *
 * <AUTHOR> Xue <<EMAIL>>
 * @since 2.0
 *
 */
class StaffController extends ConsoleController
{

    public function actionCheckFleByStaffInfoDiff($staff_id = '') {
        $sql = "SELECT
              hsi.staff_info_id,
              hsi.name,
              hsi.node_department_id != si.department_id as '部门不一致',
              hsi.node_department_id , 
              si.department_id,
              hsi.job_title != si.job_title as '职位不一致',
              hsi.job_title , 
              si.job_title as si_job_title
            from
              staff_info as si
              inner join hr_staff_info as hsi on hsi.staff_info_id = si.id
              and hsi.`formal` in (1, 4)
              and hsi.`is_sub_staff` = 0
              and hsi.`state` =  si.state 
              and hsi.node_department_id > 0                         
            where
              staff_info_id > 0
              and (
               ( 
                hsi.job_title != si.job_title or si.`job_title` is null)
                or 
                (hsi.node_department_id != si.department_id or si.department_id is null)
              ) and staff_info_id not in (35680,16966)";

        if(!empty($staff_id)) {
            $sql .= " and hsi.staff_info_id = ".$staff_id;
        }

        $staff_list = Yii::$app->r_backyard->createCommand($sql)->queryAll();

        if (empty($staff_list)) {
            return;
        }
        Yii::$app->logger->write_log(['ms-hris员工信息不对齐' => $staff_list], 'alert');
        foreach ($staff_list as $key => $value) {
            Staff::$update_items_is_save_operate_log = false;
            Staff::updateItems($value['staff_info_id'], ['name' => $value['name']], '-1');
        }

        $this->actionFixStaffExtend();

    }

    /**
     * ./yii staff/fix-staff-extend
     * staff_extend表对齐
     * @return void
     */
    public function actionFixStaffExtend()
    {
        $sql = "select i.staff_info_id  from `hr_staff_info`  as i left join hr_staff_info_extend as e on i.`staff_info_id` = e.`staff_info_id`  where i.formal in (1, 4) and i.is_sub_staff = 0  and e.`id` is  null ";

        $staff_list = Yii::$app->r_backyard->createCommand($sql)->queryAll();

        if (empty($staff_list)) {
            return;
        }

        Yii::$app->logger->write_log(['hr_staff_info_extend表空缺' => $staff_list], 'alert');
        $service = AfterEntryService::getInstance();
        foreach ($staff_list as $key => $value) {
            $service->createStaffExtendInfo($value['staff_info_id']);
        }
    }



    //通知未更改的成SCB银行卡的员工尽快修改工资卡 30 10 * * *
    public function actionStaffBankTypeMsg() {
        $staff_list = StaffInfo::find()->where(['bank_type' => 1])->asArray()->all();
        foreach ($staff_list as $key => $value) {
            $title = 'กรุณาเปลี่ยนข้อมูลบัญชีธนาคารจากธนาคาร TMB เป็นธนาคาร SCB อย่างเร็วที่สุด';
            $msg = 'กรุณาเปลี่ยนข้อมูลบัญชีธนาคารจากธนาคาร TMB เป็นธนาคาร SCB อย่างเร็วที่สุด เพื่อจะไม่ส่งผลกระทบต่อการจ่ายเงินเดือนของเดือนนี้และเดือนต่อไป ขอบคุณค่ะ';
            Yii::$app->jrpc->backYardMessage($value['staff_info_id'], $msg, $title);
            $this->echo("工号：".$value['staff_info_id']."发送成功");
        }
    }

    //HRIS 停职状态超过4天后自动离职 15 1 * * *
    /**
     * 停职变离职
     * ./yii staff/autoleave
     * @param $date
     * @param $staffIds
     * @return void
     */
    public function actionAutoleave($date='',$staffIds = '')
    {
        $this->echo("begin task::---> auto leave");
        try{
            $staff_info_arr = [];
            $currentDate = gmdate('Y-m-d', time() + TIME_ADD_HOUR*3600);
            if(!empty($date)){
                $currentDate = $date;
            }
            $build = BaseStaffInfo::find()->select([
                'hr_staff_info.*',
                'sys_store.name as store_name',
                'sys_department.name as department_name',
                'triple_to_leave.stop_begin_date',
            ])
                ->leftJoin('sys_store', '`sys_store`.`id` = `hr_staff_info`.`sys_store_id`')
                ->leftJoin('sys_department', '`sys_department`.`id` = `hr_staff_info`.`sys_department_id`')
                ->leftJoin('triple_to_leave', '`triple_to_leave`.`staff_info_id` = `hr_staff_info`.`staff_info_id` and `triple_to_leave`.`stop_duties_date` = `hr_staff_info`.`stop_duties_date`')
                ->where(['hr_staff_info.state' => 3]);  //停职
            if ($staffIds) {
                $staffIds = explode(',' , $staffIds);
                $build->andWhere(['in','hr_staff_info.staff_info_id',$staffIds]);
            }
            $staff_list= $build->andWhere(['in', 'hr_staff_info.formal', [1, 4]])    //编制、实习生
                ->andWhere(['hr_staff_info.is_sub_staff' => 0])       //不是子账号
                ->asArray()
                ->all();
            //旷工停离职配置
            $ab_config_data  = SettingEnv::find()->select(['code','set_val'])->where(['in','code',['staff_absent_days','staff_leave_days','IC_staff_leave_days','IC_staff_absent_days','part_time_IC_staff_absent_days','part_time_IC_staff_leave_days']])->asArray()
                ->all();
            Yii::$app->logger->write_log(['ab_config_data'=>$ab_config_data],'info');
            $ab_config_data = array_column($ab_config_data,'set_val','code');
            if (!empty($staff_list)) {
                $EHS_stop_days = 7;
                if (YII_COUNTRY == 'MY') {
                    $EHS_stop_days = 14;
                }
                foreach ($staff_list as $staff) {
                    $staff['days_stop_duty'] = (strtotime($currentDate)- strtotime(substr($staff['stop_duties_date'],0,10))) / 86400;
                    //如果员工存在未处理的恢复在职申请，则跳过
                    if ($this->checkExistsReinstatement($staff)){
                        continue;
                    }
                    $staff_absent_days = intval($ab_config_data['staff_absent_days'] ?? 3);
                    $staff_leave_days  = intval($ab_config_data['staff_leave_days'] ?? 4);

                    if (YII_COUNTRY == 'MY' && $staff['hire_type'] == StaffInfo::HIRE_TYPE_UN_PAID) {
                        $staff_absent_days = intval($ab_config_data['IC_staff_absent_days'] ?? 3);
                        $staff_leave_days  = intval($ab_config_data['IC_staff_leave_days'] ?? 4);
                    }
                    if (YII_COUNTRY == 'MY' && $staff['hire_type'] == StaffInfo::HIRE_TYPE_PART_TIME_AGENT) {
                        $staff_absent_days = intval($ab_config_data['part_time_IC_staff_absent_days'] ?? 3);
                        $staff_leave_days  = intval($ab_config_data['part_time_IC_staff_leave_days'] ?? 4);
                    }
                    $is_face_black_list_hit = false;
                    $updateData = [];
                    switch ($staff['stop_duty_reason']) {
                        case SuspendReasonEnums::SUSPEND_TYPE_INVESTIGATE_THE_IMPERSONATION: //调查冒名顶替
                            if (isCountry('TH') && $staff['days_stop_duty'] >= 7) {
                                $updateData = ['state' => BaseStaffInfo::STATE_RESIGN, 'leave_reason' => BaseStaffInfo::LEAVE_REASON_FAKE_INFO, 'leave_type' => BaseStaffInfo::LEAVE_TYPE_NO_COMPENSATE, 'leave_date' => $currentDate,'leave_source'=>BaseStaffInfo::LEAVE_SOURCE_FACE_BLACKLIST];
                                $is_face_black_list_hit = true;
                            }
                            break;
                        case '8': //[EHS立案调查][大于等于X天停职状态][停职变在职]   19781调整个人代理一直停着
                            if ($staff['days_stop_duty'] >= $EHS_stop_days && !in_array($staff['hire_type'],StaffInfo::$agentTypeTogether)) {
                                $updateData = ['state' => 1];
                            }
                            break;
                        case '3': //[惩罚员工][大于7天停职状态][停职变在职]
                            if ($staff['days_stop_duty'] >= 7) {
                                $updateData = ['state' => 1];
                            }
                            break;
                        case '4': //[调查中][大于15天停职状态][停职变离职]
                            if ($staff['days_stop_duty'] >= 15) {
                                $updateData = ['state' => 2, 'leave_reason' => 21, 'leave_type' => 2, 'leave_date' => $currentDate,'leave_source'=>3];
                            }
                            break;
                        case '5': //[其他][大于4天停职状态][停职变离职]
                            if ($staff['days_stop_duty'] >= 4) {
                                $updateData = ['state' => 2, 'leave_reason' => 21, 'leave_type' => 2, 'leave_date' => $currentDate,'leave_source'=>3];
                            }
                            break;
                        case '6': //[旷工3天自动停职][大于7天停职状态][停职变离职]
                            //旷工的第一天 为离职日期
                            $countryCode = strtolower(YII_COUNTRY);
                            if ( (in_array($countryCode,['th','ph','id','vn','la']) && $staff['days_stop_duty'] >= ($ab_config_data['staff_leave_days'] ?? 4))
                                || ($countryCode == 'my' && $staff['days_stop_duty'] >= ($staff_absent_days + $staff_leave_days)) // 马来停职日期是连续旷工的第一天
                            ) {
                                if ($staff['stop_begin_date']) {
                                    $updateData = ['state' => 2, 'leave_reason' => 21, 'leave_type' => 2, 'leave_date' => $staff['stop_begin_date'],'leave_source'=>3];
                                }
                                // 没有打卡记录或审批通过的请假记录：离职日期=入职日期
                                if (isCountry('TH') && StaffAttendanceService::getInstance()->validateLeaveDateEqHireData($staff['staff_info_id'])) {
                                    $updateData['leave_date'] = $staff['hire_date'];
                                }

                            }
                            break;
                        default:
                            break;
                    }
                    if (!empty($updateData)) {
                        try {
                            $result = Staff::updateItems($staff['staff_info_id'], $updateData, -1);
                        } catch (Exception $e) {
                            $result = [
                                'message' => $e->getMessage(),
                                'line'    => $e->getLine(),
                            ];
                        }
                        
                        if ($result !== true) {
                            $this->echo("员工" . $staff['staff_info_id'] . '停职失败 失败原因 :'. json_encode([$result]) . '-- 更新内容：'  .json_encode($updateData));
                            Yii::$app->logger->write_log([
                                'staff_info_id' => $staff['staff_info_id'],
                                'message' => 'auto_leave停职失败',
                                'result' => $result,
                                'updateData' => $updateData,
                                ]);
                            continue;
                        }
                        if ($updateData['state'] == 1) {
                            //原来是离职 或者原来是待离职 变成在职 同步新离职资产 员工在职信息
                            StaffService::getInstance()->pushRedisSyncResignAssetsStaff([
                                'event_type' => 'staff_work',
                                'params'     => [
                                    'staff_info_id'      => $staff['staff_info_id'],
                                    'staff_name'         => $staff['name'],
                                    'operation_staff_id' => -1,
                                ],
                            ]);
                        }

                        if ($is_face_black_list_hit && $staff['stop_duty_reason'] == SuspendReasonEnums::SUSPEND_TYPE_INVESTIGATE_THE_IMPERSONATION) {
                            $staffFaceBlacklistHitRecordModel = StaffFaceBlacklistHitRecord::find()->where([
                                'staff_info_id' => $staff['staff_info_id'],
                                'status'        => StaffFaceBlacklistHitRecord::STATUS_HIT,
                            ])->orderBy(['id' => SORT_DESC])->one();
                            if($staffFaceBlacklistHitRecordModel){
                                //自动生成hc;
                                if($staffFaceBlacklistHitRecordModel->create_hc_id == 0){
                                    $hc_id = Yii::$app->jrpc->copyLeaveStaffHc($staff['staff_info_id']);
                                    if ($hc_id) {
                                        $staffFaceBlacklistHitRecordModel->create_hc_id = $hc_id;
                                    }
                                }
                                $staffFaceBlacklistHitRecordModel->status          = StaffFaceBlacklistHitRecord::STATUS_CONFIRM;
                                $staffFaceBlacklistHitRecordModel->operator_time   =  gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR*3600);
                                $staffFaceBlacklistHitRecordModel->operator_remark = 'face_blacklist_operator_remark_resign';
                                $staffFaceBlacklistHitRecordModel->save();
                            }
                        }
                        $this->echo("员工" . $staff['staff_info_id'] . ", 停职原因:". $staff['stop_duty_reason'] .",停职时间为:" .$staff['days_stop_duty']. "天,停职状态变更为:" . $updateData['state']);

                        if (in_array($staff['stop_duty_reason'], [4,5,6])) {
                            if ($staff['manger']) {
                                $email = BaseStaffInfo::find()->select('email')->where(['staff_info_id'=>$staff['manger']])->column();
                                $email = current($email);
                            }

                            $staff_info_arr[] = [
                                'staff_info_id' => $staff['staff_info_id'],
                                'staff_name'    => $staff['name'],
                                'leave_date'    => $updateData['leave_date'],
                                'stop_duty_reason' => $staff['stop_duty_reason'],
                                'manager_id'    => $relations['value'] ?? '',
                                'manager_email' => $email ?? '',
                                'store_name'    => $staff['store_name'] ?? Yii::$app->lang->get('head_office'),
                                'department_name' => $staff['department_name'],
                            ];
                        }
                    }
                }

                if (!empty($staff_info_arr)) {
                    $this->sendMailtoHr($staff_info_arr);
                    $this->sendMailtoManager($staff_info_arr);
                }
            } else {
                $this->echo("无停职员工需要处理");
            }
        }catch (\Exception $e) {
            Yii::$app->logger->write_log('停职自动离职遇到问题，问题原因：' . $e->getMessage());
        }

        $this->echo("end task::---> auto leave");
    }

    /**
     * 检查是否存在复职申请记录
     * @param $staff
     * @return bool
     */
    private function checkExistsReinstatement($staff)
    {
        $res = Yii::$app->backyard_main->createCommand('select * from reinstatement_request where staff_info_id=:staff_info_id and state in (1,2) and handled=0')
            ->bindValue(":staff_info_id", $staff['staff_info_id'])
            ->queryAll();
        return !empty($res);
    }

    //发送邮件给hr
    private function sendMailtoHr($staff_info_arr)
    {
        $this->echo("send mail to hr::---> auto leave");

        $lang_str = $this->getDefaultLang();
        $trans =  \Yii::$app->lang;
        if( strtolower(env('country','th'))=='ph' || strtolower(env('country_code', 'th')) == 'my'){
            $head = $trans->get("PH_leave_mail2hr_head",'',$lang_str);
        }elseif(strtolower(env('country','th'))=='la'){
            $head = $trans->get("LA_leave_mail2hr_head",'',$lang_str);
        }elseif (strtolower(env('country','th'))=='my') {
            $head = $trans->get("MY_leave_mail2hr_head",'',$lang_str);
        } else if (strtolower(env('country', 'vn')) == 'vn') {
            $head = $trans->get("vn_leave_mail2hr_head",'',$lang_str);
        } else if (strtolower(env('country', 'vn')) == 'id') {
            $head = $trans->get("id_leave_mail2hr_head",'',$lang_str);
        } else {
            $head = $trans->get("leave_mail2hr_head",'',$lang_str);
        }


        $foot = "<p><strong>The system automatically sends emails. Please do not reply.</strong></p>
                 <p><srong>Thanks，</strong></br>
                 Flash Express Team</p>";

        $settingEnv = SettingEnv::findOne(['code' => 'staff_leave_days']);
        if ($settingEnv) {
            $settingEnv = $settingEnv->toArray();
            $leaveDays = $settingEnv['set_val'] ?? "";
        }

        $leave_reason = [
            4 => $trans->get("leave_reason_by_company_4","",$lang_str),
            5 => $trans->get("leave_reason_by_company_5","",$lang_str),
            6 => sprintf($trans->get("leave_reason_by_company_6_config_day","",$lang_str), $leaveDays ?? ""),
        ];

        $table_list = "";
        foreach ($staff_info_arr as $staff) {
            $table_list .= "<tr>";
            $table_list .= "<td style='padding: 10px;'>{$staff['staff_name']}（{$staff['staff_info_id']}）</td>";
            $table_list .= "<td style='padding: 10px;'> ". ($staff['department_name'] ?? '') ."</td>";
            $table_list .= "<td style='padding: 10px;'>". ($staff['store_name'] ?? '') ."</td>";
            $table_list .= "<td style='padding: 10px;'>". date("Y-m-d", strtotime($staff['leave_date'])) ."</td>";
            $table_list .= "<td style='padding: 10px;'>". ($leave_reason[$staff['stop_duty_reason']] ?? '') ."</td>";
            $table_list .= "</tr>";
        }
        $content =
            "<table border='1' cellspacing='0' cellpadding='0'>
                <th>".$trans->get("leave_mail2hr_table_1","",$lang_str)."</th>
                <th>".$trans->get("department","",$lang_str)."</th>
                <th>".$trans->get("leave_mail2hr_table_3","",$lang_str)."</th>
                <th>".$trans->get("leave_mail2hr_table_4","",$lang_str)."</th>
                <th>".$trans->get("leave_mail2hr_table_5","",$lang_str)."</th>
                {$table_list}
            </table>";

        try {
            if (YII_COUNTRY == "VN") {

                $mail = Yii::$app->mailer->compose()
                    ->setSubject('【VN】leave staff email')
                    ->setHtmlBody($head . $content . $foot);
            } else if (YII_COUNTRY == 'ID') {

                $mail = Yii::$app->mailer->compose()
                    ->setSubject('【ID】leave staff email')
                    ->setHtmlBody($head . $content . $foot);

            } else {

                $mail = Yii::$app->mailer->compose()
                    ->setSubject('leave staff email')
                    ->setHtmlBody($head . $content . $foot);
            }

            $emails = SettingEnvService::getInstance()->getSetVal('resigned_staff_email', ',');
            $mail->setTo($emails);
            $result = $mail->send();
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $emails,
            ]);
        }

        $this->echo('end' . json_encode($result));
    }

    //发送邮件给直接上级
    private function sendMailtoManager($staff_info_arr)
    {
        $this->echo("send mail to manager");
        if (empty($staff_info_arr)) {
            $this->echo("no data");
            return true;
        }
        $staff_info_arr = array_column($staff_info_arr, null, 'staff_info_id');

        $manager = [];
        foreach ($staff_info_arr as $staff) {
            if (empty($staff['manager_email'])) {
                continue;
            }
            $manager[$staff['manager_email']][] = $staff['staff_info_id'];
        }

        if (empty($manager)) {
            $this->echo("no data");
            return true;
        }

        $lang_str = $this->getDefaultLang();
        $trans =  \Yii::$app->lang;
        $head = $trans->get("leave_mail2manager_head",'',$lang_str);

        $foot = "<p><strong>The system automatically sends emails. Please do not reply.</strong></p>
                        <p><srong>Thanks，</strong></br>
                            Flash Express Team</p>";

        $settingEnv = SettingEnv::findOne(['code' => 'staff_leave_days']);
        if ($settingEnv) {
            $settingEnv = $settingEnv->toArray();
            $leaveDays = $settingEnv['set_val'] ?? "";
        }

        $leave_reason = [
            4 => $trans->get("leave_reason_by_company_4","",$lang_str),
            5 => $trans->get("leave_reason_by_company_5","",$lang_str),
            6 => sprintf($trans->get("leave_reason_by_company_6_config_day","",$lang_str), $leaveDays ?? ""),
        ];

        foreach ($manager as $manager_email => $staffs) {
            $table_list = "";
            foreach ($staffs as $staff) {
                $table_list .= "<tr>";
                $table_list .= "<td style='padding: 10px;'>{$staff_info_arr[$staff]['staff_name']}（{$staff_info_arr[$staff]['staff_info_id']}）</td>";
                $table_list .= "<td style='padding: 10px;'>". date("Y-m-d", strtotime($staff_info_arr[$staff]['leave_date'])) ."</td>";
                $table_list .= "<td style='padding: 10px;'>". ($leave_reason[$staff_info_arr[$staff]['stop_duty_reason']] ?? '') ."</td>";
                $table_list .= "</tr>";
            }
            $content =
                "<table border='1' cellspacing='0' cellpadding='0'>
                    <th>".$trans->get("leave_mail2hr_table_1","",$lang_str)."</th>
                    <th>".$trans->get("leave_mail2hr_table_4","",$lang_str)."</th>
                    <th>".$trans->get("leave_mail2hr_table_5","",$lang_str)."</th>
                    {$table_list}
                </table>";
            try {
                if (YII_COUNTRY == 'VN') {

                    $mail = Yii::$app->mailer->compose()
                        ->setSubject('【VN】leave staff email')
                        ->setHtmlBody($head . $content . $foot);
                } else if (YII_COUNTRY == 'ID') {

                    $mail = Yii::$app->mailer->compose()
                        ->setSubject('【ID】leave staff email')
                        ->setHtmlBody($head . $content . $foot);

                } else {

                    $mail = Yii::$app->mailer->compose()
                        ->setSubject('leave staff email')
                        ->setHtmlBody($head . $content . $foot);
                }
                if(YII_ENV == 'dev'){
                    $emails = [
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                    ];
                    $mail->setTo($emails);
                }else{
                    $mail->setTo(trim($manager_email));
                }
                Yii::$app->logger->write_log(['params' => $staffs,'manager_email'=>$manager_email,'content'=>$head . $content . $foot],'info');
                $result = $mail->send();
                $this->echo('end' . json_encode($result));
            } catch (Exception $e) {
                $level = 'error';
                if($e->getCode() == 503){
                    $level = 'notice';
                }
                Yii::$app->logger->write_log([
                    'message' => $e->getMessage(),
                    'line' => $e->getLine(),
                    'file' => $e->getFile(),
                    'params' => $staffs,
                ],$level);
                $this->echo('error');
            }
        }
        $this->echo("end send mail to manager");
    }

    //待离职状态 离职日期（生效日期）前系统通过邮箱通知上级和HR部门 前三天和前一天泰国时间 04.00
    public function actionWaitLeaveSendEmail() {
        $this->echo('执行时间:'.gmdate('Y-m-d H:i:s',time()+TIME_ADD_HOUR*3600));
        try {
            $currentDate = gmdate('Y-m-d', time() + TIME_ADD_HOUR*3600);//当前日期
            $the_day_before =  date("Y-m-d H:i:s",strtotime('+1 day', strtotime($currentDate)));
            $three_days_before = date("Y-m-d H:i:s",strtotime('+3 day', strtotime($currentDate)));

            $recipient_email = SettingEnvService::getInstance()->getSetVal('waiting_resignation_email', ',');

            $storeTemp = SysStoreTemp::temp();
            $staffs_list = StaffInfo::find()->where(['wait_leave_state' => 1])
                                    ->andWhere(['formal' => [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE]])
                                    ->andWhere(['OR',['leave_date' => $the_day_before],['leave_date' => $three_days_before]])
                                    ->asArray()
                                    ->all();
            if(count($staffs_list) > 0) {
                $the_day_before_list = [];
                $three_days_before_list = [];
                foreach ($staffs_list as $key => $staff) {
                    if (isset($storeTemp[$staff['sys_store_id']])) {
                        $staff['sys_store_name'] = $storeTemp[$staff['sys_store_id']]['name'];
                    } else {
                        $staff['sys_store_name'] = $staff['sys_store_id'];
                    }
                    if($staff['node_department_id'] != 0) {
                        $staff['sys_department_name'] = Yii::$app->sysconfig->allDepeartments[$staff['node_department_id']] ?? '';
                    } else {
                        $staff['sys_department_name'] = Yii::$app->sysconfig->allDepeartments[$staff['sys_department_id']] ?? '';
                    }
                    $staff['job_title_name'] = Yii::$app->sysconfig->jobTitle[$staff['job_title']] ?? '';
                    //https://flashexpress.feishu.cn/docx/Hya7du1BmonoaxxpUZfca6JHnde
                    if (YII_COUNTRY == 'VN') {
                        //上级
                        $staff['manager_email'] = $this->GetStaffMangerEmail($staff['staff_info_id'],2);
                    } else {
                        //上级 上上级
                        $staff['manager_email'] = $this->GetStaffMangerEmail($staff['staff_info_id'],3);
                    }

                    if($staff['leave_date'] == $the_day_before) {
                        $this->echo('离职前一天工号:'.$staff['staff_info_id']);
                        //发送邮件给直线上级
                        $this->DayTypeSendEmail([$staff], $staff['manager_email'],'the_day_before');
                        $the_day_before_list[] = $staff;
                    }
                    if($staff['leave_date'] == $three_days_before) {
                        $this->echo('离职前三天工号:'.$staff['staff_info_id']);
                        //发送邮件给直线上级
                        $this->DayTypeSendEmail([$staff], $staff['manager_email'],'three_days_before');
                        $three_days_before_list[] = $staff;
                    }
                }
                //离职前一天
                if(count($the_day_before_list) > 0) {
                    $this->echo('离职前一天总数：'.count($the_day_before_list));
                    $this->DayTypeSendEmail($the_day_before_list, $recipient_email,'the_day_before');
                }

                //离职前三天
                if(count($three_days_before_list) > 0) {
                    $this->echo('离职前三天总数：'.count($three_days_before_list));
                    $this->DayTypeSendEmail($three_days_before_list, $recipient_email,'three_days_before');
                }
            } else {
                $this->echo('没有需要发送待离职员工邮件');
            }
        } catch (\Exception $e) {
            $this->echo("WaitLeaveSendEmail : error " . $e->getMessage());
            Yii::$app->logger->write_log('定时任务，WaitLeaveSendEmail，可能出现的问题:'.$e->getMessage().';行号：'.$e->getLine());
        }
        $this->echo('结束时间:'. gmdate('Y-m-d H:i:s',time()+TIME_ADD_HOUR*3600));
        $this->echo('end');
    }


    //待离职 发送邮件
    private function DayTypeSendEmail($staff_info_arr,$recipient_email,$type) {
        $res = $this->getMailData($staff_info_arr,$type);
        $send_mail = Yii::$app->mailer->compose()
            ->setSubject($res['title'])
            ->setHtmlBody($res['content']);
        $send_mail->setTo($recipient_email);
        $result = $send_mail->send();
        $this->echo('收件人邮箱:'.json_encode($recipient_email).';send_email_result:' . json_encode($result));
        return true;
    }

    //离职前 发邮件内容整理
    protected function getMailData($staff_info_arr, $type)
    {
        $countryCode = YII_COUNTRY;
        $countryCode = strtoupper($countryCode);
        $return      = [];
        //获取配置的邮箱地址
        $hrsMail         = SettingEnvService::getInstance()->getSetVal('hris_email');
        $return['title'] = "【{$countryCode}】List of employees whose status is awaiting resignation";
        $text_other      = $text_cn = '';
        switch ($type) {
            case 'the_day_before'://1天
                $text_cn = "<p><strong>你好</strong></p>
                    <p>以下的员工由于在系统申请离职，次日系统将自动把以下员工的状态改为 ”已离职“。若员工需要取消离职申请或如有疑问，请联系 {$hrsMail}</p>";
                if (in_array($countryCode, ['TH', 'LA'])) {//泰语
                    $text_other = "<p><strong>เรียน ทุกท่านที่เกี่ยวข้อง </strong></p>
                    <p>พนักงานรายชื่อดังต่อไปนี้เป็นพนักงานที่ยื่นเรื่องลาออก โดยระบบจะดำเนินการปิดระบบการทำงานของพนักงาน และเปลี่ยนสถานะการทำงานของพนักงานเป็น “ลาออก” อัตโนมัติในวันถัดไป หากพนักงานมีความประสงค์ยกเลิกลาออก หรือ มีข้อสงสัยสามารถสอบถามได้ที่ {$hrsMail}</p>
                    <p>จึงเรียนมาเพื่อทราบ </p>";
                } else {
                    //其他国家
                    $text_other = "<p><strong>Hello,</strong></p>
                    <p>As the following employees apply for resignation in the system, the system will automatically change the status of the following employees to resign after 1 days. If employees need to cancel their resignation applications or have questions, please contact {$hrsMail}</p>
                       ";
                }
                break;
            case 'three_days_before'://3天
                //中文
                $text_cn = "<p><strong>你好</strong></p>
                    <p>以下的员工由于在系统申请离职，三天后系统将自动把以下员工的状态改为 ”离职“。若员工需要取消离职申请或如有疑问，请联系 {$hrsMail}</p>";

                if (in_array($countryCode, ['TH', 'LA'])) {
                    $text_other = "<p><strong>เรียน ทุกท่านที่เกี่ยวข้อง </strong></p>
                    <p>พนักงานรายชื่อดังต่อไปนี้เป็นพนักงานที่ยื่นเรื่องลาออก โดยระบบจะดำเนินการปิดระบบการทำงานของพนักงาน และเปลี่ยนสถานะการทำงานของพนักงานเป็น “ลาออก” อัตโนมัติในอีก 3 วันถัดไป หากพนักงานมีความประสงค์ยกเลิกลาออก หรือ มีข้อสงสัยสามารถสอบถามได้ที่ {$hrsMail}</p>
                    <p>จึงเรียนมาเพื่อทราบ </p>";
                } else {//其他国家
                    $text_other = "<p><strong>Hello, </strong></p>
                    <p>As the following employees apply for resignation in the system, the system will automatically change the status of the following employees to resign after 3 days. If employees need to cancel their resignation applications or have questions, please contact {$hrsMail}</p>";
                }
                break;
        }
        $tableContent = '';
        foreach ($staff_info_arr as $staff) {
            $tableContent .= "<tr>";
            $tableContent .= "<td style='padding: 10px;'>{$staff['name']}（{$staff['staff_info_id']}）</td>";//姓名/工号
            $tableContent .= "<td style='padding: 10px;'> " . ($staff['job_title_name'] ?? '') . "</td>";//职位
            $tableContent .= "<td style='padding: 10px;'>" . ($staff['sys_department_name'] ?? '') . "</td>";//部门
            $tableContent .= "<td style='padding: 10px;'>" . ($staff['sys_store_name'] ?? '') . "</td>";//网点
            $tableContent .= "<td style='padding: 10px;'>" . date("Y-m-d",
                    strtotime('-1 day', strtotime($staff['leave_date']))) . "</td>";//最后工作日期
            $tableContent .= "<td style='padding: 10px;'>" . date("Y-m-d",
                    strtotime($staff['leave_date'])) . "</td>";//生效日期
            $tableContent .= "</tr>";
        }

        $tableContent =
            "<table border='1' cellspacing='0' cellpadding='0'>
                    <th>Name/staff id</th>
                    <th>Position</th>
                    <th>Department</th>
                    <th>Branch</th>
                    <th>Last working day</th>
                    <th>Resignation Date</th>
                    {$tableContent}
                </table>";

        $foot              = "<p><strong>The system automatically sends emails. Please do not reply.</strong></p>";
        $return['content'] = $text_other . $text_cn . $tableContent . $foot;
        return $return;
    }

    //获取直线上级邮箱
    private function GetStaffMangerEmail($staff_info_id, $level, $i = 0,&$result = []) {
        $i++;
        if($i < $level) {
            $manger_staff_info = StaffItems::find()->where(['item' => 'MANGER'])->andWhere(['staff_info_id' => $staff_info_id])->asArray()->one();
            $email = BaseStaffInfo::find()->select('email')->where(['staff_info_id'=>$manger_staff_info['value']])->column();
            if(!empty(current($email))) {
                $result[] = current($email);
            }
            $this->GetStaffMangerEmail($manger_staff_info['value'], $level, $i, $result);
        }
        return $result;
    }

    //到待离职日期（生效日期）当天系统将自动把 “待离职” 状态改为 “离职”，泰国时间 凌晨00：05分钟
    public function actionWaitLeaveToLeave($date = '',$staff_info_id=0) {
        $this->echo('执行时间:'.gmdate('Y-m-d H:i:s',time()+TIME_ADD_HOUR*3600));
        $currentDate  = !empty($date) ? $date . ' 00:00:00' : gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600);//当前日期
        $query = BaseStaffInfo::find()->select([
            'staff_info_id',
            'leave_date',
            'leave_source',
            'leave_reason',
        ])->where(['wait_leave_state' => 1,'state' => 1]);
        if (!empty($staff_info_id)) {
            $query->where(['staff_info_id' => $staff_info_id]);
        }
        $staffs_leave = $query->andWhere(['<=', 'leave_date', $currentDate])
            ->asArray()
            ->all();
        if(empty($staffs_leave)) {
            $this->echo($currentDate.',没有需要执行离职的员工');
            return;
        }

        foreach ($staffs_leave as $item) {
            $attributes['state']                 = 2;                  //离职
            $attributes['leave_date']            = $item['leave_date'];//离职日期
            $attributes['is_auto_system_change'] = 1;
            $is_update                           = false;
            if (strtotime($item['leave_date']) <= strtotime($currentDate)) {
                $is_update = true;
            }
            //by申请离职
            if($item['leave_source'] == 5){
                $staffResign = StaffResign::find()->where([
                    'submitter_id' => $item['staff_info_id'],
                ])->andWhere(['IN', 'status', [2, 5]])->orderBy('resign_id desc')->one();
                if($staffResign){
                    $is_update = true;
                }
            }

            if (!$is_update) {
                Yii::$app->logger->write_log("离职数据异常:" . $item['staff_info_id'] . 'data:' . json_encode($item));
                continue;
            }

            try {
                $result = Staff::updateItems($item['staff_info_id'], $attributes, -1);
                if ($result !== true) {
                    Yii::$app->logger->write_log(['params'=>$item,'result'=>$result,'error'=>'待离职变离职失败!!!']);
                    continue;
                }
            } catch (Exception $e) {
                Yii::$app->logger->write_log([
                    'function'      => 'actionWaitLeaveToLeave',
                    'error_message' => $e->getMessage(),
                    'line'          => $e->getLine(),
                    'file'          => $e->getFile(),
                    'staff_info_id' => $item['staff_info_id'],
                ]);
                continue;
            }

            //试用期未通过类型hold工资加提出
            if (
                YII_COUNTRY == 'MY' &&
                $item['leave_source'] == BaseStaffInfo::LEAVE_SOURCE_PROBATION
            ) {
                $staffHoldParams = [
                    'staff_info_id' => $item['staff_info_id'],                                              //员工id
                    'type'          => '1,2',                                                               //hold类型(1.工资hold,2.提成hold)
                    'hold_reason'   => 'incomplete_resignation_procedures3',                                //hold原因
                    'hold_remark'   => '',                                                                  //hold备注
                    'hold_time'     => gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600),    //hold时间
                    'hold_source'   => 9,                                                                   //hold来源
                ];

                $result = Yii::$app->jrpc->staff_hold($staffHoldParams);
                Yii::$app->logger->write_log(['function' => 'StopStaff-syncHoldAStaff', 'result' => $result, 'params' => $staffHoldParams], 'info');
            }

            //处理后续其他业务逻辑
            //1. 泰国 转个人代理
            if ($item['leave_reason'] == 33 && !empty($staffResign) && $staffResign->reason == 33) {
                $data = [
                    'origin_staff_info_id' => $item['staff_info_id'],
                    'mobile'               => $staffResign->mobile,
                    'job_title_id'         => $staffResign->job_title,
                    'entry_date'           => date('Y-m-d', strtotime($item['leave_date'])),
                    'hc_id'                => $staffResign->re_employment_hc_id,
                    'resign_id'            => $staffResign->resign_id,
                ];

                //调用WinHR的rpc
                $body = [
                    'jsonrpc' => '2.0',
                    'method'  => 'transfer_to_agent_from_resign',
                    'params'  => [['locale' => 'en'], $data,],
                    'id'      => molten_get_traceid(),
                ];
                try {
                    $res = Yii::$app->http->json_rpc_post(Yii::$app->params['winhr_rpc_endpoint'], $body);
                    Yii::$app->logger->write_log(['body' => $body, 'res' => $res], 'info');
                    $res_arr = json_decode($res, true);
                    if (empty($res_arr['result']) || $res_arr['result']['code'] != 1) {
                        Yii::$app->logger->write_log(['transfer_to_agent_from_resign' => $item, 'error' => $res_arr]);
                    }
                } catch (Exception $e) {
                    Yii::$app->logger->write_log(['transfer_to_agent_from_resign' => $item, 'error' => $e->getMessage()]);
                }
            }
        }
        $this->echo('结束时间:'. gmdate('Y-m-d H:i:s',time()+TIME_ADD_HOUR*3600));
        $this->echo('end');
    }

    //残疾员工入职离职发送邮件
    public function actionDisabilityStaff() {
        $this->echo('begin');
        try {
            $currentDate = gmdate('Y-m-d', time() + TIME_ADD_HOUR*3600 - 24*3600);
            if(YII_ENV == 'pro') {
                $recipient_email = [
                    '<EMAIL>',
                ];
            } else {
                $recipient_email = [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                ];
            }
            $staff_list = BaseStaffInfo::find()->select([
                        'hr_staff_info.*',
                        'sys_store.name as store_name',
                        'sys_department.name as department_name',
                        'hr_job_title.job_name as job_title_name',
                    ])
                    ->leftJoin('sys_store', '`sys_store`.`id` = `hr_staff_info`.`sys_store_id`')
                    ->leftJoin('sys_department', '`sys_department`.`id` = `hr_staff_info`.`sys_department_id`')
                    ->leftJoin('hr_job_title', 'hr_job_title.id = hr_staff_info.job_title')
                    ->where(['hr_staff_info.state' => [1, 2]])             //入职离职员工
                    ->andWhere(['is_disability' => 1])  //残疾员工
                    ->andWhere(['OR', ['hr_staff_info.hire_date' => $currentDate], ['hr_staff_info.leave_date' => $currentDate]])
                    ->andWhere(['in', 'hr_staff_info.formal', [1, 4]])    //编制、实习生
                    ->andWhere(['hr_staff_info.is_sub_staff' => 0])       //不是子账号
                    ->asArray()
                    ->all();

            if(empty($staff_list)) {
                $this->echo("没有需要发送的邮件");
            } else {
                $staff_hire_list = [];
                $staff_leave_list = [];
                foreach ($staff_list as $staff) {
                    if($staff['state'] == 1) {
                        //入职残疾员工
                        $staff_hire_list[] = [
                            'staff_info_id' => $staff['staff_info_id'],
                            'staff_name' => $staff['name'],
                            'store_name'    => $staff['store_name'] ?? Yii::$app->lang->get('head_office'),
                            'department_name' => $staff['department_name'],
                            'job_title_name' => $staff['job_title_name'],
                            'hire_date' => $staff['hire_date'],
                            'disability_certificate' => $staff['disability_certificate'],
                        ];
                    } else {
                        //离职残疾员工
                        $staff_leave_list[] = [
                            'staff_info_id' => $staff['staff_info_id'],
                            'staff_name' => $staff['name'],
                            'store_name'    => $staff['store_name'] ?? Yii::$app->lang->get('head_office'),
                            'department_name' => $staff['department_name'],
                            'job_title_name' => $staff['job_title_name'],
                            'hire_date' => $staff['hire_date'],
                            'disability_certificate' => $staff['disability_certificate'],
                        ];
                    }
                }
                if(empty($staff_hire_list)) {
                    $this->echo('没有入职的残疾员工');
                } else {
                    $this->DisabilityStaffSendEmail(1, $staff_hire_list, $recipient_email);
                }

                if(empty($staff_leave_list)) {
                    $this->echo('没有离职的残疾员工');
                } else {
                    $this->DisabilityStaffSendEmail(2, $staff_leave_list, $recipient_email);
                }
            }

        }catch (\Exception $e) {
            Yii::$app->logger->write_log('残疾员工入职离职发送邮件，问题原因：' . $e->getMessage());
        }
        $this->echo('end');
    }

    private function DisabilityStaffSendEmail($type, $staff_list, $recipient_email) {
        try {
            switch ($type) {
                case 1: //入职
                    $subject = 'List of new hired disable employees';
                    $head_th = "<p><strong>เรียนทุกท่านที่เกี่ยวข้อง</strong></p>
                                <p>พนักงานรายชื่อดังต่อไปนี้ เป็นพนักงานที่มีความพิการเข้าทำงานใหม่ในบริษัท</p>
                                <p>จึงเรียนมาเพื่อทราบ</p>";
                    $head_cn = "<p><strong>你好</strong></p>
                                <p>以下为新入职残疾员工名单，请知悉！</p>";
                    break;
                case 2: //离职
                    $subject = 'List of disable employees whose status is awaiting resignation';
                    $head_th = "<p><strong>เรียนทุกท่านที่เกี่ยวข้อง</strong></p>
                                <p>พนักงานรายชื่อดังต่อไปนี้ เป้นพนักงานที่มีความพิการที่ยื่อเรื่องลาออก</p>
                                <p>จึงเรียนมาเพื่อทราบ</p>";
                    $head_cn = "<p><strong>你好</strong></p>
                                <p>以下为离职残疾员工名单，请知悉！</p>";
                    break;
            }

            $table_list_th = '';
            $table_list_cn = '';
            foreach ($staff_list as $staff) {
                $table_list_th .= "<tr>";
                $table_list_th .= "<td style='padding: 10px;'>{$staff['staff_name']}（{$staff['staff_info_id']}）</td>";//姓名/工号
                $table_list_th .= "<td style='padding: 10px;'> ". ($staff['job_title_name'] ?? '') ."</td>";//职位
                $table_list_th .= "<td style='padding: 10px;'>". ($staff['department_name'] ?? '') ."</td>";//部门
                $table_list_th .= "<td style='padding: 10px;'>". ($staff['store_name'] ?? '') ."</td>";//网点
                $table_list_th .= "<td style='padding: 10px;'>". date("d-m-Y", strtotime($staff['hire_date'])) ."</td>";//入职日期
                $table_list_th .= "<td style='padding: 10px;'>". (empty($staff['disability_certificate']) ? '-' : $staff['disability_certificate']) ."</td>";//残疾证号
                $table_list_th .= "</tr>";
            }
            $content_th =
                "<table border='1' cellspacing='0' cellpadding='0'>
                <th>รายชื่อ</th>
                <th>ตำแหน่ง</th>
                <th>แผนก</th>
                <th>สาขา</th>
                <th>วันที่เริ่มงาน</th>
                <th>เลขบัตรประจำตัวคนพิการ</th>
                {$table_list_th}
            </table>";

            foreach ($staff_list as $staff) {
                $table_list_cn .= "<tr>";
                $table_list_cn .= "<td style='padding: 10px;'>{$staff['staff_name']}（{$staff['staff_info_id']}）</td>";//姓名/工号
                $table_list_cn .= "<td style='padding: 10px;'> ". ($staff['job_title_name'] ?? '') ."</td>";//职位
                $table_list_cn .= "<td style='padding: 10px;'>". ($staff['department_name'] ?? '') ."</td>";//部门
                $table_list_cn .= "<td style='padding: 10px;'>". ($staff['store_name'] ?? '') ."</td>";//网点
                $table_list_cn .= "<td style='padding: 10px;'>". date("d-m-Y", strtotime($staff['hire_date'])) ."</td>";//入职日期
                $table_list_cn .= "<td style='padding: 10px;'>". (empty($staff['disability_certificate']) ? '-' : $staff['disability_certificate']) ."</td>";//残疾证号
                $table_list_cn .= "</tr>";
            }
            $content_cn =
                "<table border='1' cellspacing='0' cellpadding='0'>
                <th>员工姓名/工号</th>
                <th>职位</th>
                <th>部门</th>
                <th>网点</th>
                <th>入职日期</th>
                <th>残疾证编号</th>
                {$table_list_cn}
            </table>";

            $foot = "<p><strong>The system automatically sends emails. Please do not reply.</strong></p>";

            $send_mail = Yii::$app->mailer->compose()
                ->setSubject($subject)
                ->setHtmlBody($head_th . $content_th . $foot . $head_cn . $content_cn . $foot);

            $send_mail->setTo($recipient_email);
            $result = $send_mail->send();
            $this->echo('收件人邮箱:'.json_encode($recipient_email).';send_email_result:' . json_encode($result));
        } catch (\Exception $e) {
            $this->echo("TheDayBeforeSendEmail : error " . $e->getMessage());
            Yii::$app->logger->write_log('TheDayBeforeSendEmail，可能出现的问题:'.$e->getMessage().';行号：'.$e->getLine());
        }
    }

    /**
     * 组织架构负责人变更 更新上级
     * @return void
     */
    public function actionOaSyncStaffManagerUpdate()
    {
        $this->echo("actionOaSyncStaffManagerUpdatePop start");
        $data = Yii::$app->redis->rpop(RedisListKeyEnums::OA_SYNC_STAFF_MANAGER_UPDATE);
        if (!empty($data)) {
            sleep(3);
            $param = json_decode($data, true);
            $res = (new StaffManager())->oaSyncStaffManagerUpdatePop($param);
            $this->echo("actionOaSyncStaffManagerUpdatePop end " .$res);
        }
    }

    /**
     * 组织变更，当前负责人变更 为新组织的负责人为上级
     * @return void
     */
    public function actionHcmSyncStaffManagerUpdate()
    {
        $this->echo("actionHcmSyncStaffManagerUpdate start");
        $data = Yii::$app->redis->rpop(RedisListKeyEnums::HCM_SYNC_STAFF_MANAGER_UPDATE);
        if (!empty($data)) {
            sleep(3);
            $param = json_decode($data, true);
            $res = (new StaffManager())->hcmSyncStaffManagerUpdatePop($param);
            $this->echo("actionOaSyncStaffManagerUpdatePop end " .$res);
        }
    }

    /**
     * 网点主管自动同步为网点负责人
     * @return void
     */
    public function actionChangeStoreSupervisorManager()
    {
        $this->echo("actionChangeStoreSupervisorManager start");
        $data = Yii::$app->redis->rpop(RedisListKeyEnums::CHANGE_STORE_SUPERVISOR_MANAGER);
        if (!empty($data)) {
            sleep(3);
            $params = json_decode($data, true);
            $this->echo('actionChangeStoreSupervisorManager', $data);
            $result = StaffService::getInstance()->changeStoreSupervisorManager($params);
            $this->echo('actionChangeStoreSupervisorManager result', $result);
            Yii::$app->logger->write_log('actionChangeStoreSupervisorManager require:' . $data . 'response: ' . $result,
                'info');
        } else {
            $this->echo('actionChangeStoreSupervisorManager data empty');
        }

        $this->echo("actionChangeStoreSupervisorManager end");
    }

    /**
     * 网点主管自动同步为网点负责人候补脚本10分钟一次
     * @return void
     */
    public function actionChangeStoreSupervisorManagerAlternate()
    {
        $this->echo("actionChangeStoreSupervisorManagerAlternate start");
        while ($data = Yii::$app->redis->rpop(RedisListKeyEnums::CHANGE_STORE_SUPERVISOR_MANAGER_ALTERNATE)) {
            $params = json_decode($data, true);
            $count  = $params['count'] ?? 0;
            if ($count < 6) { //重试5次之后丢弃
                $count > 1 && sleep($count*2);
                $this->echo('actionChangeStoreSupervisorManagerAlternate', $data);
                $result = StaffService::getInstance()->changeStoreSupervisorManager($params);
                $this->echo('actionChangeStoreSupervisorManagerAlternate result', $result);
                Yii::$app->logger->write_log('actionChangeStoreSupervisorManagerAlternate require:' . $data . 'response: ' . $result,
                    'info');
            } else {
                Yii::$app->logger->write_log([
                    'message' => '网点主管自动同步为网点负责人候补脚本重试超过5次',
                    'params'  => $params,
                ]);
            }
        }
        $this->echo("actionChangeStoreSupervisorManagerAlternate end");
    }

    //同步新离职资产员工在职离职信息 任务配置在 mse
    //./yii staff/sync-resign-assets-staff
    public function actionSyncResignAssetsStaff()
    {
        while ($data = Yii::$app->redis->rpop(RedisListKeyEnums::SYNC_RESIGN_ASSETS_STAFF)){
            if (!empty($data)) {
                sleep(3);
                $params = json_decode($data, true);
                $this->echo('actionSyncResignAssetsStaff data ', $data);
                $result = StaffService::getInstance()->syncResignAssetsStaff($params);
                $this->echo('actionSyncResignAssetsStaff result ', json_encode($result));
                Yii::$app->logger->write_log('actionSyncResignAssetsStaff require:' . json_encode($data) . 'response: ' . json_encode($result),
                    'info');//响应
            } else {
                $this->echo('actionSyncResignAssetsStaff data empty');
            }
        }
    }

    //同步hold 处理状态 任务配置在mes
    //./yii staff/sync-backyard-source-hold-handle
    public function actionSyncBackyardSourceHoldHandle() {
        $this->echo("actionSyncBackyardSourceHoldHandle start");
        $data = Yii::$app->redis->rpop(RedisListKeyEnums::SYNC_BACKYARD_SOURCE_HOLD_HANDLE);
        if (!empty($data)) {
            $params = json_decode($data, true);
            $this->echo('actionSyncBackyardSourceHoldHandle data ', $data);
            $result = StaffService::getInstance()->syncBackyardSourceHoldHandle($params);
            $this->echo('actionSyncBackyardSourceHoldHandle result ', json_encode($result));
            Yii::$app->logger->write_log('actionSyncBackyardSourceHoldHandle require:' . json_encode($data) . 'response: ' . json_encode($result), 'info');//响应
        } else {
            $this->echo('actionSyncBackyardSourceHoldHandle data empty');
        }
        $this->echo("actionSyncBackyardSourceHoldHandle end");
    }

    //同步社保离职日期 任务配置在mes
    //./yii staff/social-security-leave-date
    public function actionSocialSecurityLeaveDate() {
        $this->echo("actionSocialSecurityLeaveDate start");
        $data = Yii::$app->redis->rpop(RedisListKeyEnums::SOCIAL_SECURITY_LEAVE_DATE);
        if (empty($data)){
            return true;
        }
        $params = json_decode($data, true);
        $result = StaffService::getInstance()->syncSocialSecurityLeaveDate($params);
        Yii::$app->logger->write_log('actionSocialSecurityLeaveDate require:' . json_encode($data) . 'response: ' . json_encode($result), 'info');//响应
        return true;
    }

    //撤销离职 任务配置在mes
    //./yii staff/leave-manager-cancel
    public function actionLeaveManagerCancel() {
        $this->echo("actionLeaveManagerCancel start");
        $data = Yii::$app->redis->rpop(RedisListKeyEnums::LEAVE_MANAGER_CANCEL);
        if (empty($data)){
            return true;
        }
        sleep(3);
        $params = json_decode($data, true);
        $leaveManageLog = LeaveManageLog::find()->where([
            'staff_info_id' => $params['staff_info_id'],
        ])->orderBy('id desc')->one();
        if (empty($leaveManageLog) || empty($leaveManageLog->is_cancel_staff_resign) || $leaveManageLog->is_cancel_staff_resign == 1){
            $data = [
                'staff_info_id' => $params['staff_info_id'],
                'reason' => '员工管理编辑在职状态撤销',
                'fbid' => $params['fbid'] ?? '',
                'is_cancel_staff_resign' => 1,
            ];
            $result = StaffService::getInstance()->syncLeaveManagerCancel($data);
            Yii::$app->logger->write_log('actionLeaveManagerCancel require:' . json_encode($data) . 'response: ' . json_encode($result), 'info');//响应
        }else{
            $this->echo('actionLeaveManagerCancel is_cancel_staff_resign =2');
        }
        return true;
    }

    /*========================================================*/

    public function actionImportWorkingDayRestType($is_update = 0) {
        $sql = "SELECT
                `node_department_id`, `job_title`, `week_working_day`, `rest_type` 
            FROM
                hr_staff_info 
            WHERE
                `formal` IN ( 1, 4 ) 
                AND `is_sub_staff` = 0 
            GROUP BY
                `node_department_id`, `job_title`, `week_working_day`, `rest_type`;";
        $list = Yii::$app->get('backyard')->createCommand($sql)->queryAll();
        echo '总数量:' . count($list) .PHP_EOL;
        $error_count = 0;
        $staff_department_job_title_relation = [];
        foreach ($list as $key => $value) {
            if(empty($value['node_department_id']) || empty($value['job_title']) || empty($value['week_working_day']) || empty($value['rest_type'])) {
                $error_count++;
                echo '异常数据:'.json_encode($value) .PHP_EOL;
            } else {
                $department_id = $value['node_department_id'];
                $job_title = $value['job_title'];
                $working_day_rest_type = $value['week_working_day'] . $value['rest_type'];
                $relation_key = $department_id . '_' . $job_title;
                $staff_department_job_title_relation[$relation_key][] = $working_day_rest_type;
            }
        }

        echo '异常数据总量:' . $error_count . PHP_EOL;
        echo '==========================================' . PHP_EOL;

        $relation_list = HrJobDepartmentRelation::find()->asArray()->all();
        foreach ($relation_list as $key => $value) {
            $staff_relation_key = $value['department_id'] . '_' . $value['job_id'];
            if(!isset($staff_department_job_title_relation[$staff_relation_key])) {
                continue;
            }

            $working_day_rest_type = implode(',', $staff_department_job_title_relation[$staff_relation_key]);
            $update_sql = "UPDATE `hr_job_department_relation` SET `working_day_rest_type` = '". $working_day_rest_type ."' WHERE `department_id` = ". $value['department_id'] ." and `job_id` = ". $value['job_id'] .";";
            if($is_update == 1) {
                $result = Yii::$app->get('backyard_main')->createCommand($update_sql)->execute();
                if($result === false) {
                    echo $staff_relation_key . ' - ' . $working_day_rest_type . '更新失败' . PHP_EOL;
                } else {
                    echo $staff_relation_key . ' - ' . $working_day_rest_type . '更新成功' . PHP_EOL;
                }
            }
        }
    }

    //临时处理sendStaffLeavelMQ {"staff_info_id":65230,"leave_date":"2022-03-17"}
    public function actionSendStaffLeaveMqTemp() {
        return false;
        $s = '[
{"staff_info_id":3164663,"leave_date":"2022-08-28"},
{"staff_info_id":3165148,"leave_date":"2022-08-28"},
{"staff_info_id":352392,"leave_date":"2022-08-28"},
{"staff_info_id":3092141,"leave_date":"2022-08-28"},
{"staff_info_id":3148125,"leave_date":"2022-08-28"},
{"staff_info_id":3122303,"leave_date":"2022-08-28"},
{"staff_info_id":3108019,"leave_date":"2022-08-28"},
{"staff_info_id":3160950,"leave_date":"2022-08-28"},
{"staff_info_id":3178332,"leave_date":"2022-08-28"},
{"staff_info_id":3191279,"leave_date":"2022-08-28"},
{"staff_info_id":3139774,"leave_date":"2022-08-28"},
{"staff_info_id":3128947,"leave_date":"2022-08-28"},
{"staff_info_id":3142298,"leave_date":"2022-08-28"},
{"staff_info_id":3133040,"leave_date":"2022-08-28"},
{"staff_info_id":3188111,"leave_date":"2022-08-28"},
{"staff_info_id":3070530,"leave_date":"2022-08-28"},
{"staff_info_id":462265,"leave_date":"2022-08-28"},
{"staff_info_id":3186624,"leave_date":"2022-08-28"},
{"staff_info_id":3122452,"leave_date":"2022-08-28"},
{"staff_info_id":3191138,"leave_date":"2022-08-28"},
{"staff_info_id":3092483,"leave_date":"2022-08-28"},
{"staff_info_id":3191280,"leave_date":"2022-08-28"},
{"staff_info_id":3179332,"leave_date":"2022-08-28"},
{"staff_info_id":3185662,"leave_date":"2022-08-28"},
{"staff_info_id":3186646,"leave_date":"2022-08-28"},
{"staff_info_id":3179334,"leave_date":"2022-08-28"},
{"staff_info_id":3187113,"leave_date":"2022-08-28"},
{"staff_info_id":3179683,"leave_date":"2022-08-28"},
{"staff_info_id":3049364,"leave_date":"2022-08-28"},
{"staff_info_id":3076850,"leave_date":"2022-08-28"},
{"staff_info_id":3077836,"leave_date":"2022-08-28"},
{"staff_info_id":3190186,"leave_date":"2022-08-28"},
{"staff_info_id":3141548,"leave_date":"2022-08-28"},
{"staff_info_id":3187942,"leave_date":"2022-08-28"},
{"staff_info_id":3164060,"leave_date":"2022-08-28"},
{"staff_info_id":3139778,"leave_date":"2022-08-28"},
{"staff_info_id":3100712,"leave_date":"2022-08-28"},
{"staff_info_id":3178412,"leave_date":"2022-08-28"},
{"staff_info_id":3055468,"leave_date":"2022-08-28"},
{"staff_info_id":352033,"leave_date":"2022-08-28"},
{"staff_info_id":3142963,"leave_date":"2022-08-28"},
{"staff_info_id":3191282,"leave_date":"2022-08-28"},
{"staff_info_id":3142258,"leave_date":"2022-08-28"},
{"staff_info_id":3093455,"leave_date":"2022-08-28"},
{"staff_info_id":3073844,"leave_date":"2022-08-28"},
{"staff_info_id":3182875,"leave_date":"2022-08-28"},
{"staff_info_id":352312,"leave_date":"2022-08-28"},
{"staff_info_id":398349,"leave_date":"2022-08-28"},
{"staff_info_id":3131104,"leave_date":"2022-08-28"},
{"staff_info_id":3074917,"leave_date":"2022-08-28"},
{"staff_info_id":3177922,"leave_date":"2022-08-28"},
{"staff_info_id":3170771,"leave_date":"2022-08-28"},
{"staff_info_id":335018,"leave_date":"2022-08-28"},
{"staff_info_id":458641,"leave_date":"2022-08-28"},
{"staff_info_id":3186625,"leave_date":"2022-08-28"},
{"staff_info_id":3142252,"leave_date":"2022-08-28"},
{"staff_info_id":462762,"leave_date":"2022-08-28"},
{"staff_info_id":3120466,"leave_date":"2022-08-28"},
{"staff_info_id":3122293,"leave_date":"2022-08-28"},
{"staff_info_id":3191283,"leave_date":"2022-08-28"},
{"staff_info_id":3132009,"leave_date":"2022-08-28"},
{"staff_info_id":462062,"leave_date":"2022-08-28"},
{"staff_info_id":3186645,"leave_date":"2022-08-28"},
{"staff_info_id":3177956,"leave_date":"2022-08-28"},
{"staff_info_id":3107961,"leave_date":"2022-08-28"},
{"staff_info_id":3179337,"leave_date":"2022-08-28"},
{"staff_info_id":3142297,"leave_date":"2022-08-28"},
{"staff_info_id":3158671,"leave_date":"2022-08-28"},
{"staff_info_id":3163877,"leave_date":"2022-08-28"},
{"staff_info_id":3191285,"leave_date":"2022-08-28"},
{"staff_info_id":479582,"leave_date":"2022-08-28"},
{"staff_info_id":3179349,"leave_date":"2022-08-28"},
{"staff_info_id":3139108,"leave_date":"2022-08-28"},
{"staff_info_id":182068,"leave_date":"2022-08-28"},
{"staff_info_id":409144,"leave_date":"2022-08-28"},
{"staff_info_id":3142695,"leave_date":"2022-08-28"},
{"staff_info_id":179011,"leave_date":"2022-08-28"},
{"staff_info_id":3182486,"leave_date":"2022-08-28"},
{"staff_info_id":3131997,"leave_date":"2022-08-28"},
{"staff_info_id":3145617,"leave_date":"2022-08-28"},
{"staff_info_id":454836,"leave_date":"2022-08-28"},
{"staff_info_id":3177113,"leave_date":"2022-08-28"},
{"staff_info_id":3187108,"leave_date":"2022-08-28"},
{"staff_info_id":3188335,"leave_date":"2022-08-28"},
{"staff_info_id":3178411,"leave_date":"2022-08-28 00:00:00"},
{"staff_info_id":3158680,"leave_date":"2022-08-28"},
{"staff_info_id":3179107,"leave_date":"2022-08-28"},
{"staff_info_id":3191286,"leave_date":"2022-08-28"},
{"staff_info_id":3093450,"leave_date":"2022-08-28"},
{"staff_info_id":3187923,"leave_date":"2022-08-28"},
{"staff_info_id":3179350,"leave_date":"2022-08-28"},
{"staff_info_id":472927,"leave_date":"2022-08-28"},
{"staff_info_id":3089316,"leave_date":"2022-08-28"},
{"staff_info_id":3157634,"leave_date":"2022-08-28"},
{"staff_info_id":3177472,"leave_date":"2022-08-28"},
{"staff_info_id":3145900,"leave_date":"2022-08-28"},
{"staff_info_id":3187951,"leave_date":"2022-08-28"},
{"staff_info_id":3136009,"leave_date":"2022-08-28"},
{"staff_info_id":3142262,"leave_date":"2022-08-28"},
{"staff_info_id":3171016,"leave_date":"2022-08-28"},
{"staff_info_id":3160953,"leave_date":"2022-08-28"},
{"staff_info_id":3108020,"leave_date":"2022-08-28"},
{"staff_info_id":3092526,"leave_date":"2022-08-28"},
{"staff_info_id":3099174,"leave_date":"2022-08-28"},
{"staff_info_id":3131978,"leave_date":"2022-08-28"},
{"staff_info_id":3133084,"leave_date":"2022-08-28"},
{"staff_info_id":3177483,"leave_date":"2022-08-28"},
{"staff_info_id":3140899,"leave_date":"2022-08-28"},
{"staff_info_id":3082177,"leave_date":"2022-08-28"},
{"staff_info_id":3191287,"leave_date":"2022-08-28"},
{"staff_info_id":3185675,"leave_date":"2022-08-28"},
{"staff_info_id":3131966,"leave_date":"2022-08-28"},
{"staff_info_id":3085485,"leave_date":"2022-08-28"},
{"staff_info_id":402078,"leave_date":"2022-08-28"},
{"staff_info_id":3186159,"leave_date":"2022-08-28"},
{"staff_info_id":3191288,"leave_date":"2022-08-28"},
{"staff_info_id":3188108,"leave_date":"2022-08-28"},
{"staff_info_id":351056,"leave_date":"2022-08-28"},
{"staff_info_id":3146636,"leave_date":"2022-08-28"},
{"staff_info_id":3148135,"leave_date":"2022-08-28"},
{"staff_info_id":3008082,"leave_date":"2022-08-28"},
{"staff_info_id":3007588,"leave_date":"2022-08-28"},
{"staff_info_id":3136004,"leave_date":"2022-08-28"},
{"staff_info_id":3068051,"leave_date":"2022-08-28"},
{"staff_info_id":3177490,"leave_date":"2022-08-28"},
{"staff_info_id":3089323,"leave_date":"2022-08-28"},
{"staff_info_id":3081982,"leave_date":"2022-08-28"},
{"staff_info_id":3122680,"leave_date":"2022-08-28"},
{"staff_info_id":3157632,"leave_date":"2022-08-28"},
{"staff_info_id":3185657,"leave_date":"2022-08-28"},
{"staff_info_id":365557,"leave_date":"2022-08-28"},
{"staff_info_id":3179690,"leave_date":"2022-08-28"},
{"staff_info_id":3092505,"leave_date":"2022-08-28"},
{"staff_info_id":3060262,"leave_date":"2022-08-28"},
{"staff_info_id":343589,"leave_date":"2022-08-28"},
{"staff_info_id":3131173,"leave_date":"2022-08-28"},
{"staff_info_id":3093535,"leave_date":"2022-08-28"},
{"staff_info_id":141511,"leave_date":"2022-08-28"},
{"staff_info_id":3142264,"leave_date":"2022-08-28"},
{"staff_info_id":3129867,"leave_date":"2022-08-28"},
{"staff_info_id":394348,"leave_date":"2022-08-28"},
{"staff_info_id":399329,"leave_date":"2022-08-28"},
{"staff_info_id":3178324,"leave_date":"2022-08-28"},
{"staff_info_id":3044149,"leave_date":"2022-08-28"},
{"staff_info_id":225953,"leave_date":"2022-08-28"},
{"staff_info_id":3170796,"leave_date":"2022-08-28"},
{"staff_info_id":179050,"leave_date":"2022-08-28"},
{"staff_info_id":461943,"leave_date":"2022-08-28"},
{"staff_info_id":3089702,"leave_date":"2022-08-28"},
{"staff_info_id":3093243,"leave_date":"2022-08-28"},
{"staff_info_id":3191151,"leave_date":"2022-08-28"},
{"staff_info_id":170502,"leave_date":"2022-08-28"},
{"staff_info_id":3190553,"leave_date":"2022-08-28"},
{"staff_info_id":3157730,"leave_date":"2022-08-28 00:00:00"},
{"staff_info_id":3170797,"leave_date":"2022-08-28"},
{"staff_info_id":3140577,"leave_date":"2022-08-28"},
{"staff_info_id":3089708,"leave_date":"2022-08-28"},
{"staff_info_id":3187076,"leave_date":"2022-08-28"},
{"staff_info_id":3128933,"leave_date":"2022-08-28"},
{"staff_info_id":3087687,"leave_date":"2022-08-28"},
{"staff_info_id":463755,"leave_date":"2022-08-28"},
{"staff_info_id":3081976,"leave_date":"2022-08-28"},
{"staff_info_id":3093601,"leave_date":"2022-08-28"},
{"staff_info_id":371835,"leave_date":"2022-08-28"},
{"staff_info_id":3070532,"leave_date":"2022-08-28"},
{"staff_info_id":3073643,"leave_date":"2022-08-28"},
{"staff_info_id":3182484,"leave_date":"2022-08-28"},
{"staff_info_id":3140591,"leave_date":"2022-08-28"},
{"staff_info_id":3074237,"leave_date":"2022-08-28"},
{"staff_info_id":3123672,"leave_date":"2022-08-28"},
{"staff_info_id":3130936,"leave_date":"2022-08-28"},
{"staff_info_id":172393,"leave_date":"2022-08-28"},
{"staff_info_id":3139159,"leave_date":"2022-08-28"},
{"staff_info_id":3165154,"leave_date":"2022-08-28"},
{"staff_info_id":3055276,"leave_date":"2022-08-28"},
{"staff_info_id":3129169,"leave_date":"2022-08-28"},
{"staff_info_id":415292,"leave_date":"2022-08-28"},
{"staff_info_id":3170772,"leave_date":"2022-08-28"},
{"staff_info_id":3190546,"leave_date":"2022-08-28"},
{"staff_info_id":3190547,"leave_date":"2022-08-28"},
{"staff_info_id":3087793,"leave_date":"2022-08-28"},
{"staff_info_id":3190391,"leave_date":"2022-08-28"},
{"staff_info_id":319904,"leave_date":"2022-08-28"},
{"staff_info_id":365346,"leave_date":"2022-08-28"},
{"staff_info_id":3191150,"leave_date":"2022-08-28"},
{"staff_info_id":460816,"leave_date":"2022-08-28"},
{"staff_info_id":3068000,"leave_date":"2022-08-28"},
{"staff_info_id":3182646,"leave_date":"2022-08-28"},
{"staff_info_id":3059987,"leave_date":"2022-08-28"},
{"staff_info_id":383041,"leave_date":"2022-08-28"},
{"staff_info_id":3045189,"leave_date":"2022-08-28"},
{"staff_info_id":3007822,"leave_date":"2022-08-28"},
{"staff_info_id":3160944,"leave_date":"2022-08-28"},
{"staff_info_id":3074569,"leave_date":"2022-08-28"},
{"staff_info_id":3093333,"leave_date":"2022-08-28"},
{"staff_info_id":3143033,"leave_date":"2022-08-28"},
{"staff_info_id":3148112,"leave_date":"2022-08-28"},
{"staff_info_id":3123644,"leave_date":"2022-08-28"},
{"staff_info_id":3131991,"leave_date":"2022-08-28"},
{"staff_info_id":3142672,"leave_date":"2022-08-28"},
{"staff_info_id":3089932,"leave_date":"2022-08-28"},
{"staff_info_id":3120486,"leave_date":"2022-08-28"},
{"staff_info_id":3077293,"leave_date":"2022-08-28"},
{"staff_info_id":393783,"leave_date":"2022-08-28"},
{"staff_info_id":3013838,"leave_date":"2022-08-28"},
{"staff_info_id":3093231,"leave_date":"2022-08-28"},
{"staff_info_id":3131841,"leave_date":"2022-08-28"},
{"staff_info_id":3131660,"leave_date":"2022-08-28"},
{"staff_info_id":3158254,"leave_date":"2022-08-28"},
{"staff_info_id":3182493,"leave_date":"2022-08-28"},
{"staff_info_id":473062,"leave_date":"2022-08-28"},
{"staff_info_id":3177475,"leave_date":"2022-08-28"},
{"staff_info_id":499431,"leave_date":"2022-08-28"},
{"staff_info_id":3092839,"leave_date":"2022-08-28"},
{"staff_info_id":3157789,"leave_date":"2022-08-28"},
{"staff_info_id":3108974,"leave_date":"2022-08-28"},
{"staff_info_id":3090056,"leave_date":"2022-08-28"},
{"staff_info_id":3187078,"leave_date":"2022-08-28"},
{"staff_info_id":3064549,"leave_date":"2022-08-28"},
{"staff_info_id":358874,"leave_date":"2022-08-28"},
{"staff_info_id":3190554,"leave_date":"2022-08-28"},
{"staff_info_id":3092946,"leave_date":"2022-08-28"},
{"staff_info_id":205603,"leave_date":"2022-08-28"},
{"staff_info_id":3121402,"leave_date":"2022-08-28"},
{"staff_info_id":3092720,"leave_date":"2022-08-28"},
{"staff_info_id":3177485,"leave_date":"2022-08-28"},
{"staff_info_id":3191292,"leave_date":"2022-08-28"},
{"staff_info_id":3177124,"leave_date":"2022-08-28"},
{"staff_info_id":3182635,"leave_date":"2022-08-28"},
{"staff_info_id":3158687,"leave_date":"2022-08-28"},
{"staff_info_id":3173466,"leave_date":"2022-08-28"},
{"staff_info_id":3104743,"leave_date":"2022-08-28"},
{"staff_info_id":3157846,"leave_date":"2022-08-28"},
{"staff_info_id":460231,"leave_date":"2022-08-28"},
{"staff_info_id":3064624,"leave_date":"2022-08-28"},
{"staff_info_id":3148141,"leave_date":"2022-08-28"},
{"staff_info_id":3160942,"leave_date":"2022-08-28"},
{"staff_info_id":3158277,"leave_date":"2022-08-28"},
{"staff_info_id":3140652,"leave_date":"2022-08-28"},
{"staff_info_id":3148368,"leave_date":"2022-08-28"},
{"staff_info_id":3185668,"leave_date":"2022-08-28"},
{"staff_info_id":382178,"leave_date":"2022-08-28"},
{"staff_info_id":3140631,"leave_date":"2022-08-28"},
{"staff_info_id":3164454,"leave_date":"2022-08-28"},
{"staff_info_id":3037766,"leave_date":"2022-08-28"},
{"staff_info_id":3145623,"leave_date":"2022-08-28"},
{"staff_info_id":3182480,"leave_date":"2022-08-28"},
{"staff_info_id":361647,"leave_date":"2022-08-28"},
{"staff_info_id":3191167,"leave_date":"2022-08-28"},
{"staff_info_id":3182455,"leave_date":"2022-08-28"},
{"staff_info_id":373841,"leave_date":"2022-08-28"},
{"staff_info_id":3074220,"leave_date":"2022-08-28"},
{"staff_info_id":3182636,"leave_date":"2022-08-28"},
{"staff_info_id":3178329,"leave_date":"2022-08-28"},
{"staff_info_id":3104749,"leave_date":"2022-08-28"},
{"staff_info_id":3077715,"leave_date":"2022-08-28"},
{"staff_info_id":458705,"leave_date":"2022-08-28"},
{"staff_info_id":3092519,"leave_date":"2022-08-28"},
{"staff_info_id":3173467,"leave_date":"2022-08-28"},
{"staff_info_id":342694,"leave_date":"2022-08-28"},
{"staff_info_id":360719,"leave_date":"2022-08-28"},
{"staff_info_id":3108969,"leave_date":"2022-08-28"},
{"staff_info_id":3191293,"leave_date":"2022-08-28"},
{"staff_info_id":3016419,"leave_date":"2022-08-28"},
{"staff_info_id":3122267,"leave_date":"2022-08-28"},
{"staff_info_id":3178742,"leave_date":"2022-08-28"},
{"staff_info_id":3131817,"leave_date":"2022-08-28"},
{"staff_info_id":3179723,"leave_date":"2022-08-28"},
{"staff_info_id":3158686,"leave_date":"2022-08-28"},
{"staff_info_id":3191295,"leave_date":"2022-08-28"},
{"staff_info_id":3139761,"leave_date":"2022-08-28"},
{"staff_info_id":3143073,"leave_date":"2022-08-28"},
{"staff_info_id":3164879,"leave_date":"2022-08-28"},
{"staff_info_id":3191296,"leave_date":"2022-08-28"},
{"staff_info_id":3073939,"leave_date":"2022-08-28"},
{"staff_info_id":3160943,"leave_date":"2022-08-28"},
{"staff_info_id":3086164,"leave_date":"2022-08-28"},
{"staff_info_id":3054863,"leave_date":"2022-08-28"},
{"staff_info_id":3055465,"leave_date":"2022-08-28"},
{"staff_info_id":3032207,"leave_date":"2022-08-28"},
{"staff_info_id":3190211,"leave_date":"2022-08-28"},
{"staff_info_id":3130907,"leave_date":"2022-08-28"},
{"staff_info_id":3139803,"leave_date":"2022-08-28"},
{"staff_info_id":3089936,"leave_date":"2022-08-28"},
{"staff_info_id":394115,"leave_date":"2022-08-28"},
{"staff_info_id":3188330,"leave_date":"2022-08-28"},
{"staff_info_id":3055464,"leave_date":"2022-08-28"},
{"staff_info_id":3139801,"leave_date":"2022-08-28"},
{"staff_info_id":3190981,"leave_date":"2022-08-28"},
{"staff_info_id":479577,"leave_date":"2022-08-28"},
{"staff_info_id":3187065,"leave_date":"2022-08-28"},
{"staff_info_id":368846,"leave_date":"2022-08-28"},
{"staff_info_id":3077045,"leave_date":"2022-08-28"},
{"staff_info_id":172688,"leave_date":"2022-08-28"},
{"staff_info_id":336978,"leave_date":"2022-08-28"},
{"staff_info_id":463081,"leave_date":"2022-08-28"},
{"staff_info_id":3133093,"leave_date":"2022-08-28"},
{"staff_info_id":279956,"leave_date":"2022-08-28"},
{"staff_info_id":3177171,"leave_date":"2022-08-28"},
{"staff_info_id":3108790,"leave_date":"2022-08-28"},
{"staff_info_id":3179674,"leave_date":"2022-08-28"},
{"staff_info_id":360381,"leave_date":"2022-08-28"},
{"staff_info_id":3077238,"leave_date":"2022-08-28"},
{"staff_info_id":460773,"leave_date":"2022-08-28"},
{"staff_info_id":337045,"leave_date":"2022-08-28"},
{"staff_info_id":255581,"leave_date":"2022-08-28"},
{"staff_info_id":399694,"leave_date":"2022-08-28"},
{"staff_info_id":3177461,"leave_date":"2022-08-28"},
{"staff_info_id":3121576,"leave_date":"2022-08-28"},
{"staff_info_id":383233,"leave_date":"2022-08-28"},
{"staff_info_id":3077862,"leave_date":"2022-08-28"},
{"staff_info_id":3049084,"leave_date":"2022-08-28"},
{"staff_info_id":3179358,"leave_date":"2022-08-28"},
{"staff_info_id":319908,"leave_date":"2022-08-28"},
{"staff_info_id":3191326,"leave_date":"2022-08-28"},
{"staff_info_id":173255,"leave_date":"2022-08-28"},
{"staff_info_id":3129877,"leave_date":"2022-08-28"},
{"staff_info_id":3191297,"leave_date":"2022-08-28"},
{"staff_info_id":394016,"leave_date":"2022-08-28"},
{"staff_info_id":3188350,"leave_date":"2022-08-28"},
{"staff_info_id":3082490,"leave_date":"2022-08-28"},
{"staff_info_id":3090073,"leave_date":"2022-08-28"},
{"staff_info_id":3182905,"leave_date":"2022-08-28"},
{"staff_info_id":3187927,"leave_date":"2022-08-28"},
{"staff_info_id":3000010,"leave_date":"2022-08-28"},
{"staff_info_id":3142660,"leave_date":"2022-08-28"},
{"staff_info_id":3191298,"leave_date":"2022-08-28"},
{"staff_info_id":3129399,"leave_date":"2022-08-28"},
{"staff_info_id":3128955,"leave_date":"2022-08-28"},
{"staff_info_id":3191165,"leave_date":"2022-08-28"},
{"staff_info_id":3140932,"leave_date":"2022-08-28"},
{"staff_info_id":3123859,"leave_date":"2022-08-28"},
{"staff_info_id":3128941,"leave_date":"2022-08-28"},
{"staff_info_id":3191156,"leave_date":"2022-08-28"},
{"staff_info_id":3186638,"leave_date":"2022-08-28"},
{"staff_info_id":3082171,"leave_date":"2022-08-28"},
{"staff_info_id":3131819,"leave_date":"2022-08-28"},
{"staff_info_id":3060259,"leave_date":"2022-08-28"},
{"staff_info_id":3153710,"leave_date":"2022-08-28"},
{"staff_info_id":3044250,"leave_date":"2022-08-28"},
{"staff_info_id":3191152,"leave_date":"2022-08-28"},
{"staff_info_id":3034579,"leave_date":"2022-08-28"},
{"staff_info_id":459111,"leave_date":"2022-08-28"},
{"staff_info_id":3073624,"leave_date":"2022-08-28"},
{"staff_info_id":3145931,"leave_date":"2022-08-28"},
{"staff_info_id":3188115,"leave_date":"2022-08-28"},
{"staff_info_id":350772,"leave_date":"2022-08-28"},
{"staff_info_id":3146677,"leave_date":"2022-08-28"},
{"staff_info_id":3139805,"leave_date":"2022-08-28"},
{"staff_info_id":3183136,"leave_date":"2022-08-28"},
{"staff_info_id":173477,"leave_date":"2022-08-28"},
{"staff_info_id":3191299,"leave_date":"2022-08-28"},
{"staff_info_id":3177470,"leave_date":"2022-08-28"},
{"staff_info_id":3123219,"leave_date":"2022-08-28"},
{"staff_info_id":3188142,"leave_date":"2022-08-28"},
{"staff_info_id":3096594,"leave_date":"2022-08-28"},
{"staff_info_id":3142325,"leave_date":"2022-08-28"},
{"staff_info_id":3141606,"leave_date":"2022-08-28"},
{"staff_info_id":3153709,"leave_date":"2022-08-28"},
{"staff_info_id":3085956,"leave_date":"2022-08-28"},
{"staff_info_id":3096699,"leave_date":"2022-08-28"},
{"staff_info_id":3190557,"leave_date":"2022-08-28"},
{"staff_info_id":3190558,"leave_date":"2022-08-28"},
{"staff_info_id":3190191,"leave_date":"2022-08-28"},
{"staff_info_id":3076894,"leave_date":"2022-08-28"},
{"staff_info_id":3099167,"leave_date":"2022-08-28"},
{"staff_info_id":3177142,"leave_date":"2022-08-28"},
{"staff_info_id":3178310,"leave_date":"2022-08-28"},
{"staff_info_id":3123207,"leave_date":"2022-08-28"},
{"staff_info_id":393759,"leave_date":"2022-08-28"},
{"staff_info_id":3190994,"leave_date":"2022-08-28"},
{"staff_info_id":401926,"leave_date":"2022-08-28"},
{"staff_info_id":397740,"leave_date":"2022-08-28"},
{"staff_info_id":381098,"leave_date":"2022-08-28"},
{"staff_info_id":3188078,"leave_date":"2022-08-28"},
{"staff_info_id":3188079,"leave_date":"2022-08-28"},
{"staff_info_id":3096607,"leave_date":"2022-08-28"},
{"staff_info_id":3187103,"leave_date":"2022-08-28"},
{"staff_info_id":3146676,"leave_date":"2022-08-28"},
{"staff_info_id":392615,"leave_date":"2022-08-28"},
{"staff_info_id":3132012,"leave_date":"2022-08-28"},
{"staff_info_id":3141591,"leave_date":"2022-08-28"},
{"staff_info_id":3092499,"leave_date":"2022-08-28"},
{"staff_info_id":3081969,"leave_date":"2022-08-28"},
{"staff_info_id":3082023,"leave_date":"2022-08-28"},
{"staff_info_id":3130665,"leave_date":"2022-08-28"},
{"staff_info_id":3096709,"leave_date":"2022-08-28"},
{"staff_info_id":3190974,"leave_date":"2022-08-28"},
{"staff_info_id":3164457,"leave_date":"2022-08-28"},
{"staff_info_id":3045704,"leave_date":"2022-08-28"},
{"staff_info_id":3186664,"leave_date":"2022-08-28"},
{"staff_info_id":3186170,"leave_date":"2022-08-28"},
{"staff_info_id":360784,"leave_date":"2022-08-28"},
{"staff_info_id":3064700,"leave_date":"2022-08-28"},
{"staff_info_id":409149,"leave_date":"2022-08-28"},
{"staff_info_id":3081836,"leave_date":"2022-08-28"},
{"staff_info_id":3099178,"leave_date":"2022-08-28"},
{"staff_info_id":3129863,"leave_date":"2022-08-28"},
{"staff_info_id":3139804,"leave_date":"2022-08-28"},
{"staff_info_id":3138633,"leave_date":"2022-08-28"},
{"staff_info_id":3139170,"leave_date":"2022-08-28"},
{"staff_info_id":3190189,"leave_date":"2022-08-28"},
{"staff_info_id":3145632,"leave_date":"2022-08-28"},
{"staff_info_id":3140639,"leave_date":"2022-08-28"},
{"staff_info_id":3055915,"leave_date":"2022-08-28"},
{"staff_info_id":454835,"leave_date":"2022-08-28"},
{"staff_info_id":3123875,"leave_date":"2022-08-28"},
{"staff_info_id":3074350,"leave_date":"2022-08-28"},
{"staff_info_id":3073464,"leave_date":"2022-08-28"},
{"staff_info_id":3139169,"leave_date":"2022-08-28"},
{"staff_info_id":3100764,"leave_date":"2022-08-28"},
{"staff_info_id":3179121,"leave_date":"2022-08-28"},
{"staff_info_id":3077240,"leave_date":"2022-08-28"},
{"staff_info_id":3140968,"leave_date":"2022-08-28"},
{"staff_info_id":3096597,"leave_date":"2022-08-28"},
{"staff_info_id":461817,"leave_date":"2022-08-28"},
{"staff_info_id":458258,"leave_date":"2022-08-28"},
{"staff_info_id":461812,"leave_date":"2022-08-28"},
{"staff_info_id":3177118,"leave_date":"2022-08-28"},
{"staff_info_id":3182906,"leave_date":"2022-08-28"},
{"staff_info_id":173343,"leave_date":"2022-08-28"},
{"staff_info_id":3187102,"leave_date":"2022-08-28"},
{"staff_info_id":3188103,"leave_date":"2022-08-28"},
{"staff_info_id":3129144,"leave_date":"2022-08-28"},
{"staff_info_id":179492,"leave_date":"2022-08-28"},
{"staff_info_id":202809,"leave_date":"2022-08-28"},
{"staff_info_id":361682,"leave_date":"2022-08-28"},
{"staff_info_id":3177948,"leave_date":"2022-08-28"},
{"staff_info_id":396862,"leave_date":"2022-08-28"},
{"staff_info_id":3164657,"leave_date":"2022-08-28"},
{"staff_info_id":3073868,"leave_date":"2022-08-28"},
{"staff_info_id":3187101,"leave_date":"2022-08-28"},
{"staff_info_id":3121376,"leave_date":"2022-08-28"},
{"staff_info_id":3087783,"leave_date":"2022-08-28"},
{"staff_info_id":3139180,"leave_date":"2022-08-28"},
{"staff_info_id":3178764,"leave_date":"2022-08-28"},
{"staff_info_id":168557,"leave_date":"2022-08-28"},
{"staff_info_id":402532,"leave_date":"2022-08-28"},
{"staff_info_id":3178763,"leave_date":"2022-08-28"},
{"staff_info_id":414327,"leave_date":"2022-08-28"},
{"staff_info_id":3092289,"leave_date":"2022-08-28"},
{"staff_info_id":3085501,"leave_date":"2022-08-28"},
{"staff_info_id":3141588,"leave_date":"2022-08-28"},
{"staff_info_id":3164279,"leave_date":"2022-08-28"},
{"staff_info_id":3190997,"leave_date":"2022-08-28"},
{"staff_info_id":3130694,"leave_date":"2022-08-28"},
{"staff_info_id":3178765,"leave_date":"2022-08-28"},
{"staff_info_id":473136,"leave_date":"2022-08-28"},
{"staff_info_id":337161,"leave_date":"2022-08-28"},
{"staff_info_id":3187100,"leave_date":"2022-08-28"},
{"staff_info_id":3145657,"leave_date":"2022-08-28"},
{"staff_info_id":364130,"leave_date":"2022-08-28"},
{"staff_info_id":3143016,"leave_date":"2022-08-28"},
{"staff_info_id":3190993,"leave_date":"2022-08-28"},
{"staff_info_id":382981,"leave_date":"2022-08-28"},
{"staff_info_id":3191158,"leave_date":"2022-08-28"},
{"staff_info_id":3045703,"leave_date":"2022-08-28"},
{"staff_info_id":373640,"leave_date":"2022-08-28"},
{"staff_info_id":3187071,"leave_date":"2022-08-28"},
{"staff_info_id":3190999,"leave_date":"2022-08-28"},
{"staff_info_id":3148137,"leave_date":"2022-08-28"},
{"staff_info_id":3077052,"leave_date":"2022-08-28"},
{"staff_info_id":3177930,"leave_date":"2022-08-28"},
{"staff_info_id":3182884,"leave_date":"2022-08-28"},
{"staff_info_id":3188351,"leave_date":"2022-08-28"},
{"staff_info_id":182131,"leave_date":"2022-08-28"},
{"staff_info_id":3177467,"leave_date":"2022-08-28"},
{"staff_info_id":181903,"leave_date":"2022-08-28"},
{"staff_info_id":3104726,"leave_date":"2022-08-28"},
{"staff_info_id":3093236,"leave_date":"2022-08-28"},
{"staff_info_id":3085792,"leave_date":"2022-08-28"},
{"staff_info_id":3129148,"leave_date":"2022-08-28"},
{"staff_info_id":183496,"leave_date":"2022-08-28"},
{"staff_info_id":144589,"leave_date":"2022-08-28"},
{"staff_info_id":3188352,"leave_date":"2022-08-28"},
{"staff_info_id":3093445,"leave_date":"2022-08-28"},
{"staff_info_id":3128916,"leave_date":"2022-08-28"},
{"staff_info_id":3092111,"leave_date":"2022-08-28"},
{"staff_info_id":3058409,"leave_date":"2022-08-28"},
{"staff_info_id":3108116,"leave_date":"2022-08-28"},
{"staff_info_id":150839,"leave_date":"2022-08-28"},
{"staff_info_id":532034,"leave_date":"2022-08-28"},
{"staff_info_id":458499,"leave_date":"2022-08-28"},
{"staff_info_id":3179089,"leave_date":"2022-08-28"},
{"staff_info_id":3123632,"leave_date":"2022-08-28"},
{"staff_info_id":3164291,"leave_date":"2022-08-28"},
{"staff_info_id":403645,"leave_date":"2022-08-28"},
{"staff_info_id":3085788,"leave_date":"2022-08-28"},
{"staff_info_id":3089908,"leave_date":"2022-08-28"},
{"staff_info_id":3002013,"leave_date":"2022-08-28"},
{"staff_info_id":3178777,"leave_date":"2022-08-28"},
{"staff_info_id":3177498,"leave_date":"2022-08-28"},
{"staff_info_id":3081905,"leave_date":"2022-08-28"},
{"staff_info_id":479313,"leave_date":"2022-08-28"},
{"staff_info_id":360721,"leave_date":"2022-08-28"},
{"staff_info_id":3067968,"leave_date":"2022-08-28"},
{"staff_info_id":3092931,"leave_date":"2022-08-28"},
{"staff_info_id":3008149,"leave_date":"2022-08-28"},
{"staff_info_id":3139115,"leave_date":"2022-08-28"},
{"staff_info_id":3092724,"leave_date":"2022-08-28"},
{"staff_info_id":3070527,"leave_date":"2022-08-28"},
{"staff_info_id":414575,"leave_date":"2022-08-28"},
{"staff_info_id":3131120,"leave_date":"2022-08-28"},
{"staff_info_id":170561,"leave_date":"2022-08-28"},
{"staff_info_id":3136007,"leave_date":"2022-08-28"},
{"staff_info_id":3041853,"leave_date":"2022-08-28"},
{"staff_info_id":3182882,"leave_date":"2022-08-28"},
{"staff_info_id":3138613,"leave_date":"2022-08-28"},
{"staff_info_id":3121374,"leave_date":"2022-08-28"},
{"staff_info_id":3129871,"leave_date":"2022-08-28"},
{"staff_info_id":3085798,"leave_date":"2022-08-28"},
{"staff_info_id":3092929,"leave_date":"2022-08-28"},
{"staff_info_id":3089966,"leave_date":"2022-08-28"},
{"staff_info_id":3121406,"leave_date":"2022-08-28"},
{"staff_info_id":3177496,"leave_date":"2022-08-28"},
{"staff_info_id":3173478,"leave_date":"2022-08-28"},
{"staff_info_id":3131119,"leave_date":"2022-08-28"},
{"staff_info_id":3171012,"leave_date":"2022-08-28"},
{"staff_info_id":3185681,"leave_date":"2022-08-28"},
{"staff_info_id":3145636,"leave_date":"2022-08-28"},
{"staff_info_id":364119,"leave_date":"2022-08-28"},
{"staff_info_id":409131,"leave_date":"2022-08-28"},
{"staff_info_id":3190193,"leave_date":"2022-08-28"},
{"staff_info_id":3131832,"leave_date":"2022-08-28"},
{"staff_info_id":408333,"leave_date":"2022-08-28"},
{"staff_info_id":413993,"leave_date":"2022-08-28"},
{"staff_info_id":3055754,"leave_date":"2022-08-28"},
{"staff_info_id":3123657,"leave_date":"2022-08-28"},
{"staff_info_id":3076839,"leave_date":"2022-08-28"},
{"staff_info_id":3129854,"leave_date":"2022-08-28"},
{"staff_info_id":3145903,"leave_date":"2022-08-28"},
{"staff_info_id":3158676,"leave_date":"2022-08-28"},
{"staff_info_id":3191161,"leave_date":"2022-08-28"},
{"staff_info_id":3140626,"leave_date":"2022-08-28"},
{"staff_info_id":3188122,"leave_date":"2022-08-28"},
{"staff_info_id":319861,"leave_date":"2022-08-28"},
{"staff_info_id":3136016,"leave_date":"2022-08-28"},
{"staff_info_id":3164678,"leave_date":"2022-08-28"},
{"staff_info_id":3177501,"leave_date":"2022-08-28"},
{"staff_info_id":3164461,"leave_date":"2022-08-28"},
{"staff_info_id":3187077,"leave_date":"2022-08-28"},
{"staff_info_id":3185680,"leave_date":"2022-08-28"},
{"staff_info_id":3173456,"leave_date":"2022-08-28"},
{"staff_info_id":417944,"leave_date":"2022-08-28"},
{"staff_info_id":3142606,"leave_date":"2022-08-28"},
{"staff_info_id":3179689,"leave_date":"2022-08-28"},
{"staff_info_id":3123877,"leave_date":"2022-08-28"},
{"staff_info_id":141592,"leave_date":"2022-08-28"},
{"staff_info_id":177451,"leave_date":"2022-08-28"},
{"staff_info_id":3191000,"leave_date":"2022-08-28"},
{"staff_info_id":3157645,"leave_date":"2022-08-28"},
{"staff_info_id":439713,"leave_date":"2022-08-28"},
{"staff_info_id":3142616,"leave_date":"2022-08-28"},
{"staff_info_id":392875,"leave_date":"2022-08-28"},
{"staff_info_id":3035218,"leave_date":"2022-08-28"},
{"staff_info_id":479573,"leave_date":"2022-08-28"},
{"staff_info_id":3178762,"leave_date":"2022-08-28"},
{"staff_info_id":3178316,"leave_date":"2022-08-28"},
{"staff_info_id":3093465,"leave_date":"2022-08-28"},
{"staff_info_id":3093138,"leave_date":"2022-08-28"},
{"staff_info_id":3177459,"leave_date":"2022-08-28"},
{"staff_info_id":3191300,"leave_date":"2022-08-28"},
{"staff_info_id":3186136,"leave_date":"2022-08-28"},
{"staff_info_id":3073708,"leave_date":"2022-08-28"},
{"staff_info_id":3142342,"leave_date":"2022-08-28"},
{"staff_info_id":3089921,"leave_date":"2022-08-28"},
{"staff_info_id":3191001,"leave_date":"2022-08-28"},
{"staff_info_id":3116232,"leave_date":"2022-08-28"},
{"staff_info_id":3060125,"leave_date":"2022-08-28"},
{"staff_info_id":3108783,"leave_date":"2022-08-28"},
{"staff_info_id":3122299,"leave_date":"2022-08-28"},
{"staff_info_id":3049555,"leave_date":"2022-08-28"},
{"staff_info_id":3067996,"leave_date":"2022-08-28"},
{"staff_info_id":3082381,"leave_date":"2022-08-28"},
{"staff_info_id":3187117,"leave_date":"2022-08-28"},
{"staff_info_id":3177170,"leave_date":"2022-08-28"},
{"staff_info_id":3136001,"leave_date":"2022-08-28"},
{"staff_info_id":3092763,"leave_date":"2022-08-28"},
{"staff_info_id":3143034,"leave_date":"2022-08-28"},
{"staff_info_id":3123022,"leave_date":"2022-08-28"},
{"staff_info_id":3136070,"leave_date":"2022-08-28"},
{"staff_info_id":3140964,"leave_date":"2022-08-28"},
{"staff_info_id":3116282,"leave_date":"2022-08-28"},
{"staff_info_id":402519,"leave_date":"2022-08-28"},
{"staff_info_id":3092158,"leave_date":"2022-08-28"},
{"staff_info_id":3086150,"leave_date":"2022-08-28"},
{"staff_info_id":3087778,"leave_date":"2022-08-28"},
{"staff_info_id":168561,"leave_date":"2022-08-28"},
{"staff_info_id":3187119,"leave_date":"2022-08-28"},
{"staff_info_id":3138657,"leave_date":"2022-08-28"},
{"staff_info_id":3191301,"leave_date":"2022-08-28"},
{"staff_info_id":148187,"leave_date":"2022-08-28"},
{"staff_info_id":360492,"leave_date":"2022-08-28"},
{"staff_info_id":3130926,"leave_date":"2022-08-28"},
{"staff_info_id":3179359,"leave_date":"2022-08-28"},
{"staff_info_id":3104747,"leave_date":"2022-08-28"},
{"staff_info_id":3190990,"leave_date":"2022-08-28"},
{"staff_info_id":177884,"leave_date":"2022-08-28"},
{"staff_info_id":3186142,"leave_date":"2022-08-28"},
{"staff_info_id":3073182,"leave_date":"2022-08-28"},
{"staff_info_id":439697,"leave_date":"2022-08-28"},
{"staff_info_id":3104739,"leave_date":"2022-08-28"},
{"staff_info_id":3191173,"leave_date":"2022-08-28"},
{"staff_info_id":3142322,"leave_date":"2022-08-28"},
{"staff_info_id":3131662,"leave_date":"2022-08-28"},
{"staff_info_id":3032388,"leave_date":"2022-08-28"},
{"staff_info_id":3191302,"leave_date":"2022-08-28"},
{"staff_info_id":3191163,"leave_date":"2022-08-28"},
{"staff_info_id":3160947,"leave_date":"2022-08-28"},
{"staff_info_id":3093677,"leave_date":"2022-08-28"},
{"staff_info_id":3121581,"leave_date":"2022-08-28"},
{"staff_info_id":3096255,"leave_date":"2022-08-28"},
{"staff_info_id":456266,"leave_date":"2022-08-28"},
{"staff_info_id":3074530,"leave_date":"2022-08-28"},
{"staff_info_id":3177137,"leave_date":"2022-08-28"},
{"staff_info_id":3131663,"leave_date":"2022-08-28"},
{"staff_info_id":3191303,"leave_date":"2022-08-28"},
{"staff_info_id":3131187,"leave_date":"2022-08-28"},
{"staff_info_id":3077658,"leave_date":"2022-08-28"},
{"staff_info_id":373368,"leave_date":"2022-08-28"},
{"staff_info_id":365041,"leave_date":"2022-08-28"},
{"staff_info_id":375860,"leave_date":"2022-08-28"},
{"staff_info_id":3186666,"leave_date":"2022-08-28"},
{"staff_info_id":3090037,"leave_date":"2022-08-28"},
{"staff_info_id":3132018,"leave_date":"2022-08-28"},
{"staff_info_id":3130954,"leave_date":"2022-08-28"},
{"staff_info_id":3136063,"leave_date":"2022-08-28"},
{"staff_info_id":3096835,"leave_date":"2022-08-28"},
{"staff_info_id":3177486,"leave_date":"2022-08-28"},
{"staff_info_id":465731,"leave_date":"2022-08-28"},
{"staff_info_id":3158262,"leave_date":"2022-08-28"},
{"staff_info_id":3129125,"leave_date":"2022-08-28"},
{"staff_info_id":3191176,"leave_date":"2022-08-28"},
{"staff_info_id":3131177,"leave_date":"2022-08-28"},
{"staff_info_id":177742,"leave_date":"2022-08-28"},
{"staff_info_id":466594,"leave_date":"2022-08-28"},
{"staff_info_id":3055271,"leave_date":"2022-08-28"},
{"staff_info_id":337160,"leave_date":"2022-08-28"},
{"staff_info_id":3015870,"leave_date":"2022-08-28"},
{"staff_info_id":3108799,"leave_date":"2022-08-28"},
{"staff_info_id":3130697,"leave_date":"2022-08-28"},
{"staff_info_id":3148133,"leave_date":"2022-08-28"},
{"staff_info_id":3179675,"leave_date":"2022-08-28"},
{"staff_info_id":3037680,"leave_date":"2022-08-28"},
{"staff_info_id":3188119,"leave_date":"2022-08-28"},
{"staff_info_id":3190209,"leave_date":"2022-08-28"},
{"staff_info_id":3182659,"leave_date":"2022-08-28"},
{"staff_info_id":3074745,"leave_date":"2022-08-28"},
{"staff_info_id":3177961,"leave_date":"2022-08-28"},
{"staff_info_id":3049556,"leave_date":"2022-08-28"},
{"staff_info_id":3164086,"leave_date":"2022-08-28"},
{"staff_info_id":3178579,"leave_date":"2022-08-28"},
{"staff_info_id":380871,"leave_date":"2022-08-28"},
{"staff_info_id":473135,"leave_date":"2022-08-28"},
{"staff_info_id":3077308,"leave_date":"2022-08-28"},
{"staff_info_id":3108803,"leave_date":"2022-08-28"},
{"staff_info_id":3064478,"leave_date":"2022-08-28"},
{"staff_info_id":3007539,"leave_date":"2022-08-28"},
{"staff_info_id":3178772,"leave_date":"2022-08-28"},
{"staff_info_id":3131186,"leave_date":"2022-08-28"},
{"staff_info_id":3190996,"leave_date":"2022-08-28"},
{"staff_info_id":3073840,"leave_date":"2022-08-28"},
{"staff_info_id":3003662,"leave_date":"2022-08-28"},
{"staff_info_id":365909,"leave_date":"2022-08-28"},
{"staff_info_id":437875,"leave_date":"2022-08-28"},
{"staff_info_id":3142250,"leave_date":"2022-08-28"},
{"staff_info_id":3182892,"leave_date":"2022-08-28"},
{"staff_info_id":3141577,"leave_date":"2022-08-28"},
{"staff_info_id":3076842,"leave_date":"2022-08-28"},
{"staff_info_id":3089952,"leave_date":"2022-08-28"},
{"staff_info_id":3130938,"leave_date":"2022-08-28"},
{"staff_info_id":3092521,"leave_date":"2022-08-28"},
{"staff_info_id":3170783,"leave_date":"2022-08-28"},
{"staff_info_id":3074537,"leave_date":"2022-08-28"},
{"staff_info_id":3145928,"leave_date":"2022-08-28"},
{"staff_info_id":3182685,"leave_date":"2022-08-28"},
{"staff_info_id":3171022,"leave_date":"2022-08-28"},
{"staff_info_id":3141598,"leave_date":"2022-08-28"},
{"staff_info_id":3142689,"leave_date":"2022-08-28"},
{"staff_info_id":3121373,"leave_date":"2022-08-28"},
{"staff_info_id":3116254,"leave_date":"2022-08-28"},
{"staff_info_id":3130690,"leave_date":"2022-08-28"},
{"staff_info_id":3139784,"leave_date":"2022-08-28"},
{"staff_info_id":394589,"leave_date":"2022-08-28"},
{"staff_info_id":3183145,"leave_date":"2022-08-28"}
]';
        $data  = json_decode($s,true);

        foreach ($data as $datum) {
            $staff_info_id = $datum['staff_info_id'];
            $leave_date = $datum['leave_date'];

            $this->echo('begin');
            if(!empty($staff_info_id) && !empty($leave_date)) {
                $this->echo('参数:工号:'.$staff_info_id . '-----离职日期:'. $leave_date);
                $staff_info = [
                    'staff_info_id' => $staff_info_id,
                    'leave_date' => $leave_date,
                ];
                $result = Staff::sendStaffLeavelMQ($staff_info);
                $this->echo('结果:'.$result);
            }
        }


        $this->echo('end');
    }

    public function actionResolveRolePosition()
    {
        foreach (StaffInfo::find()->select('staff_info_id,position_category')->asArray()->each(100) as $key => $obj) {
            try {
                echo '#### 开始 staff:' . $obj['staff_info_id'], ':' . $obj['position_category'] . PHP_EOL;
                foreach (explode(',', $obj['position_category']) as $key => $value) {
                    $role = new HrStaffInfoPosition();
                    $role->staff_info_id = $obj['staff_info_id'];
                    $role->position_category = $value;
                    if ($role->save()) {
                        echo '导入staff:' . $role->staff_info_id . ':' . $value . ' OK' . PHP_EOL;
                    }
                }
                echo '#### 结束 staff:' . $obj['staff_info_id'], ':' . $obj['position_category'] . PHP_EOL;
            } catch (\Exception $e) {
                echo 'Exception: ' . $obj['staff_info_id'] . $e->getMessage() . PHP_EOL;
            }

            echo PHP_EOL . PHP_EOL . PHP_EOL;
        }
    }

    /**
     * 员工离职提醒
     */
    public function actionLeavingNotify()
    {
        $date = (new \DateTime('2018\01\01', new \DateTimeZone(TIMEZONE)))->format('Y-m-d H:i:s');
        foreach (StaffInfo::find()->where(['state' => 1, 'leave_date' => $date])->all() as $key => $obj) {
            $res = Yii::$app->mailer->compose()
                ->setFrom('<EMAIL>')
                ->setTo('<EMAIL>')
                ->setSubject('Message subject')
                ->setTextBody('Plain text content')
                ->setHtmlBody('<b>HTML content</b>')
                ->send();
        }
    }

    public function actionImpSalary($fle)
    {
        $bankRepeat = fopen(__DIR__ . '/data/bankRepeat.csv', 'w');
        $this->readCsv($fle, function ($row) use ($bankRepeat) {
            try {
                // $staff = StaffInfo::find()->where(['staff_info_id' => $row[0]])->one();
                // if (!$staff) {
                //     echo '员工 ' . $row[0] . ' 没有找到' . PHP_EOL;
                //     return;
                // }

                $salary = new HrStaffSalary();
                $salary->staff_info_id = (int) $row[0];
                $salary->base_salary = $this->number($row[1]);
                $salary->exp_allowance = $this->number($row[4]);
                $salary->position_allowance = $this->number($row[3]);
                $salary->car_rental = $this->number($row[6]);

                $salary->notebook_rental = $this->number($row[7]);
                $salary->recommended = $this->number($row[8]);
                $salary->food_allowance = $this->number($row[5]);
                $salary->base_salary_bak = $this->number($row[1]);
                $salary->dangerous_area = $this->number($row[11]);
                $row[9] = trim(strtolower($row[9]));
                if ($row[9] == 'van courier') {
                    $trip = 20000;
                } else if ($row[9] == 'bike courier') {
                    $trip = 15000;
                } else if ($row[9] == 'shop bike') {
                    $trip = 15000;
                } else {
                    $trip = 0;
                }
                $salary->trip_payment = $trip;

                if (!$salary->save()) {
                    echo $salary->staff_info_id . ' fail' . json_encode($salary->getErrors()) . PHP_EOL;
                } else {
                    echo $salary->staff_info_id . ' ok' . PHP_EOL;
                }

                // StaffItems::deleteAll(['item' => 'BANK_NO', 'staff_info_id' => $salary->staff_info_id]);

                if (!StaffItems::find()->where(['item' => 'BANK_NO', 'value' => $row[10]])->exists()) {
                    $bank = StaffItems::find()->where(['staff_info_id' => $salary->staff_info_id, 'item' => 'BANK_NO'])->one();
                    if (!$bank) {
                        $bank = new StaffItems();
                        $bank->staff_info_id = $salary->staff_info_id;
                        $bank->item = 'BANK_NO';
                    }
                    $bank->value = $row[10];
                    if (!$bank->save()) {
                        echo 'bank ' . $salary->staff_info_id . ' fail' . PHP_EOL;
                    }
                } else if (!empty($row[10])) {
                    fputcsv($bankRepeat, $row);
                }
            } catch (\Exeception $e) {
                echo $e->getMessage() . PHP_EOL;
            }
        });
        fclose($bankRepeat);
    }

    public function actionNewSalary($fle)
    {
        file_put_contents(__DIR__ . '/data/new_salary_log2.csv', '');
        $bankRepeat = fopen(__DIR__ . '/data/new_salary_log2.csv', 'w');
        $this->readCsv($fle, function ($row) use ($bankRepeat) {
            $salary = HrStaffSalary::findOne($row['staff_info_id']);
            if (!$salary) {
                $salary = new HrStaffSalary();
                $salary->staff_info_id = $row['staff_info_id'];
            }

            if (!$salary->base_salary) {
                $salary->base_salary = $this->number($row['base_salary']);
            } else if ($salary->base_salary != $this->number($row['base_salary'])) {
                $row[] = 'base_salary ' . $salary->base_salary . ' excel ' . $this->number($row['base_salary']);
                // $salary->base_salary = $this->number($row['base_salary']);
            }

            if (!$salary->position_allowance) {
                $salary->position_allowance = $this->number($row['position_allowance']);
            } else if ($salary->position_allowance != $this->number($row['position_allowance'])) {
                $row[] = 'position_allowance ' . $salary->position_allowance . ' excel ' . $this->number($row['position_allowance']);
                // $salary->position_allowance = $this->number($row['position_allowance']);
            }

            if (!$salary->exp_allowance) {
                $salary->exp_allowance = $this->number($row['exp_allowance']);
            } else if ($salary->exp_allowance != $this->number($row['exp_allowance'])) {
                $row[] = 'exp_allowance ' . $salary->exp_allowance . ' excel ' . $this->number($row['exp_allowance']);
                // $salary->exp_allowance = $this->number($row['exp_allowance']);
            }

            if (!$salary->car_rental) {
                $salary->car_rental = $this->number($row['car_rental']);
            } else if ($salary->car_rental != $this->number($row['car_rental'])) {
                $row[] = 'car_rental ' . $salary->car_rental . ' excel ' . $this->number($row['car_rental']);
                // $salary->car_rental = $this->number($row['car_rental']);
            }

            if (!$salary->food_allowance) {
                $salary->food_allowance = $this->number($row['food_allowance']);
            } else if ($salary->food_allowance != $this->number($row['food_allowance'])) {
                $row[] = 'food_allowance ' . $salary->food_allowance . ' excel ' . $this->number($row['food_allowance']);
                // $salary->food_allowance = $this->number($row['food_allowance']);
            }

            $salary->dangerous_area = 0;
            if (!$salary->dangerous_area) {
                $salary->dangerous_area = $this->number($row['dangerous_area']);
            } else if ($salary->dangerous_area != $this->number($row['dangerous_area'])) {
                $row[] = 'dangerous_area ' . $salary->dangerous_area . ' excel ' . $this->number($row['dangerous_area']);
                // $salary->dangerous_area = $this->number($row['dangerous_area']);
            }

            if (!$salary->trip_payment) {
                if (isset($row['job_title'])) {
                    $jobTitle = trim(strtolower($row['job_title']));
                    if ($jobTitle == 'van courier') {
                        $trip = 20000;
                    } else if ($jobTitle == 'bike courier') {
                        $trip = 15000;
                    } else if ($jobTitle == 'shop bike') {
                        $trip = 15000;
                    } else {
                        $trip = 0;
                    }
                    $salary->trip_payment = $trip;
                }
            }

            if ($salary->save()) {
                echo $row['staff_info_id'] . ' ok' . PHP_EOL;
            } else {
                // var_dump($salary);
            }

            if (isset($row['bank_no'])) {
                if (!StaffItems::find()->where(['item' => 'BANK_NO', 'value' => $row['bank_no']])->exists()) {
                    $bank = StaffItems::find()->where(['staff_info_id' => $salary->staff_info_id, 'item' => 'BANK_NO'])->one();
                    if (!$bank) {
                        $bank = new StaffItems();
                        $bank->staff_info_id = $salary->staff_info_id;
                        $bank->item = 'BANK_NO';
                    }
                    $bank->value = $row['bank_no'];
                    if (!$bank->save()) {
                        echo 'bank ' . $salary->staff_info_id . ' fail' . PHP_EOL;
                    }
                }
            }

            fputcsv($bankRepeat, $row);
            echo '.';
        });
        fclose($bankRepeat);
    }

    public function actionNewSalary2($fle)
    {
        file_put_contents(__DIR__ . '/data/new_salary_log3.csv', '');
        $bankRepeat = fopen(__DIR__ . '/data/new_salary_log3.csv', 'w');
        $this->readCsv($fle, function ($row) use ($bankRepeat) {
            $salary = HrStaffSalary::findOne($row['staff_info_id']);
            if (!$salary) {
                $salary = new HrStaffSalary();
                $salary->staff_info_id = $row['staff_info_id'];
            }

            // if (isset($row['base_salary']) && $row['base_salary'] > 0) {
            //     $salary->base_salary = $this->number($row['base_salary']);
            // } else if ($salary->base_salary != $this->number($row['base_salary'])) {
            //     $row[] = 'base_salary ' . $salary->base_salary . ' excel ' . $this->number($row['base_salary']);
            //     // $salary->base_salary = $this->number($row['base_salary']);
            // }

            $items = [
                // 'base_salary',
                // 'position_allowance',
                // 'exp_allowance',
                // 'car_rental',
                // 'food_allowance',
                // 'dangerous_area',
                'trip_payment',
                // 'notebook_rental',
                // 'recommended',
            ];

            foreach ($items as $field) {
                $num = 0;
                if ($row[$field] ?? false) {
                    $num = $this->number($row[$field]);
                }

                $jobTitle = StaffInfo::find()->where(['staff_info_id' => $row['staff_info_id']])->asArray()->one();
                $jobTitle = $jobTitle['job_title'] ?? -1;
                $vc = \Yii::$app->sysconfig->getIdJobTitle('van courier');
                $bc = \Yii::$app->sysconfig->getIdJobTitle('bike courier');
                $sb = \Yii::$app->sysconfig->getIdJobTitle('shop bike');

                $trip = 0;
                if ($jobTitle == $vc) {
                    $trip = 20000;
                } else if ($jobTitle == $bc) {
                    $trip = 15000;
                } else if ($jobTitle == $sb) {
                    $trip = 15000;
                }

                echo $row['staff_info_id'] . ' ' . $jobTitle . ' ' . $trip . PHP_EOL;
                if ($trip != $salary->trip_payment) {
                    $row[] = $field . $salary->{$field} . ' excel ' . $num;
                }

                $salary->trip_payment = $trip;
                // if ($num != $salary->{$field}) {
                //     $row[] = $field . $salary->{$field} . ' excel ' . $num;
                // }

                // if ($salary->base_salary == 0 && $num != 0) {
                //      echo $salary->base_salary . '  ' . $num.PHP_EOL;
                //     $salary->base_salary = $num;
                //     $salary->save();

                // }
                // if ($num < $salary->{$field}) {
                //     $salary->{$field} = $num;
                // } else if ($salary->{$field} != $num) {
                //     $salary->{$field} = $num;
                // }
            }
            if ($salary->save()) {
                echo $row['staff_info_id'] . ' ok' . PHP_EOL;
            } else {
                var_dump($salary->getErrors());
            }
            // if ($salary->staff_info_id == '16930') {
            //     var_dump($salary);die();
            //     die();
            // }

            // echo 'ok' . PHP_EOL;
            fputcsv($bankRepeat, $row);
            // if (!empty($row['position_allowance'])) {
            //     $salary->position_allowance = $this->number($row['position_allowance']);
            // } else if ($salary->position_allowance != $this->number($row['position_allowance'])) {
            //     $row[] = 'position_allowance ' . $salary->position_allowance . ' excel ' . $this->number($row['position_allowance']);
            //     // $salary->position_allowance = $this->number($row['position_allowance']);
            // }

            // if (!empty($row['exp_allowance'])) {
            //     $salary->exp_allowance = $this->number($row['exp_allowance']);
            // } else if ($salary->exp_allowance != $this->number($row['exp_allowance'])) {
            //     $row[] = 'exp_allowance ' . $salary->exp_allowance . ' excel ' . $this->number($row['exp_allowance']);
            //     // $salary->exp_allowance = $this->number($row['exp_allowance']);
            // }

            // if (!empty($row['car_rental'])) {
            //     $salary->car_rental = $this->number($row['car_rental']);
            // } else if ($salary->car_rental != $this->number($row['car_rental'])) {
            //     $row[] = 'car_rental ' . $salary->car_rental . ' excel ' . $this->number($row['car_rental']);
            //     // $salary->car_rental = $this->number($row['car_rental']);
            // }

            // if (!empty($row['food_allowance'])) {
            //     $salary->food_allowance = $this->number($row['food_allowance']);
            // } else if ($salary->food_allowance != $this->number($row['food_allowance'])) {
            //     $row[] = 'food_allowance ' . $salary->food_allowance . ' excel ' . $this->number($row['food_allowance']);
            //     // $salary->food_allowance = $this->number($row['food_allowance']);
            // }

            // if (!empty($row['dangerous_area'])) {
            //     $salary->dangerous_area = $this->number($row['dangerous_area']);
            // } else if ($salary->dangerous_area != $this->number($row['dangerous_area'])) {
            //     $row[] = 'dangerous_area ' . $salary->dangerous_area . ' excel ' . $this->number($row['dangerous_area']);
            //     // $salary->dangerous_area = $this->number($row['dangerous_area']);
            // }

            // if (!$salary->trip_payment) {
            //     if (isset($row['job_title'])) {
            //         $jobTitle = trim(strtolower($row['job_title']));
            //         if ($jobTitle == 'van courier') {
            //             $trip = 20000;
            //         } else if ($jobTitle == 'bike courier') {
            //             $trip = 15000;
            //         } else if ($jobTitle == 'shop bike') {
            //             $trip = 15000;
            //         } else {
            //             $trip = 0;
            //         }
            //         $salary->trip_payment = $trip;
            //     }
            // }

            // if ($salary->save()) {
            //     echo $row['staff_info_id'] . ' ok' . PHP_EOL;
            // } else {
            //     // var_dump($salary);
            // }

            // if (isset($row['bank_no'])) {
            //     if (!StaffItems::find()->where(['item' => 'BANK_NO', 'value' => $row['bank_no']])->exists()) {
            //         $bank = StaffItems::find()->where(['staff_info_id' => $salary->staff_info_id, 'item' => 'BANK_NO'])->one();
            //         if (!$bank) {
            //             $bank = new StaffItems();
            //             $bank->staff_info_id = $salary->staff_info_id;
            //             $bank->item = 'BANK_NO';
            //         }
            //         $bank->value = $row['bank_no'];
            //         if (!$bank->save()) {
            //             echo 'bank ' . $salary->staff_info_id . ' fail' . PHP_EOL;
            //         }
            //     }
            // }

            // fputcsv($bankRepeat, $row);
            // echo '.';
        });
        fclose($bankRepeat);
    }

    public function number($str)
    {
        if (empty($str)) {
            return 0;
        }
        $str = str_replace(',', '', $str);
        $str = str_replace('.', '', $str);
        return ((int) $str) * 100;
    }

    public function actionPosition()
    {
        file_put_contents(__DIR__ . '/data/position.csv', '');
        $csv = fopen(__DIR__ . '/data/position.csv', 'w');

        $query = StaffInfo::find()
            ->select('staff_info_id')
            ->where(['state' => 1])
            ->andWhere(['in', 'formal', [1, 4]])
            ->orderBy(['id' => SORT_ASC])
            ->all();
        $depJob = [];
        foreach (HrDepeartmentJobTitle::find()->all() as $key => $obj) {
            $depJob[] = $obj->sys_depeartment_id . '_' . $obj->job_title_id;
        }
        $i = 0;
        foreach ($query as $key => $staffId) {
            $staffId = $staffId->staff_info_id;
            $staff = Staff::view($staffId);
            // var_export($staff);die();
            $position = $staff['position_category'];

            $depJobKey = HrJobTitleRole::find()
                ->select('role_id')
                ->where([
                    'sys_depeartment_id' => $staff['sys_department_id'],
                    'job_title_id' => $staff['job_title']]
                )->asArray()
                ->column();

            $dp = $staff['sys_department_id'] . '_' . $staff['job_title'];
            $outRole = [];
            foreach ($staff['position_category'] as $key => $role) {
                if (!in_array($role, $depJobKey)) {
                    $outRole[] = $role;
                }
            }
            // 工号 姓名 部门 职位 角色 入职时间
            $result = [
                $staff['staff_info_id'],
                $staff['name'],
                Yii::$app->sysconfig->allDepeartments[$staff['sys_department_id']] ?? '-1',
                Yii::$app->sysconfig->jobTitle[$staff['job_title']] ?? $staff['job_title'],
                implode('|', array_map(function ($v) {
                    return \Yii::$app->lang->get('role_' . $v, 'zh');
                }, $staff['position_category'])),
                $staff['hire_date'],
                implode('|', array_map(function ($v) {
                    return \Yii::$app->lang->get('role_' . $v, 'zh');
                }, $depJobKey)),
                implode('|', array_map(function ($v) {
                    return \Yii::$app->lang->get('role_' . $v, 'zh');
                }, $outRole)),
                '部门-职位' . (in_array($dp, $depJob) ? '有' : '无'),
            ];
            fputcsv($csv, $result);

            $i++;

            echo $i . PHP_EOL;
        }
    }

    public function actionAddSubStaff()
    {
        // StaffInfo::updateAll(['is_sub_staff'=>0]);
        $items = StaffItems::find()->where(['item' => 'MASTER_STAFF'])->all();
        foreach ($items as $key => $item) {
            $model = StaffInfo::find()->where(['staff_info_id' => $item->staff_info_id])->one();
            if (!$model) {
                echo $item->staff_info_id . ' 没有找到' . PHP_EOL;
                $item->delete();
                continue;
            }
            if ($model->updateAttributes(['is_sub_staff' => 1])) {
                echo $item->staff_info_id . ' 子账号，更新 ok' . PHP_EOL;
            }
        }
    }

    public function actionSalaryCheck()
    {

        file_put_contents(__DIR__ . '/temp/190614_salary_check.csv', '');
        $csv = fopen(__DIR__ . '/temp/190614_salary_check.csv', 'w');
        foreach (HrStaffSalary::find()->all() as $key => $salary) {
            $jobTitle = StaffInfo::find()->where(['staff_info_id' => $salary['staff_info_id']])->asArray()->one();
            $jobTitle = $jobTitle['job_title'] ?? -1;
            $one = $two = $three = $four = $five = [];

            $one[] = \Yii::$app->sysconfig->getIdJobTitle('DC officer');
            $one[] = \Yii::$app->sysconfig->getIdJobTitle('Hub Officer');
            $one[] = \Yii::$app->sysconfig->getIdJobTitle('Mini CS');

            $two[] = \Yii::$app->sysconfig->getIdJobTitle('Warehourse officer');
            $two[] = \Yii::$app->sysconfig->getIdJobTitle('Hub staff');

            $three[] = \Yii::$app->sysconfig->getIdJobTitle('Bike courier');
            $four[] = \Yii::$app->sysconfig->getIdJobTitle('Van courier');
            $five[] = \Yii::$app->sysconfig->getIdJobTitle('Branch Supervisor');

            $row = $salary->getAttributes();
            if (in_array($jobTitle, $one)) {

                $jj = ' DC officer/Hub Officer/Mini CS';
                // if ($salary->base_salary > 990000) {
                //     $row[] = $jj . 'base_salary' . $salary->base_salary . '  ' . 990000;
                // }

                if ($salary->exp_allowance > 300000) {
                    $row[] = $jj . 'exp_allowance' . $salary->exp_allowance . '  ' . 300000;
                }

                // if ($salary->food_allowance > 110000) {
                //     $row[] = $jj . 'food_allowance' . $salary->food_allowance . '  ' . 110000;
                // }

                if ($salary->position_allowance > 0) {
                    $row[] = $jj . 'position_allowance' . $salary->position_allowance . '  ' . 0;
                }

                // if ($salary->car_rental > 0) {
                //     $row[] = $jj . 'car_rental' . $salary->car_rental . '  ' . 0;
                // }
            }

            if (in_array($jobTitle, $two)) {
                $jj = ' Warehourse officer/Hub staff';
                // if ($salary->base_salary > 990000) {
                //     $row[] = $jj . 'base_salary' . $salary->base_salary . '  ' . 990000;
                // }

                if ($salary->exp_allowance > 200000) {
                    $row[] = $jj . 'exp_allowance' . $salary->exp_allowance . '  ' . 200000;
                }

                // if ($salary->food_allowance > 110000) {
                //     $row[] = $jj . 'food_allowance' . $salary->food_allowance . '  ' . 110000;
                // }

                if ($salary->position_allowance > 0) {
                    $row[] = $jj . 'position_allowance' . $salary->position_allowance . '  ' . 0;
                }

                // if ($salary->car_rental > 0) {
                //     $row[] = $jj . 'car_rental' . $salary->car_rental . '  ' . 0;
                // }
            }

            if (in_array($jobTitle, $three)) {
                // if ($salary->base_salary > 990000) {
                //     $row[] = 'Bike courier base_salary' . $salary->base_salary . '  ' . 990000;
                // }

                if ($salary->exp_allowance > 0) {
                    $row[] = 'Bike courier exp_allowance' . $salary->exp_allowance . '  ' . 0;
                }

                if ($salary->food_allowance > 4500) {
                    $row[] = 'Bike courier food_allowance' . $salary->food_allowance . '  ' . 4500;
                }

                if ($salary->position_allowance > 0) {
                    $row[] = 'Bike courier position_allowance' . $salary->position_allowance . '  ' . 0;
                }

                if ($salary->car_rental > 13500) {
                    $row[] = 'Bike courier car_rental' . $salary->car_rental . '  ' . 13500;
                }

                if ($salary->dangerous_area > 12000) {
                    $row[] = 'Bike courier dangerous_area' . $salary->dangerous_area . '  ' . 12000;
                }

                if ($salary->trip_payment > 15000) {
                    $row[] = 'Bike courier trip_payment' . $salary->trip_payment . '  ' . 15000;
                }
            }

            if (in_array($jobTitle, $four)) {
                // if ($salary->base_salary > 990000) {
                //     $row[] = 'Van courier base_salary' . $salary->base_salary . '  ' . 990000;
                // }

                if ($salary->exp_allowance > 0) {
                    $row[] = 'Van courier exp_allowance' . $salary->exp_allowance . '  ' . 0;
                }

                if ($salary->food_allowance > 4500) {
                    $row[] = 'Van courier food_allowance' . $salary->food_allowance . '  ' . 4500;
                }

                if ($salary->position_allowance > 0) {
                    $row[] = 'Van courier position_allowance' . $salary->position_allowance . '  ' . 0;
                }

                if ($salary->car_rental > 46500) {
                    $row[] = 'Van courier car_rental' . $salary->car_rental . '  ' . 46500;
                }

                if ($salary->dangerous_area > 12000) {
                    $row[] = 'Van courier dangerous_area' . $salary->dangerous_area . '  ' . 12000;
                }

                if ($salary->trip_payment > 20000) {
                    $row[] = 'Van courier trip_payment' . $salary->trip_payment . '  ' . 20000;
                }
            }

            if (in_array($jobTitle, $five)) {
                // if ($salary->base_salary > 2500000) {
                //     $row[] = 'Branch Supervisor base_salary' . $salary->base_salary . '  ' . 2500000;
                // }

                if ($salary->exp_allowance > 0) {
                    $row[] = 'Branch Supervisor exp_allowance' . $salary->exp_allowance . '  ' . 0;
                }

                // if ($salary->food_allowance > 110000) {
                //     $row[] = 'Branch Supervisor food_allowance' . $salary->food_allowance . '  ' . 110000;
                // }

                if ($salary->position_allowance > 500000) {
                    $row[] = 'Branch Supervisor position_allowance' . $salary->position_allowance . '  ' . 0;
                }

                // if ($salary->car_rental > 0) {
                //     $row[] = 'Branch Supervisor car_rental' . $salary->car_rental . '  ' . 0;
                // }
            }

            echo '.' . PHP_EOL;
            fputcsv($csv, $row);
        }
        fclose($csv);
    }

    public function actionTransforDepartment()
    {
        $storeIds = [
            'TH02030204', // Yii::$app->sysconfig->getStoreIdByName('LAS_HUB-ลาซาล'),
            'TH01470301', //Yii::$app->sysconfig->getStoreIdByName('BKK_DC-จตุโชติ'),
            'TH27011602', //Yii::$app->sysconfig->getStoreIdByName('NE1_HUB-นครราชสีมา'),
            'TH56010102', //Yii::$app->sysconfig->getStoreIdByName('NO1_HUB-นครสวรรค์'),
            'TH68010201', //Yii::$app->sysconfig->getStoreIdByName('SO1_HUB-สุราษฏร์ธานี'),
        ];

        // $storeIds = [
        //     Yii::$app->sysconfig->getStoreIdByName('branch_001'),
        //     Yii::$app->sysconfig->getStoreIdByName('DC111'),
        //     Yii::$app->sysconfig->getStoreIdByName('SAS_DC'),
        //     Yii::$app->sysconfig->getStoreIdByName('dadga'),
        // ];

        foreach (BaseStaffInfo::find()->where(['sys_store_id' => $storeIds])->each(1000) as $staff) {
            $positions = HrStaffInfoPosition::find()->select('position_category')->where(['staff_info_id' => $staff->staff_info_id])->column();
            $jobTitle = $staff->job_title;
            if (!HrDepeartmentJobTitle::find()->where(['sys_depeartment_id' => 25, 'job_title_id' => $jobTitle])->exists()) {
                $newRelation = new HrDepeartmentJobTitle();
                $newRelation->sys_depeartment_id = 25;
                $newRelation->job_title_id = $jobTitle;
                if ($newRelation->save()) {
                    $this->echo('25 ', $jobTitle, 'ok');
                } else {
                    $this->echo('25 ', $jobTitle, 'fail');
                    var_dump($newRelation->getErrors());
                    continue;
                }
            }

            foreach ($positions as $pos) {
                if (!HrJobTitleRole::find()->where(['sys_depeartment_id' => 25, 'job_title_id' => $jobTitle, 'role_id' => $pos])->exists()) {
                    $newRelation = new HrJobTitleRole();
                    $newRelation->sys_depeartment_id = 25;
                    $newRelation->job_title_id = $jobTitle;
                    $newRelation->role_id = $pos;
                    if ($newRelation->save()) {
                        $this->echo('25 ', $jobTitle, $pos, 'ok');
                    } else {
                        $this->echo('25 ', $jobTitle, $pos, 'fail');
                        var_dump($newRelation->getErrors());
                        continue;
                    }
                }
            }

            $trans = HrDepeartmentJobTitle::getDb()->beginTransaction();
            $staff->sys_department_id = '25';

            if ($staff->updateAttributes(['sys_department_id' => 25]) !== false) {
                $this->echo($staff->staff_info_id, 'ok');
            } else {
                $trans->rollback();
                $this->echo($staff->staff_info_id, 'fail');
                var_dump($staff->getErrors());
                continue;
            }
            $info = Staff::view($staff->staff_info_id);
            if (!StaffSyncService::getInstance()->saveStaffInfo($info, 1)) {
                $trans->rollback();
                $this->echo($staff->staff_info_id, 'send msg fail');
                continue;
            }
            $trans->commit();
            $this->echo(str_repeat('=', 10));
        }
    }

    public function actionRepair()
    {
        foreach (StaffInfo::find()->each(100) as $obj) {
            $lid = HrOperateLogs::find()->select('id')->where(['staff_info_id' => $obj->staff_info_id])->min('id');
            if (empty($lid)) {
                continue;
            }

            $logs = HrOperateLogs::findOne($lid);

            if (empty($logs->operater)) {
                continue;
            }

            if ($obj->updateAttributes(['creater' => $logs->operater]) !== false) {
                $this->echo($obj->staff_info_id, $logs->operater, 'ok');
            } else {
                var_dump($obj->getErrors());
            }
            unset($logs);
        }
    }

    public function actionLeaveState($isUpd = false)
    {
        $headSkip = true;
        $num = 0;
        $this->readCsv('data/190530_leave_staff', function ($row) use (&$headSkip, $isUpd, &$num) {
            if ($headSkip) {
                $headSkip = false;
                return true;
            }

            $num++;
            // $this->echo('.', $num);
            $staffInfoId = $row['staff_info_id'];
            $attr = [
                'state' => $row['State'] == 'Dimission' ? 2 : 3,
                'leave_date' => $row['leave_date'] ? date_create_from_format("d/m/Y", $row['leave_date'])->format('Y-m-d') : null,
                'stop_duties_date' => $row['stop_duties_date'] ? date_create_from_format("d/m/Y", $row['stop_duties_date'])->format('Y-m-d') : null,
                'payment_state' => $row['payment_state'] == 'Block the payment' ? 2 : 1,
                'payment_markup' => 7,
            ];

            $staff = StaffInfo::find()->where(['staff_info_id' => $staffInfoId])->one();
            if (!$staff) {
                $this->echo('staff', $staffInfoId, 'not found');
                return;
            }

            if ($isUpd && $staff->updateAttributes($attr) === false) {
                $this->echo('staff', $staffInfoId, 'update error', $staff->getErrors());
                return;
            }

            $msg = Staff::view($staffInfoId);
            if ($isUpd && !StaffSyncService::getInstance()->saveStaffInfo($msg, 1)) {
                $this->echo('staff', $staffInfoId, 'send msg error');
                return;
            }

            $this->echo($staffInfoId, 'ok');
        });
    }

    public function actionImpBank($isUpd = false)
    {
        $headSkip = true;
        $num = 0;
        $rows = [];
        $this->readCsv('data/190612_bank_02', function ($row) use (&$headSkip, $isUpd, &$num, &$rows) {
            if ($headSkip) {
                $headSkip = false;
                return true;
            }
            $canUpd = false;
            $staff = BaseStaffInfo::find()->where(['staff_info_id' => $row['staff_info_id']])->one();
            if (!$staff) {
                $row[] = 'not found';
            } else if (!$staff->bank_no && $row['bank_no']) {
                $staff->bank_no = $row['bank_no'];
                $row[] = 'upd';
                $canUpd = true;
            } else if ($row['bank_no'] && ($staff->bank_no != $row['bank_no'])) {
                $row[] = 'need upd db:' . $staff->bank_no . ' excel:' . $row['bank_no'];
            } else if ($staff->bank_no == $row['bank_no'] || !$row['bank_no']) {
                $row[] = 'not upd';
            }

            if ($isUpd && $canUpd) {
                $op = new HrOperateLogs();
                $before = Staff::view($row['staff_info_id']);
                if ($staff->update(true, ['bank_no']) === false) {
                    $row[] = 'error ' . json_encode($staff->getErrors());
                } else {
                    $after = Staff::view($row['staff_info_id']);
                    $op->operater = 999;
                    $op->type = 'staff';
                    $op->staff_info_id = $row['staff_info_id'];
                    $op->before = json_encode(['body' => $before]);
                    $op->after = json_encode(['body' => $after]);
                    $op->save();
                }
            }

            $rows[] = $row;
            $num++;
            $this->echo('ok');
            unset($op, $before, $after, $staff);
        });
        $this->genCsv('190612_bank', $rows);
    }

    public function actionStaffImpBank($isUpd = false) {
        $headSkip = true;
        $this->readCsv('data/bank_no_190625', function ($row) use (&$headSkip, $isUpd) {
            if ($headSkip) {
                $headSkip = false;
                return true;
            }

            $canUpd = false;
            $staff = BaseStaffInfo::find()->where(['staff_info_id' => $row['staff_info_id']])->one();
            if (!$staff) {
                //未找到用户
                $this->echo($row['staff_info_id'].'   not found');
            } else if (!$staff->bank_no && $row['bank_no']) {
                //用户银行卡号为空，更新数据
                $staff->bank_no = $row['bank_no'];
                $canUpd = true;
                $this->echo($row['staff_info_id']."   update");
            } else if ($row['bank_no'] && ($staff->bank_no != $row['bank_no'])) {
                //员工已有银行卡号，和导入的银行卡号不一致
                $this->echo($row['staff_info_id'].'---need upd db:' . $staff->bank_no . ' excel:' . $row['bank_no']);
            } else if ($staff->bank_no == $row['bank_no'] || !$row['bank_no']) {
                //员工银行卡号和导入的银行卡号一致
                $this->echo($row['staff_info_id'].'   not upd');
            }

            if ($isUpd && $canUpd) {
                $op = new HrOperateLogs();
                $before = Staff::view($row['staff_info_id']);
                if ($staff->update(true, ['bank_no']) === false) {
                    $this->echo($row['staff_info_id'].'---error'.json_encode($staff->getErrors()));
                } else {
                    $after = Staff::view($row['staff_info_id']);
                    $op->operater = 999;
                    $op->type = 'staff';
                    $op->staff_info_id = $row['staff_info_id'];
                    $op->before = json_encode(['body' => $before]);
                    $op->after = json_encode(['body' => $after]);
                    $op->save();
                }
            }
            unset($op, $before, $after, $staff);
        });
    }

    public function actionJobtitle($isUpd = false)
    {
        $headSkip = true;
        $num = 0;
        $rows = [];
        $this->readCsv('data/190617_job_title', function ($row) use (&$headSkip, $isUpd, &$num, &$rows) {
            if ($headSkip) {
                $headSkip = false;
                return true;
            }

            $base = BaseStaffInfo::find()->where(['staff_info_id' => $row['staff_info_id']])->one();
            if (!$base) {
                $this->echo($row['staff_info_id'], 'not found');
                return;
            }
            $base->job_title = $row['job_title'];

            $before = Staff::view($row['staff_info_id']);
            if ($isUpd && !$base->update(true, ['job_title'])) {
                var_export($base->getErrors());
                $this->echo($row['staff_info_id'], 'ok');
                return;
            }
            $after = Staff::view($row['staff_info_id']);
            if ($isUpd && !StaffSyncService::getInstance()->saveStaffInfo($after, 1)) {
                $this->echo('staff', $row['staff_info_id'], 'send msg error');
                return;
            }
            $op = new HrOperateLogs();
            $op->operater = 999;
            $op->type = 'staff';
            $op->staff_info_id = $row['staff_info_id'];
            $op->before = json_encode(['body' => $before]);
            $op->after = json_encode(['body' => $after]);
            $op->save();

            $this->echo($row['staff_info_id'], 'ok');
        });
        $this->genCsv('190612_bank', $rows);
    }

    public function actionUpdateStaffNodeDepartment() {
        $this->echo('部门Network Planning 子部门id = 30 开始');
        //部门Network Planning 子部门id = 30
        $staff_list_a = BaseStaffInfo::find()
                            ->where(['sys_department_id' => 4])
                            ->andWhere(['staff_info_id' => [19953,29798,30889,19761,31291,20251,24485,24918,25509,27012,28162,28536,28560,17672,19813,19940,20469,21613]])
                            ->andWhere(['formal' => [1,4]])
                            ->andWhere(['state' => [1,3]])
                            ->asArray()->all();
        foreach ($staff_list_a as $key_a => $value_a) {
            $attr['node_department_id'] = 30;
            Staff::updateItems($value_a['staff_info_id'],$attr,'-1');
            $this->echo($value_a['staff_info_id'].'-----ok');

        }
        $this->echo('部门Network Planning 子部门id = 30 结束');

        $this->echo('Network suppurt.xlsx 子部门id = 33 开始');
        //表二：Network suppurt.xlsx 子部门id = 33
        $staff_list_b = BaseStaffInfo::find()
                            ->where(['sys_department_id' => 4])
                            ->andWhere(['staff_info_id' => [17109,19616,33486,28534,24902,24688,24903,19060,21426,33763,34607,24901,34007,33765,34531,20836]])
                            ->andWhere(['formal' => [1,4]])
                            ->andWhere(['state' => [1,3]])
                            ->asArray()->all();
        foreach ($staff_list_b as $key_b => $value_b) {
            $attr['node_department_id'] = 33;
            Staff::updateItems($value_b['staff_info_id'],$attr,'-1');
            $this->echo($value_b['staff_info_id'].'-----ok');
        }
        $this->echo('Network suppurt.xlsx 子部门id = 33 结束');

        $this->echo('Network Training& Standardization.xlsx 子部门id = 31 开始');
        //表三：Network Training& Standardization.xlsx 子部门id = 31
        $staff_list_c = BaseStaffInfo::find()
                            ->where(['sys_department_id' => 4])
                            ->andWhere(['staff_info_id' => [33064,33134,33796,33134,33064,33796]])
                            ->andWhere(['formal' => [1,4]])
                            ->andWhere(['state' => [1,3]])
                            ->asArray()->all();
        foreach ($staff_list_c as $key_c => $value_c) {
            $attr['node_department_id'] = 31;
            Staff::updateItems($value_c['staff_info_id'],$attr,'-1');
            $this->echo($value_c['staff_info_id'].'-----ok');
        }
        $this->echo('Network Training& Standardization.xlsx 子部门id = 31 结束');

        $this->echo('Network operation 子部门id = 32 开始');
        //Network operation 子部门id = 32
        $staff_list_d = BaseStaffInfo::find()
                            ->where(['sys_department_id' => 4])
                            ->andWhere(['not in','staff_info_id',[19953,29798,30889,19761,31291,20251,24485,24918,25509,27012,28162,28536,28560,17672,19813,19940,20469,21613,17109,19616,33486,28534,24902,24688,24903,19060,21426,33763,34607,24901,34007,33765,34531,20836,33064,33134,33796,33134,33064,33796]])
                            ->andWhere(['formal' => [1,4]])
                            ->andWhere(['state' => [1,3]])
                            ->asArray()->all();
        foreach ($staff_list_d as $key_d => $value_d) {
            $attr['node_department_id'] = 32;
            Staff::updateItems($value_d['staff_info_id'],$attr,'-1');
            $this->echo($value_d['staff_info_id'].'-----ok');
        }
        $this->echo('Network operation 子部门id = 32 结束');
    }

    //修改员工工资发放状态
    public function actionStaffUpdPaymentstate($isUpd = false) {
        $headSkip = true;
        $this->readCsv('data/payment_state190625', function ($row) use (&$headSkip, $isUpd) {
            if ($headSkip) {
                $headSkip = false;
                return true;
            }

            $canUpd = false;
            $staff = BaseStaffInfo::find()->where(['staff_info_id' => $row['staff_info_id']])->one();
            if (!$staff) {
                //未找到用户
                $this->echo($row['staff_info_id'].'   not found');
            } else if ($row['payment_state'] == 1 && $staff->payment_state != 2) {
                //payment_state 修改成2
                $canUpd = true;
                $staff->payment_state = 2;
                $staff->payment_markup = 2;
                $this->echo($row['staff_info_id']."   update 2");
            } else if ($row['payment_state'] == 2 && $staff->payment_state != 1) {
                //payment_state 修改成2
                $canUpd = true;
                $staff->payment_state = 1;
                $staff->payment_markup = 102;
                $this->echo($row['staff_info_id']."   update 1");
            }

            if ($isUpd && $canUpd) {
                $op = new HrOperateLogs();
                $before = Staff::view($row['staff_info_id']);
                if ($staff->update(true, ['payment_state','payment_markup']) === false) {
                    $this->echo($row['staff_info_id'].'---error'.json_encode($staff->getErrors()));
                } else {
                    $after = Staff::view($row['staff_info_id']);
                    $op->operater = 999;
                    $op->type = 'staff';
                    $op->staff_info_id = $row['staff_info_id'];
                    $op->before = json_encode(['body' => $before]);
                    $op->after = json_encode(['body' => $after]);
                    $op->save();
                }
            }

        });
    }

    //查找6.1-6.30 员工变换过网点的员工记录
    public function actionStaffStore() {

        $result = HrOperateLogs::find()->Where(['BETWEEN', 'created_at', '2019-06-01 00:00:00', '2019-06-30 23:59:59'])->asArray()->all();

        if(count($result) > 0) {
            foreach ($result as $key => $obj) {
                $before = json_decode($obj['before'], true)['body'] ?? [];
                $after = json_decode($obj['after'], true)['body'] ?? [];


                if (count($before) == 0 || count($after) == 0) {
                    continue;
                }
                if(isset($before['sys_store_id']) && isset($after['sys_store_id'])) {
                    if($before['sys_store_id'] != $after['sys_store_id']) {
                         $this->echo('日期：'.$obj['created_at'].'-----员工工号：'.$obj['staff_info_id'].'-----网点变更：'.$before['sys_store_id'].'->'.$after['sys_store_id']);
                    }
                }
            }

        } else {
            $this->echo("empty");
        }
    }

    //关闭外协员工账号，并把工资结算类型设置为日结
    public function actionStaffFormal($isUpd = false) {
        $headSkip = true;
        $this->readCsv('data/190705_hr_staff_info_f', function ($row) use (&$headSkip, $isUpd) {
            if ($headSkip) {
                $headSkip = false;
                return true;
            }

            $canUpd = false;
            $staff = Staff::view($row['staff_info_id']);

            if (count($staff) == 0) {
                $this->echo($row['staff_info_id'].'---not found');//未找到用户

            } else if (in_array($staff['state'], [2,3])) {
                $this->echo($row['staff_info_id']."---state=2");

            } else if ($staff['state'] == 1) {
                $canUpd = true;
                $staff['state'] = 2;
                $staff['pay_type'] = 'BY_DAY';
                $this->echo($row['staff_info_id']."---update state 2");
            }

            if ($isUpd && $canUpd) {
                $op = new HrOperateLogs();
                $before = Staff::view($row['staff_info_id']);

                $staff_model = Staff::combination($staff);
                if (Staff::save($staff_model,'10000') === false) {
                    $this->echo($row['staff_info_id'].'---error'.json_encode($staff->getErrors()));
                } else {
                    $after = Staff::view($row['staff_info_id']);
                    $op->operater = 999;
                    $op->type = 'staff';
                    $op->staff_info_id = $row['staff_info_id'];
                    $op->before = json_encode(['body' => $before]);
                    $op->after = json_encode(['body' => $after]);
                    $op->save();
                }
            }
        });
    }


    //关闭外协员工账号
    public function actionOutSourcingStaffLeave($isUpd = false)
    {
        $staff_info_ids = [
            90310,90311,90313,90315,90316,91678,91679,91781,91788,91953,91959,91960,92268,125130,133341,133342,198638,198639,198640,198641,198642,198643,198644,198645,198646,198647,198648,198649,
            198650,198651,198652,198654,198655,198656,198657,198658,198659,198660,198661,198662,198663,198664,198665,198666,198667,198668,198670,198671,198672,198673,198674,198675,198676,198677,
            198678,198679,198680,198681,198682,198683,198684,198687,198688,198689,198690,198691,198692,198693,198694,198695,198696,198697,198698,198699,198700,198701,198739,198740,198741,198742,
            198743,198744,198745,198746,198747,198748,198749,198750,198751,198752,198753,198754,198755,198756,198757,198758,198759,198760,198761,198762,198763,198764,198765,198766,198767,198768,
            198769,198770,198771,198772,198773,198774,198775,198776,198777,198778,239925,256720,256721,256722,256723,256724,256725,256776,256777,256778,343352,343353,364262,377046,377415,377444,
            377605,377913,377914,378626,378627,378628,378629,378630,378631,380443,381189,381190,382263,382276,382422,382606,393008,393018,393458,393469,393470,393471,393880,394467,397872,398048,
            398090,399422,399443,399654,399656,399659,399660,399851,401818,402032,402336,402482,402787,403088,403415,403686,403687,403689,403693,403825,408419,408421,408803,409211,409336,409338,
            414733,414734,417624,423206,423207,423208,423209,423210,437886,439460,439461,439462,439529,440095,440096,443551,443552,443554,443555,443633,443835,445862,447127,451066,451073,451314,
            451316,451425,451593,451596,451609,451610,451611,453403,453404,453406,453408,453409,453410,453413,453511,453969,454022,454023,454024,454025,454026,454177,454178,454179,454180,454181,
            454247,454263,454334,454335,454336,454337,454562,454563,454564,454565,454792,456281,456432,456538,458398,458399,458450,458451,458452,458453,458454,458517,458518,458519,458520,458521,
            458524,458680,458728,458940,458941,458942,459154,459160,459161,460088,460488,461403,461890,462745,462921,462995,463708,463709,463710,464060,464482,465402,465628,465640,465641,465642,
            465856,465858,466032,466033,466054,466608,466611,466748,466905,466962,466969,467250,471725,472995,472996,472997,472998,473008,473037,473038,473086,473153,473154,479332,479333,479355,
            479447,479506,479508,479509,479800,479801,480040,480043,480044,483282,483469,483470,483471,483614,483615,483618,483619,484383,484694,484695,484699,484786,484814,484880,484881,484882,
            484883,485002,485003,485006,485008,485010,485012,487342,488971,496116,498467,498551,499084,499162,499163,499455,499803,503550,523975,524079,524281,524282,524340,524583,524584,532029,
            532032,3001831,3002062,3003624,3003625,3003792,3003955,3003956,3004030,3007570,3007571,3007778,3007779,3007780,3007878,3007880,3008230,3008231,3013832,3015708,3015709,3015710,3015742,
            3015876,3016021,3016188,3016435,3032229,3032230,3032247,3032256,3032298,3032608,3032609,3034601,3034602,3034626,3034818,3035136,3035240,3035241,3035243,3035244,3035245,3035246,3035247,
            3035248,3035249,3035250,3035251,3035303,3037431,3041713,3043622,3043623,3043624,3043625,3043626,3043627,3043628,3043629,3044165,3044168,3045169,3045170,3045171,3045401,3045464,3045509,
            3045536,3047557,3049242,3049243,3049244,3049245,3055584,3055585,3055586,3055623,3055645,3055652,3055765,3055853,3055854,3057317,3057732,3057733,3057734,3058047,3058069,3058109,3058111,
            3058296,3058297,3058307,3059920,3059999,3060020,3060051,3060052,3060163,3060168,3060300,3062125,3062270,3062271,3062272,3062431,3062432,3062433,3062434,3064501,3064651,3064652,3064654,
            3067903,3068007,3068029,3068123,3068163,3068369,3068370,3070659,3070660,3070661,3070662,3070914,3070932,3070936,3072694,3072829,3072833,3072838,3072839,3072893,3072894,3072917,3072918,
            3072973,3072977,3072978,3072979,3073203,3073216,3073426,3073501,3073602,3073666,3073667,3073668,3073669,3073791,3073792,3073834,3073903,3073904,3073905,3073906,3073907,3073908,3073990,
            3073991,3074059,3074060,3074068,3074069,3074072,3074076,3074101,3074102,3074103,3074104,3074255,3074268,3074269,3074270,3074282,3074480,3074484,3074491,3074499,3074501,3074503,3074613,
            3074635,3074636,3074643,3074644,3074826,3074828,3074832,3074847,3074856,3075056,3075057,3075058,3076988,3076989,3077114,3077186,3077187,3077360,3077362,3077789,3077793,3077794,3077981,
            3077982,3077983,3077988,3079864,3079865,3079938,3079940,3080059,3080224,3080243,3080244,3081778,3081780,3081781,3081789,3081855,3081948,3081949,3081950,3081962,3082067,3082231,3082232,
            3082233,3082235,3082297,3082322,3082546,3082547,3085348,3085439,3085881,3085893,3085924,3085925,3085926,3085927,3085928,3086039,3086040,3086204,3086206,3087632,3087834,3087835,3087837,
            3087838,3087856,3089447,3089449,3089472,3089738,3089830,3089833,3089885,3090017,3090130,3090151,3090169,3090170,3090172,3092196,3092210,3092352,3092355,3092367,3092546,3092549,3092550,
            3092572,3092694,3092711,3092896,3092897,3092991,3093008,3093083,3093084,3093089,3093092,3093093,3093098,3093106,3093182,3093374,3093395,3093571,3093573,3093578,3093582,3093583,3093586,
            3093587,3093588,3093589,3093712,3093713,3096305,3096306,3096310,3096311,3096319,3096320,3096321,3096325,3096428,3096432,3096535,3096536,3096537,3096544,3096545,3096557,3096729,3096730,
            3096733,3096734,3096738,3096872,3096951,3096956,3097120,3097121,3097122,3097123,3097124,3098906,3099213,3099214,3099271,3099272,3100888,3100890,3100891,3100892,3100895,3100903,3101109,
            3102893,3102900,3102902,3102904,3102906,3104674,3104932,3105084,3105090,3105091,3105092,3105093,3108059,3108091,3108093,3108095,3108096,3108193,3108248,3108249,3108250,3108519,3108710,
            3108711,3108718,3108845,3108861,3109002,3109003,3109004,3109008,3109014,3109015,3109017,3109019,3109021,3109034,3109035,3109036,3109038,3109351,3114064,3114103,3114206,3114207,3116337,
            3116338,3116339,3116340,3116341,3118333,3118364,3118365,3118368,3118371,3120544,3120551,3120575,3120576,3121454,3121456,3121493,3121604,3121674,3121947,3121948,3121949,3121974,3121975,
            3121976,3121977,3122357,3122358,3122359,3122361,3122385,3122386,3122391,3122393,3122394,3122395,3122396,3122399,3122536,3122538,3122772,3122784,3122785,3122837,3122838,3122872,3122873,
            3123096,3123101,3123102,3123103,3123107,3123110,3123111,3123555,3123557,3123558,3123568,3123730,3123898,3125914,3126189,3128980,3129009,3129010,3129018,3129019,3129020,3129021,3129028,
            3129029,3129179,3129180,3129181,3129240,3129264,3129265,3129475,3129497,3129498,3129499,3129500,3129504,3129505,3129507,3129513,3129925,3129927,3129944,3129945,3129951,3130566,3130567,
            3130568,3130569,3130571,3130590,3130774,3130802,3130803,3130804,3130823,3130824,3130825,3130826,3131011,3131036,3131039,3131041,3131057,3131058,3131225,3131241,3131263,3131264,3131548,
            3131707,3131709,3131725,3131727,3131728,3131729,3131888,3131889,3131890,3131891,3131892,3131913,3131919,3132335,3132341,3132342,3132351,3132364,3132375,3132746,3133163,3133165,3133167,
            3133168,3133171,3133173,3133174,3133188,3133189,3133192,3133199,3136144,3136145,3136148,3136161,3136162,3136166,3136172,3136173,3136174,3138711,3138731,3138732,3138741,3139233,3139245,
            3139246,3139250,3139252,3139253,3139256,3139257,3139259,3139260,3139295,3139312,3139313,3139314,3139315,3139328,3139855,3139856,3139858,3139861,3139862,3139863,3140203,3140204,3140205,
            3140522,3140524,3140707,3140708,3140709,3140731,3140754,3140759,3140773,3140774,3141039,3141042,3141043,3141053,3141054,3141056,3141065,3141068,3141069,3141072,3141076,3141077,3141078,
            3141079,3141412,3141413,3141414,3141415,3141416,3141704,3141708,3141710,3141718,3141721,3141797,3141800,3142404,3142407,3142411,3142412,3142423,3142491,3142505,3142511,3142512,3142517,
            3142762,3142778,3142779,3142780,3142781,3142810,3142811,3142825,3142830,3142831,3142841,3142842,3142844,3142845,3142853,3142865,3142866,3142868,3142869,3142870,3143106,3143110,3143123,
            3143134,3143135,3143137,3143139,3144768,3144770,3144782,3145728,3145730,3145732,3145734,3145737,3145740,3145743,3145744,3145751,3145752,3145754,3145755,3145756,3145769,3145770,3145776,
            3145789,3145791,3145797,3145809,3145968,3145970,3145973,3145975,3146005,3146743,3146744,3146748,3147977,3147987,3147988,3147989,3147990,3148005,3148182,3148183,3148216,3148236,3148237,
            3148239,3148394,3148426,3148427,3148428,3148430,3148431,3148432,3148433,3148447,3148450,3150948,3151262,3151264,3151265,3151274,3151275,3151276,3151277,3151279,3151280,3151281,3151282,
            3151283,3151389,3151424,3151425,3151434,3151441,3151451,3151452,3151453,3151454,3151456,3151479,3153667,3153670,3153773,3153775,3153776,3153779,3153782,3153797,3153799,3154080,3154117,
            3157689,3157690,3157691,3157692,3157695,3157696,3157698,3157699,3157708,3157728,3157734,3157745,3157750,3157751,3157797,3157846,3158421,3158422,3158423,3158427,3158567,3158568,3158708,
            3158712,3158714,3158723,3158725,3158732,3158737,3158739,3161020,3161021,3161023,3161024,3161025,3161026,3161123,3161124,3161240,3161271,3163764,3163767,3163773,3163913,3163914,3163958,
            3164120,3164122,3164124,3164125,3164126,3164127,3164152,3164158,3164159,3164160,3164328,3164330,3164351,3164352,3164353,3164480,3164481,3164498,3164512,3164519,3164520,3164521,3164526,
            3164537,3164763,3164765,3164784,3164785,3164788,3164791,3164792,3164805,3164919,3164924,3164925,3164926,3164927,3164928,3164929,3164933,3164943,3165030,3165047,3165067,3165075,3165078,
            3165079,3165086,3165132,3165191,3170553,3170554,3170557,3170558,3170560,3170702,3170705,3170707,3170711,3170815,3170823,3170824,3170825,3170828,3171050,3173180,3173359,3173360,3173375,
            3173536,3173539,3173540,3173927,3173938,3173939,3173945,3173946,3173957,3173958,3177208,3177209,3177340,3177341,3177343,3177345,3177348,3177363,3177522,3177532,3177533,3177534,3177542,
            3177546,3177588,3177589,3177591,3177592,3177593,3177595,3177599,3178005,3178019,3178020,3178021,3178023,3178029,3178030,3178032,3178038,3178039,3178046,3178047,3178301,3178394,3178435,
            3178437,3178438,3178444,3178456,3178457,3178466,3178467,3178468,3178613,3178634,3178639,3178640,3178643,3178644,3178797,3178841,3178851,3178852,3178957,3178960,3178961,3178962,3178963,
            3178964,3178966,3178977,3178978,3178979,3178986,3178998,3179006,3179007,3179077,3179167,3179185,3179191,3179192,3179194,3179195,3179197,3179201,3179204,3179266,3179268,3179269,3179270,
            3179452,3179453,3179759,3179768,3179910,3180080,3180085,3180090,3180091,3180127,3180142,3182322,3182533,3182534,3182536,3182537,3182539,3182560,3182565,3182566,3182570,3182573,3182574,
            3182580,3182583,3182585,3182586,3182590,3182592,3182593,3182723,3182827,3182828,3182829,3182948,3182950,3182965,3182967,3182970,3182974,3182975,3182976,3182981,3182986,3182987,3182989,
            3182990,3182994,3183192,3183292,3183301,3183340,3185521,3185522,3185523,3185524,3185525,3185531,3185532,3185534,3185536,3185538,3185549,3185551,3185734,3185929,3185994,3186236,3186241,
            3186242,3186247,3186291,3186292,3186293,3186294,3186480,3186494,3186495,3186713,3186724,3186730,3186879,3186883,3186897,3186899,3186900,3187187,3187811,3187989,3188005,3188011,3188013,
            3188030,3188158,3188159,3188166,3188169,3188192,3188195,3188199,3188200,3188201,3188202,3188203,3188204,3188388,3188427,3188433,3188437,3188439,3188442,3188444,3188445,3188446,3188448,
            3188449,3188450,3188451,3188458,3188459,3188460,3188467,3188468,3190267,3190275,3190277,3190287,3190289,3190447,3190467,3190468,3190471,3190602,3190610,3190957,3191053,3191071,3191099,
            3191100,3191201,3191202,3191206,3191215,3191465,3191471,3191475,3191476,3191481,3191510,3191511,3191531,3191624,3191637,3191638,3191650,3191681,3191732,3191788,3191792,3194413,3194421,
            3194810,3194851,3194854,3194855,3194857,3194925,3196431,3196445,3196497,3197252,3197679,3197685,3197696,3197716,3197827,3197831,3197833,3197835,3197836,3197883,3197885,3197890,3197892,
            3197899,3197900,3197901,3197932,3197933,3197934,3197941,3198063,3198069,3198070,3198317,3198318,3198319,3198320,3198321,3198357,3198359,3198362,3198364,3198365,3198378,3198400,3198404,
            3198588,3198590,3198630,3198654,3198657,3198664,3198811,3198842,3198843,3198848,3198871,3198872,3198891,3199492,3200127,3200128,3200147,3200148,3200151,3200577,3200614,3200615,3200616,
            3200617,3200618,3200619,3200620,3200621,3200623,3200812,3200814,3200815,3200876,3201339,3202565,3202566,3202568,3202673,3202674,3202809,3202811,3202832,3202843,3202876,3202878,3202879,
            3202880,3202881,3202883,3202889,3202891,3202892,3203002,3203033,3203035,3203060,3203061,3203064,3203065,3203116,3203193,3203195,3203196,3203197,3203208,3203209,3203210,3203211,3203219,
            3203221,3203249,3203250,3203256,3203257,3203258,3203259,3203455,3203616,3203629,3203646,3203647,3203782,3203843,3203844,3203846,3203848,3203865,3203878,3205073,3205076,3205078,3205083,
            3205085,3205239,3205247,3205249,3205250,3205259,3205267,3205275,3205281,3205336,3205339,3205445,3205497,3205504,3205505,3205957,3206190,3206260,3206277,3206389,3206394,3206400,3206498,
            3206501,3206564,3206632,3206633,3206634,3206638,3206740,3206748,3206750,3206751,3206759,3206779,3206919,3206929,3206932,3206933,3209670,3209674,3209684,3209705,3209707,3209734,3209735,
            3209740,3209826,3209994,3210066,3210589,3210590,3210596,3210599,3210600,3211139,3211172,3211182,3211183,3211185,3211186,3211188,3211256,3211257,3211258,3211373,3211388,3211389,3211390,
            3211400,3211499,3211500,3211501,3211502,3211541,3211542,3211569,3211620,3211682,3211738,3211739,3211743,3211753,3211760,3211764,3211774,3211778,3211805,3211806,3211863,3211894,3211898,
            3211901,3211935,3211941,3211942,3211943,3211998,3212034,3212048,3212049,3212064,3212065,3212066,3213179,3213192,3213193,3213380,3213386,3213388,3213534,3213553,3213554,3213559,3213678,
            3213679,3213680,3213681,3213682,3213686,3213687,3213688,3213708,3213719,3213720,3213855,3213866,3213868,3213869,3213871,3213872,3213897,3213898,3213903,3213904,3214052,3214076,3214085,
            3214088,3214092,3214093,3214102,3214274,3214292,3214293,3214295,3214298,3214301,3214312,3214440,3214498,3214499,3214507,3214509,3214510,3214640,3214738,3214739,3214740,3214747,3214749,
            3214750,3214761,3214848,3214896,3214955,3214957,3214960,3214962,3215099,3215107,3215129,3215135,3215137,3215177,3215182,3215183,3215184,3215185,3215186,3217405,3217412,3217417,3217599,
            3217625,3217626,3217627,3217633,3217634,3217637,3217641,3217653,3217656,3217658,3217659,3217660,3217661,3217665,3217666,3217667,3217668,3217670,3217671,3217673,3217674,3217675,3217677,
            3217679,3219828,3219830,3219831,3219832,3219833,3219834,3219843,3219844,3219859,3220104,3220127,3220128,3220148,3220150,3220151,3220154,3220156,3220157,3220320,3220334,3220337,3220339,
            3220340,3220341,3220342,3220384,3220385,3220386,3220387,3220388,3225848,3225851,3225865,3225870,3225939,3225986,3226004,3226005,3227172,3227173,3227235,3227266,3227267,3227268,3227274,
            3227466,3227467,3227468,3227471,3227476,3227505,3227506,3228233,3228257,3228258,3228325,3228326,3228358,3228549,3228569,3229246,3230971,3230972,3230990,3230997,3231002,3231902,3231935,
            3231936,3232331,3232338,3232339,3232342,3232346,3232353,3232661,3232676,3232895,3232899,3232900,3232901,3233019,3233028,3233262,3233271,3233272,3233280,3233305,3233306,3233307,3233567,
            3233589,3233590,3233782,3233784,3233785,3233850,3233856,3233858,3236232,3236233,3236234,3236252,3236450,3236453,3236464,3236501,3236509,3236510,3236511,3236521,3237020,3237082,3237083,
            3237085,3237086,3237087,3237090,3237091,3237092,3237093,3237168,3237172,3237173,3237175,3237177,3237179,3237181,3237183,3237330,3237331,3237333,3237334,3237352,3237416,3237431,3237432,
            3237760,3237787,3237792,3237795,3237800,3237843,3237845,3238135,3238153,3238158,3238164,3238165,3246316,3246482,3246523,3246562,3246569,3246574,3246701,3246702,3246764,3246766,3246772,
            3246782,3246817,3247224,3247228,3247230,3247231,3247234,3247241,3247242,3247301,3247593,3247595,3247596,3247675,3247688,3247693,3248021,3248048,3248084,3248117,3248118,3248131,3248133,
            3248476,3248477,3248495,3248502,3248504,3248508,3248522,3248527,3248808,3248809,3248897,3248902,3248905,3248933,3248938,3248940,3248943,3248944,3248951,3248962,3248963,3249009,3249010,
            3249011,3249325,3249328,3249333,3249386,3249387,3249399,3249403,3249409,3249410,3249411,3249412,3249413,3249414,3249415,3249416,3249447,3249710,3249711,3249712,3249713,3249714,3249716,
            3249717,3249718,3249720,3249721,3249722,3249976,3249977,3250137,3250143,3250146,3250148,3250160,3250167,3250406,3250530,3250531,3250532,3250534,3250566,3250567,3250575,3250781,3250888,
            3250897,3250910,3250914,3250916,3250917,3250921,3250930,3250939,3250946,3250947,3250951,3250960,3250961,3250965,3250971,3251317,3251318,3251320,3251335,3251356,3251388,3251389,3251391,
            3251415,3251650,3251683,3251784,3251785,3251810,3251811,3251830,3251834,3251846,3251847,3252101,3252122,3252124,3252125,3252135,3252141,3252147,3252160,3252325,3252333,3252341,3252368,
            3252371,3252372,3252381,3252644,3252645,3252649,3252658,3252659,3252660,3252679,3252794,3252799,3252817,3252821,3252823,3255553,3255816,3255996,3256016,3256052,3256566,3256663,3256668,
            3256671,3256714,3256715,3257015,3257059,3257061,3257062,3257070,3257080,3257362,3257387,3257411,3257412,3257413,3257414,3257641,3257652,3257667,3257710,3257737,3257738,3257744,3257960,
            3257961,3257962,3257965,3257966,3257973,3257974,3258226,3258228,3258230,3258241,3258242,3258304,3258553,3258554,3258556,3258567,3258586,3258607,3258608,3258646,3258647,3258648,3258930,
            3258933,3258994,3259004,3259309,3259362,3259363,3259364,3259368,3259864,3259866,3259870,3259873,3259880,3259911,3259913,3259916,3259936,3259946,3260129,3260239,3260260,3260269,3260278,
            3260279,3260281,3260290,3260298,3260299,3260300,3260301,3260302,3260308,3260309,3260537,3260538,3260542,3260550,3260579,3260581,3260583,3260689,3260812,3260813,3260814,3260832,3260854,
            3260855,3260905,3260907,3260910,3260912,3260913,3260916,3261194,3261268,3261269,3261272,3261296,3261312,3261313,3261722,3263165,3263166,3263245,3263246,3263248,3263588,3263589,3263606,
            3263611,3264711,3264788,3264806,3264818,3264819,3264820,3265023,3265184,3265185,3265188,3265204,3265215,3265218,3265221,3265226,3265231,3265238,3265253,3265544,3265561,3265605,3265606,
            3265608,3265609,3265610,3265611,3265612,3265613,3265614,3265617,3265618,3265629,3265630,3265631,3265632,3265633,3265634,3265635,3265795,3265824,3265825,3265827,3265831,3265835,3265843,
            3265892,3265893,3265897,3265898,3265899,3265900,3265901,3265902,3265903,3265904,3266200,3266206,3266207,3266212,3266213,3266214,3266215,3266221,3266222,3266235,3266250,3266251,3266252,
            3266253,3266257,3266259,3266260,3266261,3266263,3266265,3266272,3266274,3266275,3266276,3266277,3266297,3266299,3266300,3266301,3266302,3266303,3266304,3266305,3266306,3266308,3266309,
            3266310,3266311,3266312,3266664,3266674,3266675,3266679,3266690,3266691,3266694,3266701,3266702,3266718,3266720,3266729,3267055,3267060,3267061,3267067,3267069,3267070,3267071,3267073,
            3267074,3267122,3267124,3267133,3267158,3267161,3267207,3267533,3267534,3267536,3267539,3267547,3267548,3267549,3267554,3267559,3267580,3267584,3267624,3267625,3267626,3267892,3267910,
            3267960,3267967,3267982,3268004,3268006,3268009,3268012,3268022,3268023,3268096,3268434,3268454,3268455,3268456,3268457,3268469,3268470,3268485,3268486,3268487,3268489,3268490,3268491,
            3268493,3268494,3268496,3268800,3268829,3268830,3268831,3268833,3268834,3269143,3269151,3269153,3269158,3269161,3269162,3271878,3274592,3274593,3274594,3274595,3274596,3274604,3274605,
            3274606,3274607,3274608,3274609,3274618,3274858,3274935,3274949,3274963,3274964,3274966,3274969,3274970,3274971,3274976,3274978,3274980,3274981,3274996,3275358,3275364,3275366,3275376,
            3275384,3275425,3281878,3282188,3282210,3282211,3282226,3282468,3282532,3282584,3282585,3282590,3282594,3282600,3282602,3282603,3282608,3282619,3282620,3282626,3282840,3282842,3282843,
            3282855,3282873,3282877,3282878,3282879,3282880,3282881,3283066,3283067,3283068,3283069,3283072,3283080,3283088,3283090,3283091,3283092,3283093,3283094,3283301,3283373,3283374,3283378,
            3286602,3286603,3286612,3286622,3286628,3286860,3286861,3286866,3286870,3286894,3286939,3286940,3286942,3286943,3286952,3286954,3287456,3287470,3287481,3287483,3287485,3287490,3287848,
            3287849,3288277,3288281,3288286,3288287,3289905,3290106,3290107,3290108,3290129,3290894,3290897,3290904,3291015,3291016,3291302,3291304,3291305,3291337,3293557,3293646,3293652,3293653,
            3293654,3293988,3293997,3294000,3294011,3294037,3294043,3295661,3295662,3295663,3295669,3295670,3295675,3295677,3298441,3298443,3298444,3298445,3298447,3298448,3298451,3298457,3298458,
            3298459,3298460,3298462,3298468,3298469,3298470,3298741,3298749,3298750,3298768,3298774,3298775,3298780,3298781,3298783,3298789,3299036,3299037,3299346,3299712,3299713,3299715,3299716,
            3299717,3299719,3299723,3299728,3299732,3299735,3299738,3299741,3299742,3299743,3299746,3299747,3299748,3299749,3299751,3302485,3302507,3302906,3302907,3302908,3302920,3302922,3302926,
            3302931,3302932,3302933,3302934,3302935,3302951,3302956,3302957,3302959,3302961,3302962,3303361,3303362,3303364,3303398,3303401,3303402,3303403,3303407,3303409,3303410,3304190,3304195,
            3304202,3304203,3304207,3304253,3304258,3304264,3304265,3309839,3309843,3309844,3316641,3316692,3316698,3316714,3316715,3316716,3316728,3316729,3316731,3316733,3316815,3316853,3316854,
            3316855,3320000,3320001,3320005,3320016,3320017,3320022,3320023,3320024,3320025,3320029,3320032,3320033,3320034,3320036,3320045,3320046,3320047,3320443,3320444,3320445,3320452,3320453,
            3320457,3320799,3320830,3320831,3320833,3320834,3320843,3320845,3320847,3320849,3320861,3320891,3320892,3320893,3320944,3320945,3320981,3320983,3320992,3320996,3320997,3321005,3321029,
            3321030,3321038,3321039,3321387,3321393,3321394,3323031,3323044,3323045,3323048,3323049,3323050,3323051,3323052,3323054,3323057,3324223,3324232,3324233,3324268,3326617,3326618,3326619,
            3326710,3327067,3327068,3327106,3327111,3327113,3327116,3327135,3327136,3327144,3327405,3327616,3327652,3327665,3327852,3327853,3327854,3327855,3327865,3327876,3327878,3327881,3328287,
            3328288,3328289,3328290,3328318,3328320,3328326,3328327,3328347,3328631,3329034,3329040,3329044,3329048,3329051,3329052,3329053,3329054,3329061,3329442,3329443,3329445,3329447,3329448,
            3329449,3329451,3329452,3329453,3329466,3329471,3329472,3329475,3329476,3329482,3329484,3329485,3329486,3329489,3329490,3329491,3329533,3329535,3329536,3329537,3329546,3329871,3329872,
            3329875,3329877,3329878,3329883,3329904,3329906,3329907,3329915,3329916,3329917,3330226,3330227,3330228,3330229,3330230,3330236,3330239,3330249,3330264,3330265,3330292,3330293,3330302,
            3330304,3330618,3330692,3330693,3330694,3330696,3330697,3330754,3330808,3330810,3330811,3330814,3330842,3330910,3330911,3330931,3331114,3331115,3331116,3331159,3331235,3331246,3331253,
            3331254,3331255,3331256,3331257,3331258,3331474,3331764,3331793,3331799,3331858,3331861,3331862,3331864,3331865,3331867,3331919,3331920,3332210,3332211,3332258,3332259,3332260,3332272,
            3332273,3332722,3332724,3332726,3332727,3332728,3332729,3332730,3332731,3332732,3332733,3332734,3332735,3333067,3333068,3333069,3333072,3333097,3333098,3333099,3333106,3333111,3333114,
            3336394,3336401,3336402,3336403,3336405,3336407,3336409,3336410,3336427,3336432,3336433,3336435,3336437,3336439,3336440,3336466,3336469,3336899,3336900,3336901,3336902,3336927,3336929,
            3336933,3336934,3336935,3336936,3336937,3336945,3336972,3336980,3336985,3337519,3337520,3337738,3337747,3337753,3337754,3337755,3337756,3338554,3338555,3338557,3338560,3338567,3338574,
            3338575,3338954,3338968,3338970,3338971,3338972,3338975,3338986,3338987,3339001,3339002,3339516,3339517,3339519,3339520,3339541,3339542,3339573,3339574,3339623,3339733,3340142,3340143,
            3340528,3340529,3340530,3340531,3340532,3340533,3340539,3340540,3340570,3340597,3340598,3340638,3341045,3341046,3341047,3341048,3341049,3341417,3341492,3341504,3341505,3341506,3341507,
            3341508,3341645,3342917,3343289,3343296,3343297,3343331,3343334,3343336,3345801,3345829,3346104,3346383,3346423,3346445,3346456,3346457,3346473,3346515,3346540,3346541,3346825,3346839,
            3346841,3347331,3347387,3347388,3347389,3347390,3347396,3347397,3347398,3347399,3347812,3347833,3347835,3347836,3347838,3348280,3348281,3348314,3348375,3348376,3348377,3348393,3348394,
            3348395,3348595,3348895,3348960,3348965,3348966,3348967,3348970,3348971,3349130,3349218,3349409,3349863,3349904,3349914,3350380,3350381,3350383,3350385,3350386,3350396,3350403,3350442,
            3350447,3350879,3350885,3350887,3350888,3350890,3350891,3350892,3350893,3350894,3350910,3350912,3350942,3351148,3351216,3351239,3351241,3351725,3351801,3351806,3351813,3351821,3351825,
            3351870,3351871,3351872,3352026,3352053,3352054,3352055,3352056,3352083,3352934,3353011,3353012,3353013,3353018,3353022,3353053,3353061,3353062,3353094,3353095,3353234,3353235,3353236,
            3353295,3353687,3353688,3353711,3353738,3353739,3353740,3353741,3353743,3353744,3353755,3353759,3353762,3353763,3354236,3354670,3354673,3354691,3355185,3355187,3355212,3355235,3355237,
            3355243,3355252,3355256,3355258,3356145,3356147,3356198,3356199,3356200,3356207,3356211,3356486,3356546,3356976,3357042,3357050,3357051,3357052,3357054,3357055,3357060,3357136,3357618,3358249,3358326,3358336,3358350,3358351,3358363,3358364,3358834,3358835,3358836,3358839,3358841,3358842,3358849,3359013,3359016,3359017,
            3359056,3359057,3359071,3359083,3359307,3360108,3360109,3360178,3360819,3360821,3360822,3360824,3360825,3360834,3360835,3360836,3360837,3360843,3360848,3360904,3362332,3362365,3362400,3362402,3362403,3362463,3362465,3362466,3363057,3363456,3363552,3363554,3363564,3363565,3363566,3363572,3363573,3363574,3363588,3363590,3363591,3363598,3363599,3363614,3363615,3363616,3363617,3363618,3363619,3363620,3363621,3363624,3363625,3363669,3364220,3364221,3364287,3364288,3364289,3364295,3364296,3364335,3364336,3364338,3364997,3365054,3365073,3365074,3365075,3365083,3365086,3365088,3365089,3365090,3365091,3365095,3365102,3365103,3365126,3365127,3365956,3366016,3366017,3366018,3366019,3366021,3366025,3366028,3366029,3366060,3366061,3366062,3366065,3366074,3366104,3366105,3366106,3366112,3366113,3366187,3366192,3366193,3366789,3366796,3366954,3366955,3367133,3367134,3367146,3367149,3367160,3367162,3367169,3367292,3368535,3368536,3368538,3368541,3368543,3368589,3368591,3368592,3368609,3369272,3369314,3369337,3369338,3369339,3369340,3369344,3369345,3375685,3375706,3375707,3375708,3375722,3375725,3375727,3375734,3375735,3375736,3375781,3375784,3375785,3375805,3376190,3376191,3376192,3376195,3376196,3376207,3376831,3376835,3376854,3376903,3376905,3376907,3376909,3376939,3376941,3376942,3376944,3376983,3377424,3377426,3377438,3377439,3377440,3377441,3377442,3378178,3378228,3378267,3378268,3378294,3378295,3378296,3378297,3378329,3378368,3378987,3379072,3379073,3379074,3379077,3379141,3379142,3379150,3379151,3379152,3379153,3379154,3380306,3380488,3380489,3380491,3380492,3380496,3380497,3380501,3380505,3380506,3380552,3380553,3380554,3380555,3380556,3380557,3380558,3380559,3380561,3380562,3381393,3381394,3381458,3381459,3381460,3381465,3381477,3381478,3381481,3381482,3382129,3382194,3382195,3382199,3382201,3382202,3382203,3382204,3382205,3382206,3382207,3382208,3382210,3382250,3382251,3382253,3382257,3382258,3382259,3382260,3385103,3385138,3385184,3385186,3386703,3386785,3386786,3386787,3386788,3386789,3386790,3386791,3386823,3386824,3386825,3386827,3386831,3386834,3386848,3386854,3386859,3386860,3386862,3386865,3387441,3387442,3387443,3387609,3387652,3387661,3387662,3387663,3387664,3387665,3387667,3387678,3387693,3387697,3387698,3387699,3387700,3387706,3387752,3387813,3387815,3387816,3387817,3387818,3387819,3388474,3388475,3388476,3388477,3388535,3388536,3388541,3388544,3388545,3388550,3388551,3388560,3389190,3389191,3389192,3389434,3389435,3389436,
            3389438,3389479,3389481,3389482,3389486,3389508,3389509,3389510,3389511,3389512,3389517,3389518,3389570,3389571,3389572,3389574,3390265,3390272,3390273,3390311,3390319,3390327,3390328,3390331,3390332,3390333,3390334,3390335,3390336,3390337,3390338,3390339,3390341,3390344,3390346,3390347,3390369,3390391,3393537,3393538,3393539,3393540,3393542,3393543,3393544,3393547,3393548,3393567,3393568,3393594,3393595,3393596,3393597,3393598,3393599,3393600,3393601,3393602,3393642,3393643,3393645,3393646,3393656,3393657,3393702,3393703,3393732,3393973,3393974,3394045,3394046,3394048,3394107,3394119,3394120,3394123,3394124,3394125,3394194,3394195,3394930,3394940,3394942,3394988,3394989,3394990,3394991,3394992,3395013,3395014,3395016,3395017,3395024,3395025,3395026,3395027,3395028,3395029,3395030,3395031,3395032,3395033,3395035,3395039,3395040,3395041,3395042,3395043,3395044,3395045,3395046,3395047,3395048,3395049,3395050,3395051,3395052,3395053,3395054,3395055,3395056,3395057,3395061,3395062,3395063,3395064,3395065,3395066,3395067,3395068,3395069,3395070,3395071,3395073,3395075,3395077,3395078,3395081,3395082,3395083,3395084,3395094,3395096,3395098,3395099,3395103,3395115,3395192,3395195,3395197,3395228,3395229,3397546,3398349,3398350,3398388,3398404,3398406,3398407,3398461,3398462,3398463,3398464,3398465,3398466,3398467,3398468,3398469,3398472,322808,342428,343230,343232,343234,343237,343249,343253,343255,343257,343259,343261,343264,343265,343266,343268,343275,349124,350936,350960,351138,358850,360614,365137,366100,373408,373409,373604,375399,375798,375950,377479,378131,381154,381155,382461,382885,382890,382891,383144,392799,392857,393311,393313,393315,393472,393656,393849,394249,394741,394742,394743,396622,396623,397520,398555,398561,398563,398865,398866,398868,398869,398871,398872,398876,398882,398884,398887,398889,398893,398917,398918,398919,399218,399220,399221,399222,399223,399224,399226,399228,399244,399248,399250,399419,399434,399437,399453,399455,399832,399833,399834,399836,399838,399840,399843,399844,401408,401412,402828,402829,402830,402832,408813,408816,408818,
            408820,417631,417632,417637,417642,417654,419130,423240,429979,429980,429981,429982,429983,429984,429985,429986,429988,429989,429990,429991,429992,429993,429994,429995,429996,429997,429998,429999,430000,430001,430002,430004,430005,430006,430007,430008,430009,430010,430011,430012,430013,430014,430015,430016,430017,430054,430056,430072,430076,435302,435303,435307,435312,435313,437548,437556,437562,443516,443895,443915,443916,443917,443918,443919,453937,454186,454190,454191,454192,454196,454197,460495,460497,460498,460500,461608,461609,461752,461913,466501,466503,466504,466736,466742,466743,466744,466746,466866,466868,466869,466872,466873,466876,479514,479806,479809,479810,479811,479812,479814,479817,479818,479829,479830,479832,479836,479848,479853,479857,480056,480057,480059,480061,480065,480070,480073,480074,480076,480079,480080,480083,480085,480086,480088,480089,480093,480094,480095,480096,480097,480098,480099,480104,480106,480109,480110,480111,480125,480128,480134,480141,480144,480147,480149,480150,480155,483284,483285,483286,484601,524010,524248,3001829,3001844,3001854,3001866,3001868,3007673,3007953,3008097,3008098,3008102,3008110,3008111,3008113,3008115,3008117,3008121,3008122,3008175,3008176,3008181,3008182,3008188,3015744,3015827,3021845,3021846,3021847,3021848,3021849,3021850,3021851,3021852,3021853,3021854,3021855,3021857,3022272,
            3022273,3022275,3022276,3022277,3022278,3022286,3024717,3024718,3024719,3025141,3025142,3025146,3025149,3032152,3032153,3032154,3032155,3032156,3032157,3032159,3032160,3032161,3032162,3032163,3032164,3032614,3034629,3037427,3037695,3037696,3037746,3037749,3039556,3039573,3043613,3043614,3043620,3043750,3043751,3043752,3044093,3044104,3044105,3044116,3044121,3044132,3044133,3049058,3049059,3054876,3054881,3054894,3054895,3054898,3054899,3054900,3055109,3055139,3055142,3057326,3057584,3057585,3058398,3060106,3060109,3060116,3060117,3064453,3064454,3073137,3073144,3073145,3074291,3074506,3074507,3074508,3076971,3076990,3076991,3076992,3076993,3076994,3079878,3079879,3079880,3079881,3081823,3081824,3081825,3082079,3082310,3082311,3082423,3082438,3082442,3082444,3087744,3092017,3092018,3092020,3092021,3092022,3092023,3092707,3093189,3093291,3093295,3093983,3094087,3094088,3094089,3094090,3094091,3094092,3094093,3094094,3094095,3094096,3094097,3096327,3096328,3096562,3096564,3096958,3096959,3097047,3097048,3099286,3099287,3121068,3121086,3121325,3121327,3121328,3121960,3121978,3121979,3123097,3123117,3123120,3125932,3125933,3129477,3129535,3129781,3141057,3148244,3148246,3148247,3148253,3148258,3148260,3163760,3164178,342418,343538,360613,364975,401998,402010,402048,402049,402606,403278,454198,454199,475208,475211,475212,3001959,3002046,3002047,3002048,3002049,3002052,3002053,3002054,3002057,3002058,3002060,3002066,3002069,3002070,3002071,3002072,3002075,3002076,3002087,3002088,3002090,3002091,3002092,3002093,3003556,3003558,3003559,3003943,3007505,3007506,3007508,3007509,3007510,3007511,3007512,3007513,3007514,3007760,3007761,3007763,3007766,3008014,3008015,3008016,3008017,3008018,3008019,3008020,3008021,3008125,3015756,3015757,3015758,3015759,3015760,3015761,3015774,3015775,3015776,3015777,3015778,3034499,3034501,3034503,3034505,3034817,3045498,3045538,3049406,3058344,3058345,3060100,3060189,3060191,3060192,3060193,3060195,3060225,3060227,3060228,3060229,3060230,3060231,3060232,3060234,3060235,3060236,3062406,3062408,3064457,3064458,3064459,3064663,3064666,3064667,3064669,3064684,3064685,3068153,3068155,3070680,3070681,3070688,3070689,3070690,3070691,3070692,3070693,3072752,3073209,3073210,3073586,3073588,3073994,3085451,3085453,3085891,3122825,3122831,3122832,3129488,3158424,253104,342337,342338,342342,342343,342344,343207,343208,343209,343210,343211,343212,343213,343214,343215,343216,375799,423261,437537,437565,437585,443906,443907,
            443908,484241,484242,484243,484244,484245,484246,3003716,3007671,3085929,3085930,3187796,3196449,3196450,3196451,3196452,3196453,3196454,3196455,3196456,3196457,3196458,3196459,3196460,3196461,3196478,3196479,3196480,3196481,3196483,3196484,3198900,3198901,3198902,3198903,3198904,3198905,3198906,3198907,3198908,3198909,3198910,3198911,3198912,3198913,3198914,3248087,3263625,3263626,3263627,3263628,3263629,3263630,3263631,3263632,3263633,3263709,3263710,3263711,3263712,3263713,3263715,3263716,3263717,3263719,3264367,3264368,3264369,3264370,3264371,3264678,3264679,3264696,3264697,3264698,3264700,3264701,3264702,3264703,3264704,3264705,3264706,3264707,3264708,3264709,3264712,3264713,3264714,3264715,3264716,3264717,3264718,3264719,3264720,3264721,3264725,3264726,3264727,3264728,3264729,3264730,3264731,3264732,3264733,3264734,3264735,3264736,3264737,3264738,3264739,3264740,3264741,3264742,3264743,3264744,3264745,3264746,3264747,3264748,3264749,3264750,3264751,3264752,3264753,3264754,3264755,3264756,3264757,3264758,3264759,3264760,3264761,3264762,3264763,3264764,3264765,3264766,3264767,3264768,3264769,3264770,3264771,3264772,3264773,3264774,3264775,3264776,342388,342389,342390,342391,342392,342394,342419,342421,342422,342423,342424,342425,342427,364974,365136,365457,368812,373712,373713,373738,375797,377164,377169,377170,377201,377206,381998,382893,382894,393108,393113,393117,393120,393265,393268,393269,393271,394173,394177,394247,396663,396964,397069,397070,397071,397479,397487,397494,397495,397496,398855,398857,398863,398864,398921,399823,401864,401866,401867,402173,402836,402837,414765,423227,437542,437543,453386,454188,479537,479554,479959,479961,479962,484579,484584,484593,484597,524027,524201,524562,3002097,3015962,3034606,3034609,3034615,3034616,3034617,3034618,3035270,3037449,3037707,3045173,3049599,3055844,3055846,3055847,3057354,3057358,3057445,3058054,3058055,3058059,3058063,3058129,3062446,3068160,3070671,3070673,3070674,3070675,3070677,3086217,3086220,3092357,3092702,3092706,3092905,3093013,3096474,3118329,3121655,3121656,3121658,3121659,3121660,3121670,3121671,3186927,403283,3125920,3125921,3125922,3125923,3125924,3125925,
                        ];
        $day = gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600);
        foreach ($staff_info_ids as $key => $value) {
            if ($isUpd) {
                $result = Staff::updateItems($value, ['state' => BaseStaffInfo::STATE_RESIGN, 'leave_date' => $day]);
                if ($result !== true) {
                    echo $value . 'error' . PHP_EOL;
                    Yii::$app->logger->write_log(['staff_info_id' => $value, 'result' => $result]);
                } else {
                    echo $value . '---OK' . PHP_EOL;
                }
            }
        }
    }

    //员工职位是Van Courier 快递员的车辆类型是Bike的全部更新为Van
    //员工职位是 Bike Courier 快递员的车辆类型是Van 全部更新为Bike
    public function actionStaffCarType() {
        //查询 员工职位是 Van Courier
        $staff_list = StaffInfo::find()->where(['job_title' => [13,110]])->asArray()->all();
        foreach ($staff_list as $key => $value) {
            $staff_info = Staff::view($value['staff_info_id']);
            if(in_array('1',$staff_info['position_category'])) {
                //Van Courier =》 110//Bike Courier =》 13
                if($staff_info['job_title'] == 13 && $staff_info['staff_car_type'] == 'Van') {
                    //更新车辆类型为 Bike
                    $attr['CAR_TYPE'] = 'Bike';
                    Staff::updateItems($value['staff_info_id'],$attr,'-1');
                    $this->echo($value['staff_info_id'].'车辆类型由Van变更成Bike');
                }
                if($staff_info['job_title'] == 110 && $staff_info['staff_car_type'] == 'Bike') {
                    //更新车辆类型为 Van
                    $attr['CAR_TYPE'] = 'Van';
                    Staff::updateItems($value['staff_info_id'],$attr,'-1');
                    $this->echo($value['staff_info_id'].'车辆类型由Bike变更成Van');
                }
            }
        }
    }

    public function actionStaffUpdateJobtitle() {
        $staff_info_ids = [
            16900,16981,17032,17093,17138,17145,17180,17238,17473,17511,17585,17604,17619,17700,19001,19061,19165,19247,19356,19448,19506,19592,19720,19727,19864,19890,20088,20090,20095,20149,
            20193,20199,20236,20245,20285,20388,20399,20401,20461,20819,20953,21100,21148,21172,21180,21303,21410,21527,21657,21971,21999,22138,22215,22216,22249,22267,22388,22445,22469,22603,
            22860,23066,23068,23421,23638,23760,23763,23902,23983,24180,24197,24314,24465,24806,24843,24854,24860,24939,24958,24990,24995,24997,25003,25105,25106,25129,25133,25141,25459,25463,
            25467,25471,25473,25500,25824,25897,25923,25953,26059,26078,26099,26100,26159,26184,26329,26339,26343,26345,26346,26349,26497,26537,26583,26599,26645,26729,26785,26790,26845,26932,
            26961,26962,26963,26964,26996,26998,27035,27036,27104,27119,27155,27156,27168,27275,27377,27404,27492,27493,27618,27619,27647,27684,27714,27791,27924,27948,28454,28475,28487,28506,
            28572,28582,28589,28593,28628,28705,28709,28783,28808,28837,28849,28876,28877,28936,28995,29012,29020,29022,29023,29025,29030,29036,29037,29092,29106,29143,29196,29197,29207,29234,
            29236,29239,29267,29281,29312,29317,29336,29425,29461,29484,29498,29503,29559,29621,29631,29822,29839,29840,29844,29845,29867,29871,30049,30053,30064,30135,30302,30314,30316,30482,
            30486,30509,30516,30564,30565,30610,30720,30765,30861,30891,30918,31119,31125,31130,31135,31144,31304,31314,31322,31334,31335,31366,31367,31368,31372,31387,31396,31413,31417,31515,
            31517,31523,31525,31528,31531,31532,31539,31540,31542,31543,31549,31566,31575,31581,31582,31584,31666,31668,31670,31675,31737,31749,31770,31773,31775,31782,31790,31804,31814,31816,
            31822,31823,31827,31836,31848,31869,31871,31880,31883,31885,31886,31887,31888,31890,31892,31993,32229,32230,32236,32237,32486,32487,32675,32683,32742,32743,32744,32745,32799,32941,
            32943,33055,33113,33118,33120,33139,33143,33154,33155,33168,33176,33192,33201,33319,33364,33376,33403,33499,33527,33556,33923,33949,33958,33970,33978,34083,34084,34100,34101,34137,
            34187,34434,34435,34489,
        ];
        $this->echo("begin");
        foreach ($staff_info_ids as $key => $value) {
            $before = Staff::view($value);
            if (count($before) == 0) {
                $this->echo('工号：'.$value.'----未找到');
            } else {
                $this->echo('工号：'.$value.'----更新');
                $result = Yii::$app->get('db')->createCommand()->update('hr_staff_info',['job_title' => '37'],'staff_info_id='.$value)->execute();
                $this->echo("----结果：".$result);
                if ($result) {
                    $after = Staff::view($value);
                    $after['outsourcing_category'] = 0;
                    if (!StaffSyncService::getInstance()->saveStaffInfo($after, 1)) {
                        $this->echo('----staff', $value, '-----send msg error');
                    }
                    $op = new HrOperateLogs();
                    $op->operater = -1;
                    $op->type = 'staff';
                    $op->staff_info_id = $value;
                    $op->before = json_encode(['body' => $before]);
                    $op->after = json_encode(['body' => $after]);
                    $op->save();

                    $staff_transfer_log = new HrStaffTransferLog();
                    $staff_transfer_log->staff_info_id = $value;
                    $staff_transfer_log->cur_value = '37';
                    $staff_transfer_log->old_value = $before['job_title'];
                    $staff_transfer_log->type = 2;
                    $staff_transfer_log->change_date = date('Y-m-d',time());
                    $transfer = $staff_transfer_log->save();
                    $this->echo("----transfer：".$transfer);
                    $this->echo("----".$value, '-----ok');
                } else {
                    $this->echo("----".$value, '-----没有更新');
                }
            }
            /*
            $attr['job_title'] = '37';
            $staffs = Staff::view($value);
            if($staffs) {
                if(Staff::updateItems($value,$attr,'-1')) {
                    $staff_transfer_log = new HrStaffTransferLog();
                    $staff_transfer_log->staff_info_id = $value;
                    $staff_transfer_log->cur_value = 37;
                    $staff_transfer_log->old_value = $staffs['job_title'];
                    $staff_transfer_log->type = 2;
                    $staff_transfer_log->change_date = date('Y-m-d',time());
                    $staff_transfer_log->save();
                    $this->echo('工号：'.$value.'----变更成功');
                } else {
                    $this->echo('工号：'.$value.'----变更失败');
                }
            } else {
                $this->echo('工号：'.$value.'----未找到');
            }
            */
        }
        $this->echo('end');
    }

    //银行卡历史数据修改 已经银行卡的卡类型修改成 1
    public function actionStaffBankType() {
        $staff_list = StaffInfo::find()->where(['<>','bank_no',''])->asArray()->all();
        $this->echo(count($staff_list));
        foreach ($staff_list as $key => $value) {
            if($value['bank_type'] == 0) {
                $attr['bank_type'] = 1;
                Staff::updateItems($value['staff_info_id'],$attr,'-1');
                $this->echo($value['staff_info_id'].'更新银行卡类型为1');
            }
        }
    }

    //比较银行卡号
    public function actionStaffBankCardDiff($isUpd = false) {
        $headerSkiper = true;

        $staff_info = BaseStaffInfo::find()->indexBy('staff_info_id')->asArray()->all();//
        $this->readCsv('data/StaffBankNo190807',function ($row) use (&$headerSkiper,&$staff_info,$isUpd){
            if ($headerSkiper) {
                $headerSkiper = false;
                return;
            }
            if(isset($staff_info[$row['staff_info_id']])) {
                if($staff_info[$row['staff_info_id']]['bank_no'] != $row['bank_no']) {
                    $this->echo($row['staff_info_id'].'------DB:'.$staff_info[$row['staff_info_id']]['bank_no'].'------bank_type:'.$staff_info[$row['staff_info_id']]['bank_type'].'------Excel:'.$row['bank_no']);
                    if($isUpd) {
                        $staff = BaseStaffInfo::find()->where(['staff_info_id' => $row['staff_info_id']])->one();
                        $before = Staff::view($row['staff_info_id']);
                        $staff->bank_no = $row['bank_no'];
                        $staff->bank_type = '2';
                        $op = new HrOperateLogs();
                        if($staff->update(true, ['bank_no', 'bank_type']) !== false) {
                            $after = Staff::view($row['staff_info_id']);
                            $op->operater = 999;
                            $op->type = 'staff';
                            $op->staff_info_id = $row['staff_info_id'];
                            $op->before = json_encode(['body' => $before]);
                            $op->after = json_encode(['body' => $after]);
                            $op->save();
                        }
                        unset($op, $before, $after, $staff);
                    }
                }
            } else {
                $this->echo($row['staff_info_id'].'-----'.$row['bank_no'].'-----not found');
            }
        });
        $this->echo('end');
    }

    public function actionStaffEmail($isUpd = false) {

        $staff_email = BaseStaffInfo::find()->where(['not like', 'email', 'flashexpress.com'])
                                            ->andWhere(['!=', 'email', ''])
                                            ->asArray()
                                            ->all();

        foreach ($staff_email as $key => $value) {
            $staff = BaseStaffInfo::find()->where(['staff_info_id' => $value['staff_info_id']])->one();
            if (!$staff) {
                //未找到用户
                $this->echo($value['staff_info_id'].' not found');
            } else {
                if($value['personal_email'] == '') {
                    $this->echo($value['staff_info_id'].'---personal_email:'.$value['personal_email'].'---email:'.$value['email']);
                    $staff->personal_email = $value['email'];
                    $staff->email = '';
                    if($isUpd) {
                        $before = Staff::view($value['staff_info_id']);
                        $op = new HrOperateLogs();
                        if($staff->update(true, ['personal_email','email']) === false) {
                            $this->echo($value['staff_info_id'].'---error'.json_encode($staff->getErrors()));
                        } else {
                            $after = Staff::view($value['staff_info_id']);
                            $op->operater = 999;
                            $op->type = 'staff';
                            $op->staff_info_id = $value['staff_info_id'];
                            $op->before = json_encode(['body' => $before]);
                            $op->after = json_encode(['body' => $after]);
                            $op->save();
                        }
                    }
                }
            }
        }
        $this->echo('end');
    }

    //特殊网点处理
    public function actionStaffEmailUpdate($type = -1,$isUpd = false) {
        switch ($type) {
            case 1://关闭邮箱
                $email_close_staff_info_id = [17402,19059,20238,21524,25729,26483,27793,28139,28166,31698];
                foreach ($email_close_staff_info_id as $key => $value) {
                    $email = HrEmails::find()->where(['staff_info_id' => $value])->one();
                    if(!$email) {
                        $this->echo($value.' not find');continue;
                    }

                    if($isUpd) {
                        $email->state = 2;
                        if($email->save()) {
                            $this->echo($value.' update ok');
                        } else {
                            $this->echo($value.'---error'.json_encode($email->getErrors()));
                        }
                    } else {
                        $this->echo($email->staff_info_id.'---Email.State'.$email->state.'---Email:'.$email->email);
                    }
                }
                break;
            case 2://开启邮箱
                $email_open_staff_info_id = [
                                20278,20383,20443,20446,16887,16893,16890,16944,16987,17021,
                                17427,17062,17066,17109,19953,17140,17160,17136,17135,17194,
                                17155,17200,17273,17306,17685,20038,19060,17497,17495,17539,
                                17580,17603,19646,19088,19084,19082,19308,19103,19288,19349,
                                19353,19110,19495,19496,19593,19597,19610,19606,19697,19701,
                                19867,19974,21305,21306,20234,20243,21426,21536,21613,21835,
                                22490,22750,22774,24023,24165,24485,24694,24901,24902,24903,
                                24917,24918,25224,25357,25509,25513,26803,27012,27047,27148,
                                27361,27710,28123,28138,28140,28162,28536,28558,28560,28602,
                                28674,29362,29798,29974,30003,30273,30889,31253,28568,31570,
                                31342,31849,31850,31853,31854,31859,17102,21318,19947,
                            ];
                foreach ($email_open_staff_info_id as $key => $value) {
                    $email = HrEmails::find()->where(['staff_info_id' => $value])->one();
                    if(!$email) {
                        $this->echo($value.' not find');continue;
                    }
                    if($isUpd) {
                        $email->state = 1;
                        if($email->save()) {
                            $staff = StaffInfo::find()->select(['name', 'mobile'])->where(['staff_info_id' => $email->staff_info_id])->one();
                            $msg = "ยินดีต้อนรับพนักงาน {$email->staff_info_id}({$staff->name}) มาเป็นส่วนหนึ่งของครอบครัว FlashExpress Email ของคุณคือ {$email->email} รหัสเริ่มต้นคือ：Flash123  ขอให้เข้าสู่ระบบเพื่อเปิดใช้งานผ่านทางลิ้งค์ : <a href='flashbackyard://fe/browser?url= https://en.exmail.qq.com/'> https://en.exmail.qq.com/ </a> （กรุณาเปิดด้วยเบราว์เซอร์คอมพิวเตอร์）";

                            $shortMsg = "ยินดีต้อนรับพนักงาน {$email->staff_info_id}({$staff->name}) มาเป็นส่วนหนึ่งของครอบครัว FlashExpress Email ของคุณคือ {$email->email} รหัสเริ่มต้นคือ：Flash123  ขอให้เข้าสู่ระบบเพื่อเปิดใช้งานผ่านทางลิ้งค์ :  https://en.exmail.qq.com/ （กรุณาเปิดด้วยเบราว์เซอร์คอมพิวเตอร์）";

                            if (!empty($staff->mobile)) {
                                Yii::$app->jrpc->smsSend($staff->mobile, $shortMsg);
                            }
                            Yii::$app->jrpc->backYardMessage($email->staff_info_id, $msg);
                            $this->echo($value.' update ok');
                        } else {
                            $this->echo($value.'---error'.json_encode($email->getErrors()));
                        }
                    } else {
                        $this->echo($value.'---Email.State'.$email->state.'---Email:'.$email->email);
                    }
                }
                break;
            case 3://需要关闭的邮箱 条件 员工已离职，邮箱待开启
                $query = HrEmails::find()->innerJoin('hr_staff_info', 'hr_emails.staff_info_id=hr_staff_info.staff_info_id')
                                        ->andFilterWhere(['hr_staff_info.state' => 2])
                                        ->andFilterWhere(['hr_emails.state' => 0])
                                        ->select('hr_emails.state,hr_emails.staff_info_id,hr_staff_info.name,hr_staff_info.state AS staff_state,hr_emails.email,hr_staff_info.leave_date')
                                        ->asArray()
                                        ->all();
                foreach ($query as $key => $value) {
                    if($isUpd) {
                        $email = HrEmails::find()->where(['staff_info_id' => $value['staff_info_id']])->one();
                        $email->state = 2;
                        if($email->save()) {
                            $this->echo($value['staff_info_id'].' update ok');
                        } else {
                            $this->echo($value['staff_info_id'].'---error'.json_encode($email->getErrors()));
                        }
                    } else {
                        $this->echo($value['staff_info_id'].'---Email.State:'.$value['state'].'---Staff.State:'.$value['staff_state'].'---Email:'.$value['email']);
                    }
                }
                break;
            default:
                $this->echo('no param');
                break;
        }
        $this->echo('end');
    }

    //员工更新部门将University Project14 部门的员工部门信息全部变更为“Shop Project” 13；
    public function actionUpdateDepartment() {
        $this->echo('start');
        $staff_list = BaseStaffInfo::find()->where(['sys_department_id' => 14])->all();
        $this->echo('变更总数:'.count($staff_list));
        foreach ($staff_list as $item) {
            //$before = Staff::view($item->staff_info_id);
            $before = BaseStaffInfo::find()->where(['staff_info_id' => $item->staff_info_id])->asArray()->one();
            //$this->echo($value['staff_info_id'].' 变更');
            //$staff = BaseStaffInfo::find()->where(['staff_info_id' => $value['staff_info_id']])->one();
            //$staff->sys_department_id = 13;
            //if($staff->update(true, ['sys_department_id'])){
            if(Yii::$app->backyard_main->createCommand()->update('hr_staff_info', ['sys_department_id' => 13], 'staff_info_id='.$item->staff_info_id)->execute()){
                $this->echo($item->staff_info_id.' 变更部门成功');
            }

            //$after = Staff::view($item->staff_info_id);
            $after = BaseStaffInfo::find()->where(['staff_info_id' => $item->staff_info_id])->asArray()->one();
            $op = new HrOperateLogs();
            $op->operater = '-1';
            $op->type = 'staff';
            $op->staff_info_id = $item->staff_info_id;
            $op->before = json_encode(['body' => json_encode($before)]);
            $op->after = json_encode(['body' => $after]);
            $op->save();
        }
        $this->echo('end');
    }

    //职等、职级
    public function actionJobTitleGradLevel($type = 1) {
        $headerSkiper = true;
        $job_title_list = HrJobTitle::find()->select('job_name')->where(['status' => 1])->indexBy('id')->asArray()->column();
        $department =  SysDepartment::find()->select('name')->andWhere(['not in', 'id', [1]])->indexBy('id')->asArray()->column();
        $this->echo('begin');
        $file_name = $type == 1 ? 'job_title_grad_level' : 'job_title_grade_level_b';
        $this->echo('导入文件:'.$file_name);
        $this->readCsv('data/'.$file_name,function ($row) use (&$headerSkiper,&$job_title_list,&$department){
            if ($headerSkiper) {
                $headerSkiper = false;
                return;
            }
            $j_id = array_search($row['job_title'],$job_title_list);//职位id
            $d_id = array_search($row['department'],$department);//部门id
            $level_id = array_search($row['level'],Yii::$app->sysconfig->config_job_title_level);//职等id
            if(in_array($d_id,[30,31,32,33,34])) {
                //子部门
                $count = BaseStaffInfo::updateAll(['job_title_grade' => $row['grad'],'job_title_level' => $level_id],['node_department_id' => $d_id,'job_title' => $j_id]);
                $this->echo('更新条数:'.$count.',更新完成（node_department_id）,'.$row['department'].'('.$d_id.')---'.$row['job_title'].'('.$j_id.')---'.$row['grad'].'---'.$row['level']);
            } else {
                $count = BaseStaffInfo::updateAll(['job_title_grade' => $row['grad'],'job_title_level' => $level_id],['and',['sys_department_id' => $d_id],['<>','node_department_id',33],['job_title' => $j_id]]);
                $this->echo('更新条数:'.$count.',更新完成（sys_department_id）,'.$row['department'].'('.$d_id.')---'.$row['job_title'].'('.$j_id.')---'.$row['grad'].'---'.$row['level']);
            }
        });
        $this->echo('end');
    }

    //导入职等、职级
    public function actionImportJobTitleGradLevel($type = 1) {
        $headerSkiper = true;
        $job_title_list = HrJobTitle::find()->select('job_name')->where(['status' => 1])->indexBy('id')->asArray()->column();
        $department =  SysDepartment::find()->select('name')->andWhere(['not in', 'id', [1]])->indexBy('id')->asArray()->column();
        $this->echo('begin');
        $file_name = $type == 1 ? 'data/job_title_grad_level' : 'data/job_title_grade_level_b';
        $this->echo('导入文件:'.$file_name);
        $this->readCsv($file_name,function ($row) use (&$headerSkiper,&$job_title_list,&$department){
            if ($headerSkiper) {
                $headerSkiper = false;
                return;
            }
            $j_id = array_search($row['job_title'],$job_title_list);//职位id
            $d_id = array_search($row['department'],$department);//部门id
            $level_id = array_search($row['level'],Yii::$app->sysconfig->config_job_title_level);//职等id
            $r = HrDepartmentJobTitleLevelGrade::find()
                                ->where(['department_id' => $d_id])
                                ->andWhere(['job_title_id' => $j_id])
                                ->andWhere(['job_title_level' => $level_id])
                                ->andWhere(['job_title_grade' => $row['grad']])
                                ->one();
            if(!$r) {
                $model = new HrDepartmentJobTitleLevelGrade();
                $model->department_id = $d_id;
                $model->job_title_id = $j_id;
                $model->job_title_level = $level_id;
                $model->job_title_grade = $row['grad'];
                $model->save();
                $this->echo('导入：'.$row['department'].'('.$d_id.')'.'---'.$row['job_title'].'('.$j_id.')'.'---'.$row['level'].'('.$level_id.')'.'---'.$row['grad']);
            } else {
                $this->echo('已存在'.$row['department'].'('.$d_id.')'.'---'.$row['job_title'].'('.$j_id.')'.'---'.$row['level'].'('.$level_id.')'.'---'.$row['grad']);
            }
        });
        $this->echo('end');
    }

    public function actionImportStaffWorkDay() {
        $headerSkiper = true;
        $this->echo('begin');
        $this->readCsv('data/importwordday',function ($row) use (&$headerSkiper){
            if ($headerSkiper) {
                $headerSkiper = false;
                return;
            }
            $staff_info_id = $row['staff_info_id'];
            $week_working_day = $row['week_working_day'];
            $staff_model = BaseStaffInfo::find()->where(['staff_info_id' => $staff_info_id])->one();
            if($staff_model) {
                if(Yii::$app->backyard_main->createCommand()->update('hr_staff_info', ['week_working_day' => $week_working_day], 'staff_info_id='.$staff_info_id)->execute()){
                    $this->echo($staff_info_id.'---update ok');
                } else {
                    $this->echo($staff_info_id.'---数据未更新');
                }
            } else {
                $this->echo('未找到工号:'.$staff_info_id);
            }
        });
        $this->echo('end');
    }

    public function actionImportMobileCompany() {
        //导入指定工号企业号码
        $this->echo('begin');
        $excelFile = __DIR__.'/data/staff_mobile_company202003.xlsx';
        //读取Excel，xlsx后缀文件用Excel2007，xls后缀用Excel5
        $excelReader = \PHPExcel_IOFactory::createReader('Excel2007');
        $excelReader->setReadDataOnly(true);
        //载入文件并获取第一个sheet
        $sheet = $excelReader->load($excelFile)->getSheet(0);
        $total_line = $sheet->getHighestRow();

        for ($row = 2; $row <= $total_line; $row++) {
            $excelData = [];
            $info = [];
            for ($column = 'A'; $column <= 'C'; $column++) {
                $excelData[] = trim($sheet->getCell($column . $row)->getValue());
            }
            if(empty($excelData[0]) || empty($excelData[1])) {
                continue;
            }

            //验证该工号有没有被使用
            $verify_staff = BaseStaffInfo::find()->where(['mobile_company' => $excelData[2], 'state' => [1, 3], 'is_sub_staff' => 0,])->one();
            if ($verify_staff) {
                $this->echo('工号:'.$verify_staff->staff_info_id.'--已使用该号码号码'.$excelData[2].';Excel工号:'.$excelData[0].'-----Excel号码:'.$excelData[2]);
                continue;
            }

            $staff = BaseStaffInfo::find()->where(['staff_info_id' => $excelData[0]])->one();
            if (!$staff) {
                //未找到用户
                $this->echo('工号:'.$excelData[0].'   not found');
            } else  {
                $attr = ['mobile_company' => $excelData[2]];
                $result = Staff::updateItems($excelData[0],$attr,'-1');
                if($result === true) {
                    $this->echo('工号:'.$excelData[0].'在职状态:'.$staff->state.';--更新完成--'.$staff->mobile_company.'->'.$excelData[2]);
                } else {
                    $this->echo('工号:'.$excelData[0].'更新失败.'.json_encode($result));
                }
            }
        }
        $this->echo('end');
    }

    public function actionImportGradeLevel() {
        $this->echo('begin');
        $job_title_list = HrJobTitle::find()->select('job_name')->where(['status' => 1])->indexBy('id')->asArray()->column();
        $department =  SysDepartment::find()->select('name')->andWhere(['not in', 'id', [1]])->indexBy('id')->asArray()->column();
        $excelFile = __DIR__.'/data/GradLevel20200330.xlsx';
        //读取Excel，xlsx后缀文件用Excel2007，xls后缀用Excel5
        $excelReader = \PHPExcel_IOFactory::createReader('Excel2007');
        $excelReader->setReadDataOnly(true);
        //载入文件并获取第一个sheet
        $sheet = $excelReader->load($excelFile)->getSheet(0);
        $total_line = $sheet->getHighestRow();

        for ($row = 2; $row <= $total_line; $row++) {
            $excelData = [];
            $info = [];
            for ($column = 'A'; $column <= 'F'; $column++) {
                $excelData[] = trim($sheet->getCell($column . $row)->getValue());
            }
            if(empty($excelData[0]) || empty($excelData[1])) {
                continue;
            }
            //C D E F
            $j_id = array_search($excelData[3],$job_title_list);//职位id
            $d_id = array_search(trim($excelData[2]),$department);//部门id
            $level_id = array_search($excelData[4],Yii::$app->sysconfig->config_job_title_level);//职等id

            $r = HrDepartmentJobTitleLevelGrade::find()
                ->where(['department_id' => $d_id])
                ->andWhere(['job_title_id' => $j_id])
                ->andWhere(['job_title_level' => $level_id])
                ->andWhere(['job_title_grade' => $excelData[5]])
                ->one();

            if(!$r) {
                $model = new HrDepartmentJobTitleLevelGrade();
                $model->department_id = $d_id;
                $model->job_title_id = $j_id;
                $model->job_title_level = $level_id;
                $model->job_title_grade = $excelData[5];
                $model->save();
                $this->echo('导入：'.$excelData[2].'('.$d_id.')'.'---'.$excelData[3].'('.$j_id.')'.'---'.$excelData[4].'('.$level_id.')'.'---'.$excelData[5]);
            } else {
                $this->echo('已存在'.$excelData[2].'('.$d_id.')'.'---'.$excelData[3].'('.$j_id.')'.'---'.$excelData[4].'('.$level_id.')'.'---'.$excelData[5]);
            }
        }
        $this->echo('end');
    }

    //导入默认职等职级
    public function actionImportGradeLevelV2($isUpd = false) {
        $this->echo('begin');
        $job_title_list = HrJobTitle::find()->select('job_name')->where(['status' => 1])->indexBy('id')->asArray()->column();
        $department =  SysDepartment::find()->select('name')->andWhere(['not in', 'id', [1]])->indexBy('id')->asArray()->column();
        $excelFile = __DIR__.'/data/grade-level200515.xlsx';
        //读取Excel，xlsx后缀文件用Excel2007，xls后缀用Excel5
        $excelReader = \PHPExcel_IOFactory::createReader('Excel2007');
        $excelReader->setReadDataOnly(true);
        //载入文件并获取第一个sheet
        $sheet = $excelReader->load($excelFile)->getSheet(0);
        $total_line = $sheet->getHighestRow();
        $this->echo('Excel 总行数：'.$total_line);
        $num = 0;
        for ($row = 2; $row <= $total_line; $row++) {
            $num++;
            $excelData = [];
            $info = [];
            //A:department(部门);B:Position(职位);C:Level(职级);D:JG(职等)
            for ($column = 'A'; $column <= 'D'; $column++) {
                $excelData[] = trim($sheet->getCell($column . $row)->getValue());
            }
            if(empty($excelData[0]) || empty($excelData[1])) {
                continue;
            }
            //A:department(部门);B:Position(职位);C:Level(职级);D:JG(职等)
            $j_id = array_search($excelData[1],$job_title_list);//职位id
            $d_id = array_search(trim($excelData[0]),$department);//部门id
            $level_id = array_search($excelData[2],Yii::$app->sysconfig->config_job_title_level);//职等id

            $r = HrDepartmentJobTitleLevelGrade::find()
                ->where(['department_id' => $d_id])
                ->andWhere(['job_title_id' => $j_id])
                ->andWhere(['job_title_level' => $level_id])
                //->andWhere(['job_title_grade' => $excelData[3]])
                ->one();

            if(!$r) {
                if($isUpd) {
                    $model = new HrDepartmentJobTitleLevelGrade();
                    $model->department_id = $d_id;
                    $model->job_title_id = $j_id;
                    $model->job_title_level = $level_id;
                    $model->job_title_grade = $excelData[3];
                    $model->save();
                    $this->echo($num.'新增Insert：'.$excelData[0].'('.$d_id.')'.'---'.$excelData[1].'('.$j_id.')'.'---'.$excelData[2].'('.$level_id.')'.'---'.$excelData[3]);
                } else {
                    $this->echo($num.'新增:'.$excelData[0].'('.$d_id.')'.'---'.$excelData[1].'('.$j_id.')'.'---'.$excelData[2].'('.$level_id.')'.'---'.$excelData[3]);
                }
            } else {
                if($r->job_title_grade != $excelData[3]) {
                    if($isUpd) {
                        $r->job_title_grade = $excelData[3];
                        $r->save();
                        $this->echo($num.'已存在,Update:'.$excelData[0].'('.$d_id.')'.'---'.$excelData[1].'('.$j_id.')'.'---'.$excelData[2].'('.$level_id.')'.'---'.$excelData[3].'-----'.$r->job_title_grade);
                    } else {
                        $this->echo($num.'已存在,需要修改:'.$excelData[0].'('.$d_id.')'.'---'.$excelData[1].'('.$j_id.')'.'---'.$excelData[2].'('.$level_id.')'.'---'.$excelData[3].'-----'.$r->job_title_grade);
                    }
                } else {
                    $this->echo($num.'已存在:'.$excelData[0].'('.$d_id.')'.'---'.$excelData[1].'('.$j_id.')'.'---'.$excelData[2].'('.$level_id.')'.'---'.$excelData[3].'-----'.$r->job_title_grade);
                }
            }
        }
        $this->echo('end');
    }

    public function actionStaffHold($staff_info_id,$type,$payment_markup,$stop_payment_type,$operaterId) {
        //员工hold 管理
        $this->echo($staff_info_id.'-----'.$type.'-----'.$payment_markup.'-----'.$stop_payment_type.'-----'.$operaterId);
        $attr = [
            'type' => $type,//1 hold  2 释放
            'payment_markup' => $payment_markup,//工资阻止发放原因
            'stop_payment_type' => $stop_payment_type, //工资阻止发放类型 1工资；2提成
        ];
        $result = Staff::updateStaffHoldFromBiHold($staff_info_id,$attr, $operaterId);
        $this->echo(json_encode($result));
    }

    //修改将属于[4]network management，职位为[13]bike courier,[110]van courier,[37]DC Officer,[451]assistant branch supervisor,[16]branch supervisor，状态为在职的正式员工所属部门变更为 [32]network operations
    public function actionUpdateStaffDepartment() {
        ini_set('memory_limit','1024M');
        //select COUNT(1) FROM `hr_staff_info` WHERE `formal` =1 and `is_sub_staff` =0 and `job_title` IN (13,110,37,451,16) and `sys_department_id` =4 and `node_department_id` !=32 and `state` =1
        $this->echo('begin');
        $list = BaseStaffInfo::find()
                    ->where(['formal' => 1])
                    ->andWhere(['job_title' => [13,110,37,451,16]])
                    ->andWhere(['sys_department_id' => 4])
                    ->andWhere(['state' => 1])
                    ->andWhere(['!=', 'node_department_id', 32])
                    ->asArray()
                    ->all();

        foreach ($list as $key => $value) {
            $attr['node_department_id'] = 32;
            $r = Staff::updateItems($value['staff_info_id'],$attr,'-1');
            $this->echo($key.':'.$value['staff_info_id'].'----结果:'.$r);
        }

        $this->echo('end');
    }

    //【FBI】hub部门网点员工更新至二级部门
    public function actionStaffHub() {
        $store_list_57 = ['TH02030204','TH05110400'];
        $staff_list_58 = ['TH01470301','TH27011602','TH68010201','TH56010102','TH47190703','TH37010701','TH38040201','TH71111201','TH61040103','TH64100100','TH20050103'];
        $staff_list_59 = ['TH49030503','TH54011300','TH31130200','TH70011200','TH32010103','TH28011404'];

        $this->echo('begin');
        $store_list = ['TH02030204','TH05110400','TH01470301','TH27011602','TH68010201','TH56010102','TH47190703','TH37010701','TH38040201','TH71111201','TH61040103','TH64100100','TH20050103','TH49030503','TH54011300','TH31130200','TH70011200','TH32010103','TH28011404'];
        $staff_list = BaseStaffInfo::find()->where(['formal' => 1])->andWhere(['state' => 1])->andWhere(['node_department_id' => 0])->andWhere(['in','sys_store_id',$store_list])->asArray()->all();
        $this->echo('变更中人数：'.count($staff_list));
        foreach ($staff_list as $key => $value) {
            $node_department_id = 57;
            if(in_array($value['sys_store_id'],$store_list_57)) {
                $this->echo('[57]Hub Large Size---员工id：'.$value['staff_info_id']);
                $node_department_id = 57;
            }
            if(in_array($value['sys_store_id'],$staff_list_58)) {
                $this->echo('[58]Hub Medium Size---员工id：'.$value['staff_info_id']);
                $node_department_id = 58;
            }
            if(in_array($value['sys_store_id'],$staff_list_59)) {
                $this->echo('[59]Hub Small Size---员工id：'.$value['staff_info_id']);
                $node_department_id = 59;
            }
            $r = Staff::updateItems($value['staff_info_id'], ['node_department_id' => $node_department_id], -1);
            if($r === true) {
                $this->echo('---Update OK--员工id：'.$value['staff_info_id']);
            } else {
                $this->echo('---Update error--员工id：'.$value['staff_info_id'],'错误信息：'.json_encode($r));
            }
        }

        $this->echo('end');
    }

    public function actionUpdateCarType() {
        $staff_info_ids = [16934,17437,17491,17581,17755,19841,19910,20091,20093,20096,20439,20779,21526,21551,21568,21773,21993,22126,22251,22265,22352,22526,22729,22848,22924,22947,23034,23149,23359,23368,23396,23705,23900,24255,24364,24367,24386,24818,24977,25305,25337,25349,25414,25415,25650,25666,25741,26437,26742,27140,27157,27191,27226,27319,27604,27990,28112,28274,28279,28349,28350,28351,28467,28696,28767,28912,29474,29489,29627,30115,30390,30520,30526,30551,30689,30777,30927,31036,31215,31700,31992,32347,32458,32645,33060,33071,33163,33231,33656,33709,33933,34107,34264,34420,34485,34883,35040,35198,35653,35690,35698,35716,36650,36716,36760,36907,36948,36949,37051,37126,37458,37575,37722,38046,38191,38205,38276,38297,38375,38406,38550,38627,38708,39020,39038,39238,39287,39372,39379,39512,39653,39759,40165,40288,40366,40711,40838,40900,41015,41016,41163,41206,41230,41304,41337,41481,41613,41626,41827,41956,42015,42188,42496,42604,42616,42966,43047,43120,43130,43177,43181,43327,43343,43495,43527,43566,43567,43602,43811,43851,44095,44278,44311,44322,44439,44522,44718,44790,44872,44983,44991,45108,45280,45417,45496,45501,45583,45675,45697,45733,45817,45928,45951,46093,46106,46444,46471,46473,46480,46493,46494,46575,46764,46849,46907,46969,46994,47279,47458,47701,47729,47916,48566,48623,48657,48727,48733,48759,49006,49050,49172,49177,49196,49275,49309,49329,49479,49484,50787,50831,50900,51112,51136,51141,51174,51306,51351,51465,51826,51922,51925,52001,52164,52381,52460,52464,52781,52857,52928,52977,53060,53079,53117,53350,53364,53383,53453,53516,53609,53956,53988,54235,54297,54316,54377,54410,54539,54615,54814,54902,55089,55210,55239,55251,55410,55538,55553,55628,55771,55953,56107,56298,57232,57384,57386,57778,57986,58232,59610,59659];
        //$staff_info_ids = [23111,23114,22381]; //测试工号
        $attributes = ['car_type' => 'Van'];
        foreach ($staff_info_ids as $value) {
            $r = Staff::updateItems($value,$attributes);
            $this->echo('工号：'.$value.'----结果：'.json_encode($r));
        }
        $this->echo('end');
    }

    //导入工号企业号码
    public function actionImportCompanyMobile_brk() {
        $this->echo('begin');
        $this->echo('导入企业号码');
        $config = ['path' => '/mnt/www/hr/commands/data'];
        $excel = new \Vtiful\Kernel\Excel($config);

        // 读取文件
        $excel_data = $excel->openFile('import-company-mobile-new.xlsx')
            ->openSheet()
            ->setSkipRows(1)
            ->setType([\Vtiful\Kernel\Excel::TYPE_INT, \Vtiful\Kernel\Excel::TYPE_STRING,])
            ->getSheetData();

        $import_data = [];
        foreach ($excel_data as $key => $value) {
            $staff_info = BaseStaffInfo::find()->where(['staff_info_id' => $value[0]])->asArray()->one();
            if(!empty($staff_info)) {
                /*
                if(empty($staff_info['mobile_company'])) {
                    //更新
                    $r = Staff::updateItems($value[0],['mobile_company' => $value[1]]);
                    $this->echo($value[0].'----更新完成,结果:'.json_encode($r));
                    $import_data[] = [$value[0], $value[1], $staff_info['mobile_company'], json_encode($r)];
                } else {
                    $this->echo($value[0].'-----'.$value[1].'-----'.$staff_info['mobile_company']);
                    $import_data[] = [$value[0], $value[1], $staff_info['mobile_company'], '该工号已有使用企业号码'];
                }
                */

                //更新
                $r = Staff::updateItems($value[0],['mobile_company' => $value[1]]);
                $this->echo($value[0].'----更新完成,结果:'.json_encode($r));
                $import_data[] = [$value[0], $value[1], $staff_info['mobile_company'], json_encode($r)];
            } else {
                $this->echo($value[0].'---未找到工号');
                $import_data[] = [$value[0], $value[1], '', '未找到工号'];
            }
        }

        //此处会自动创建一个工作表
        $header = ['staff_info_id','excel_mobile_company','db_mobile_company','result'];
        $fileObject = $excel->fileName('import-company-mobile-result-'.time().'.xlsx');
        $fileObject->header($header)->data($import_data);
        // 最后的最后，输出文件
        $f_path = $fileObject->output();
        $this->echo('文件路径:'.$f_path);
        $this->echo('导入企业号码完成');

        $this->echo('清空企业号码');
        $staff_info_ids_1 = [17574,16987,17306,30740,37151,21157,39876,60460,26425,54344,44246];
        foreach ($staff_info_ids_1 as $k =>$v) {
            $staff_info = BaseStaffInfo::find()->where(['staff_info_id' => $v])->asArray()->one();
            if(!empty($staff_info)) {
                $r = Staff::updateItems($v,['mobile_company' => '']);
                $this->echo($v.'----更新完成,结果:'.json_encode($r));
            } else {
                $this->echo($v.'---未找到工号');
            }
        }
        $this->echo('清空企业号码完成');
        $this->echo('end');
    }

    //导入工号企业号码
    public function actionImportCompanyMobile() {
        $this->echo('begin');
        $config = ['path' => '/mnt/www/hr/commands/data'];
        $excel = new \Vtiful\Kernel\Excel($config);

        // 读取文件
        $excel_data = $excel->openFile('import-company-mobile-new.xlsx')
            ->openSheet()
            ->setSkipRows(1)
            ->setType([\Vtiful\Kernel\Excel::TYPE_INT, \Vtiful\Kernel\Excel::TYPE_STRING,])
            ->getSheetData();

        $import_data = [];

        $this->echo('清理');
        foreach ($excel_data as $key => $value) {
            $staff_info = BaseStaffInfo::find()->where(['mobile_company' => $value[1]])->andWhere(['state' => 1])->asArray()->one();
            if(!empty($staff_info)) {
                $r = Staff::updateItems($staff_info['staff_info_id'], ['mobile_company' => '']);
                $this->echo('更新完成,结果:'.json_encode($r).'-----更新工号:'.$staff_info['staff_info_id'].'---name:'.$staff_info['name'].'---号码:'.$value[1]);
            } else {
                $this->echo($value[1].'---未找到使用工号');
            }
        }
        $this->echo('清理结束');
        $this->echo('导入企业号码');
        foreach ($excel_data as $key => $value) {
            $staff_info = BaseStaffInfo::find()->where(['staff_info_id' => $value[0]])->asArray()->one();
            if(!empty($staff_info)) {
                //更新
                $r = Staff::updateItems($value[0],['mobile_company' => $value[1]]);
                $this->echo($value[0].'----更新完成,结果:'.json_encode($r));
                $import_data[] = [$value[0], $value[1], $staff_info['mobile_company'], json_encode($r)];
            } else {
                $this->echo($value[0].'---未找到工号');
                $import_data[] = [$value[0], $value[1], '', '未找到工号'];
            }
        }

        //此处会自动创建一个工作表
        $header = ['staff_info_id','excel_mobile_company','db_mobile_company','result'];
        $fileObject = $excel->fileName('import-company-mobile-result-'.time().'.xlsx');
        $fileObject->header($header)->data($import_data);
        // 最后的最后，输出文件
        $f_path = $fileObject->output();
        $this->echo('文件路径:'.$f_path);
        $this->echo('导入企业号码完成');

        $this->echo('end');
    }

    /**
     * 获得当前系统的国家对应语言，离职邮件内容里的邮件内容是菲律宾的邮箱
     * @return string
     */
    public function getDefaultLang(){
        $country_code = strtolower(env('country','th'));
        if ($country_code == 'vn') {
            return 'vi'; // 越南
        }

        if ($country_code == 'id') {
            return 'id'; // 越南
        }

        if($country_code == 'th'){
            return 'th';
        }else{
            return 'en';
        }
    }


    /**
     * 测试发送邮件内容
     */
    public function actionTestEmail(){

        $staff_info_arr[] = [
            'staff_info_id' => 22568,
            'staff_name'    => '王洪伟',
            'leave_date'    => '2021-05-26',
            'stop_duty_reason' => 4,
            'manager_id'    => 22620 ,
            'manager_email' => '<EMAIL>',
            'store_name'    => 'header office',
            'department_name' => '部门',
        ];


        $staff_info_arr[] = [
            'staff_info_id' => 22568,
            'staff_name'    => '王洪伟',
            'leave_date'    => '2021-05-26',
            'stop_duty_reason' => 5,
            'manager_id'    => 22620 ,
            'manager_email' => '<EMAIL>',
            'store_name'    => 'header office',
            'department_name' => '部门',
        ];


        $staff_info_arr[] = [
            'staff_info_id' => 22568,
            'staff_name'    => '王洪伟',
            'leave_date'    => '2021-05-26',
            'stop_duty_reason' => 6,
            'manager_id'    => 22620 ,
            'manager_email' => '<EMAIL>',
            'store_name'    => 'header office',
            'department_name' => '部门',
        ];
        $this->sendMailtoHr($staff_info_arr);
        $this->sendMailtoManager($staff_info_arr);
    }

    public function actionCreateStaffTemp($n=0) {
        if($n == 1) {
            $staff_json = '[{"name":"Devi Anita Sari","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"34","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-03-01","mobile":"8128014801","identity":"332205540797002","sex":2,"week_working_day":6},{"name":"Fitriana","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"962","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-03-01","mobile":"8788071484","identity":"3175065903991002","sex":2,"week_working_day":6},{"name":"Teresa Emanuela Puteri","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"962","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-06-14","mobile":"8211095182","identity":"3276015901900007","sex":2,"week_working_day":6},{"name":"Shebika Aslamia","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"962","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-07-22","mobile":"8129778182","identity":"3175044503980001","sex":2,"week_working_day":6},{"name":"Hanny Verryna","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"418","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-05-24","mobile":"8951962423","identity":"1801064106950000","sex":2,"week_working_day":6},{"name":"Desi Krisdianti","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1191","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-08-12","mobile":"8138104691","identity":"3322065912940003","sex":2,"week_working_day":6},{"name":"Dita Dwi Nuraini","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-06-07","mobile":"8578083149","identity":"3175035703980009","sex":2,"week_working_day":6},{"name":"Lusi Septiani","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-06-07","mobile":"8581416216","identity":"1801156509970002","sex":2,"week_working_day":6},{"name":"Kinanti Bunga Lestari","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-06-09","mobile":"8777528321","identity":"3275074604000017","sex":2,"week_working_day":6},{"name":"Alvrodo Reinhard","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-06-10","mobile":"8577861894","identity":"3275050801910010","sex":1,"week_working_day":6},{"name":"Maulidya Winny","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-07-01","mobile":"8770295455","identity":"3275085606000010","sex":2,"week_working_day":6},{"name":"Rizki Sudirman F","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-07-01","mobile":"8223383717","identity":"3201271306940007","sex":1,"week_working_day":6},{"name":"Ony Agnesatia","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-07-01","mobile":"8968200190","identity":"3175066908980003","sex":2,"week_working_day":6},{"name":"Apriliana Nuzuly","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-09-01","mobile":"8569209083","identity":"3603286204890006","sex":2,"week_working_day":6},{"name":"Nurul Asma","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-09-13","mobile":"8787489084","identity":"3329126001000002","sex":2,"week_working_day":6},{"name":"Shania Easter","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-09-14","mobile":"8138377090","identity":"3173045104980006","sex":2,"week_working_day":6},{"name":"Oscar Louis Reynardo Soedharmadji","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-09-13","mobile":"8510172776","identity":"3172020807010003","sex":1,"week_working_day":5},{"name":"Stefania Moda","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-09-20","mobile":"8532250698","identity":"5315056612970002","sex":2,"week_working_day":6},{"name":"Ummy Kalsum Aljamil","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-09-20","mobile":"8129017560","identity":"3174024312940002","sex":2,"week_working_day":6},{"name":"Dini Rahayu","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1188","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-09-20","mobile":"8382189780","identity":"3217074210950006","sex":2,"week_working_day":6},{"name":"Fauzanil Aulia","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1193","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-07-12","mobile":"8111693039","identity":"3173052609890011","sex":1,"week_working_day":6},{"name":"Eriska Oktaviana","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1193","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-08-30","mobile":"8138224278","identity":"1802084910000002","sex":2,"week_working_day":6},{"name":"Kevin","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"1193","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-09-01","mobile":"8129599727","identity":"3173082606940002","sex":1,"week_working_day":6},{"name":"Andi Maulana","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"158","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-07-05","mobile":"8789441146","identity":"3211172512850011","sex":1,"week_working_day":6},{"name":"Nisa Sofiana","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"158","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-07-12","mobile":"8190717435","identity":"3174016008951003","sex":2,"week_working_day":6},{"name":"Awaludin Ma\'ruf","hire_type":1,"formal":1,"working_country":99,"nationality":99,"sys_store_id":"-1","manager":"82529","state":1,"sys_department_id":"441","node_department_id":441,"job_title":"158","email":"<EMAIL>","personal_email":"<EMAIL>","hire_date":"2021-08-30","mobile":"8158536777","identity":"3604221812950001","sex":1,"week_working_day":6}]';
            $staff_arr = json_decode($staff_json, true);

            foreach ($staff_arr as $key => $value) {
                $hire_date = $value['hire_date'];
                $value['hire_date'] = date('Y-m-d');
                $staff_model = Staff::combination($value);//格式化
                $res = Staff::save($staff_model,'-1');
                $this->echo('结果：' . json_encode($res). '---工号：'. $staff_model->staff_info_id . '入职日期：' . $hire_date);
            }
            $this->echo('end');
        } else {
            $this->echo('n = ' .$n);
        }

    }


    private function getStaffManager($sys_store_id)
    {

        $store = SysStore::find()->where(['id' => $sys_store_id])->one();
        if($store){
            if($store->manager_id){
                return $store->manager_id;
            }
            if($store->manage_piece){
                $piece = SysManagePiece::find()->where(['id' => $store->manage_piece])->one();
                if($piece && $piece->manager_id){
                    return  $piece->manager_id;
                }else{
                    throw new Exception('片区负责人不存在');

                }
            }else{
                throw new Exception('网点不存在归属片区');

            }

        }
        throw new Exception('网点不存在');
    }


    public function actionTransfer($super_job_transfer = 1,$super_job_transfer_not_jump = 1) {

//        $staffs = [
//            ['staff_info_id'=>25725,'department_id'=>4,'sys_store_id'=>'TH68040601','job_title'=>'110','position_category'=>[1],'direct_manager'=>''],
//            ['staff_info_id'=>25730,'department_id'=>4,'sys_store_id'=>'TH68040601','job_title'=>'110','position_category'=>[1],'direct_manager'=>''],
//        ];
//

        echo 'start';
        $staffs = TmpStaffJobTransfer::find()->where(['status'=>0])->all();

        foreach ($staffs as $staff) {
            $attr = ['staff_info_id'=>$staff->staff_info_id,'department_id'=>$staff->node_department_id,'sys_store_id'=>$staff->sys_store_id,'job_title'=>$staff->job_title,'direct_manager'=>$staff->direct_manager];
            //使用超管转岗
            $attr['super_job_transfer'] = $super_job_transfer;
            //todo 开启后会输出全部拦截信息，关闭后只会验证未回公款
            $attr['super_job_transfer_not_jump'] = $super_job_transfer_not_jump;
            $staffInfo = Staff::view($attr['staff_info_id']);

            try {
                if(empty($staffInfo)){
                    throw new Exception('员工不存在');
                }

                $staff->num ++;
                //上级为空，按照规则找
                if(empty($attr['direct_manager'])){
                    $attr['direct_manager'] = $this->getStaffManager($attr['sys_store_id']);
                }
                //角色为空 用原来的
                if(empty($staff->position_category)){
                    $attr['position_category'] = $staffInfo['position_category'];
                }else{
                    $attr['position_category'] = explode(',',$staff->position_category);
                }
                //职位为空， 用原来的
                if(empty($attr['job_title'])){
                    $attr['job_title'] = $staffInfo['job_title'];
                }

                $staff_info_id = $attr['staff_info_id'];

                $result = Staff::staffChangeJob($staff_info_id, $attr, -1);
                $err = '';
                if(isset($result['code']) && $result['code'] == 1){
                    $staff->status = 1;
                    $staff->remark = 'success';
                }else{
                    foreach ($result['msg'] as $item) {
                        if(is_array($item)){
                            foreach ($item as $t) {
                                $err .= '['.$this->lang->get($t,'','zh-CN') . ']';
                            }
                        }else{
                            $err .= '['.$this->lang->get($item,'','zh-CN').']';
                        }
                    }
                    $staff->status = 0;
                    $staff->remark = $staff->remark . $err;
                }
            }catch (\Exception $e){
                $staff->status = 0;
                $staff->remark = $e->getMessage();
            }
            $staff->save();
        }
        echo 'success';
    }

    public function actionUpdateStaffRole($staff_info_id) {
        //19953,55883
        $this->echo('begin');
        $staff_info = Staff::view($staff_info_id);
        $staff_info['position_category'] = [98];
        $model = Staff::combination($staff_info);
        // 发送消息
        $msgBody = $model->getAttributes();
        $msgBody['operator_id'] = '-1';
        $msgBody['staff_car_type'] = $model->newCarType ?? null;
        $msgBody['pay_type'] = $model->newPayType ?? null;
        $msgBody['manage_area_name'] = $model->manageAreaName;
        $msgBody['profile_object_key'] = $model->profileObjectKey;
        $msgBody['position_category'] = $model->positionCategory;
        $msgBody['is_sub_staff'] = $model->is_sub_staff ?? 0;
        $msgBody['master_staff'] = $model->newMasterStaff ?? 0;
        $msgBody['reset_password'] = false;//是否需要重置密码
        $msgBody['manager'] = $model->directManager;
        $msgBody['bank_no_name'] = $model->bankNoName ?? '';
        if($model->formal == 0) {
            $msgBody['outsourcing_category'] = $model->outsourcing_type == 'individual' ? 1 : 2;//外协员工类型
        } else {
            $msgBody['outsourcing_category'] = 0;//非外协传0
        }

        $this->echo($staff_info);
        if (!StaffSyncService::getInstance()->saveStaffInfo($msgBody, $model->staff_info_id ? true : false)) {
            $this->echo('发送失败');
        }
        $this->echo('发送完成');
    }

    public function actionFixResignAssetsStaff()
    {
        $str_my = '';

        $str_th = '';

        $str_ph = '';
        $str_la = '';
        $str_id = '';
        $str_json = '';
        switch (YII_COUNTRY) {
            case 'TH':
                $str_json = $str_th;
                break;
            case 'PH':
                $str_json = $str_ph;
                break;
            case 'MY':
                $str_json = $str_my;
                break;
            case 'LA':
                $str_json = $str_la;
                break;
            case 'ID':
                $str_json = $str_id;
                break;
        }

        echo 'begin' . PHP_EOL;

        if (!empty($str_json)) {
            $list = json_decode($str_json, true);
            foreach ($list as $key => $value) {
                $params                     = $value['params'];
                $event_params['event_type'] = 'staff_resign';
                $event_params['params']     = $params;
                echo $params['staff_info_id'] . PHP_EOL;
                $result = StaffService::getInstance()->syncResignAssetsStaff($event_params);
                echo json_encode($result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            }
        }
        echo 'end' . PHP_EOL;
    }

    //临时给指定网点创建加盟商账号
    public function actionCreateFranchiseeStaff($url = '')
    {
        $this->echo('begin');
        //$url       = 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1686535321-47798f1689e14a4ca3dfb30eb0123708.xlsx';
        if (empty($url)) {
            echo '未指定处理文件' . PHP_EOL;
            exit();
        }

        $fp        = '/mnt/www/web/excel/';
        $file_name = 'staff_' . date('YmdHis') . '.xlsx';
        //打开文件
        $fp = @fopen($fp . $file_name, 'w+');
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_FILE, $fp);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_exec($ch);
        curl_close($ch);
        //关闭文件
        fclose($fp);
        $this->echo('download ok');

        $config = ['path' => '/mnt/www/web/excel'];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 读取文件
        //姓名 手机号 网点id
        $excel_data = $excel->openFile($file_name)
            ->openSheet()
            ->setSkipRows(1)
            ->getSheetData();

        $data_list = [];
        foreach ($excel_data as $key => $value) {
            echo '姓名:' . $value[0] . PHP_EOL;
            $form = [
                'position_category' => [1],
                'state'             => BaseStaffInfo::STATE_ON_JOB,
                'hire_date'         => gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600),
                //'mobile'            => (string)$value[1], //手机号
                'mobile'            => '0000000000', //手机号,不用Excel表里真实手机号，真实手机号会给发送账号密码短信
                'sys_store_id'      => trim($value[2]),         //网点id
                'staff_car_type'    => 'Bike',
                'formal'            => BaseStaffInfo::FORMAL_FRANCHISEE,
                'name'              => trim($value[0]), //姓名
            ];

            $model = Staff::combination($form);//检查表单元素
            $r     = Staff::save($model, -1);
            if ($r !== true) {
                $this->echo('创建失败', json_encode($r, JSON_UNESCAPED_UNICODE));
                array_push($value, '', json_encode($r, JSON_UNESCAPED_UNICODE));
            } else {
                $this->echo('创建成功', $model->staff_info_id);
                array_push($value, $model->staff_info_id, 'success');
            }

            $data_list[] = $value;
        }

        $result_excel_file      = "staff_fail_" . gmdate('YmdHis') . ".csv";
        $path                   = Yii::$app->csv->filePut_v2(array_merge([
            [
                'staff_name',
                'mobile',
                'store_id',
                'staff_info_id',
                'result',
            ],
        ], $data_list), $result_excel_file);
        $result_excel_file_path = $path['object_url'] ?? '';
        $this->echo('创建结果', $result_excel_file_path);
        $this->echo('end');
    }

    //修正员工sys_department_id
    public function actionUpdateSysDepartmentId() {
        $staff_list = BaseStaffInfo::find()
            ->where('sys_department_id = node_department_id')
            ->andWhere(['formal' => [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE]])
            ->andWhere(['state' => [BaseStaffInfo::STATE_ON_JOB, BaseStaffInfo::STATE_SUSPENSION]])
            ->asArray()
            ->all();
        $this->echo('begin_更新员工sys_department_id');
        foreach ($staff_list as $key => $value) {
            $sys_department_id = (string)SysGroupDeptV2::SearchSysDeptId($value['node_department_id']);
            if(!empty($sys_department_id) && $sys_department_id != $value['sys_department_id']) {
                //员工表sys_department_id和匹配到的不一致，进行变更
                $staff = Staff::view($value['staff_info_id']);
                $staff_model = Staff::combination($staff);
                $result = Staff::save($staff_model, '-1');
                if($result === true) {
                    $this->echo($value['staff_info_id'], '更新成功', '原sys_department_id', $value['sys_department_id'], '更新后sys_department_id', $staff_model->sys_department_id);
                } else {
                    $this->echo($value['staff_info_id'], '更新失败', json_encode($result, JSON_UNESCAPED_UNICODE),'原sys_department_id', $value['sys_department_id'], '更新后sys_department_id', $staff_model->sys_department_id);
                }
            }
        }
        $this->echo('end');
    }

    public function actionFixStaffInfo() {

        $this->echo('pushRedisSyncResignAssetsStaff begin');

        $str = '[{"event_type":"change_manager","params":{"staff_info_id":127663,"before_manager_id":"119204","after_manager_id":"119484","operation_staff_id":-1}},
{"event_type":"staff_resign","params":{"staff_info_id":123552,"source":5,"operation_staff_id":-1,"staff_state":"2","wait_leave_state":0,"leave_date":"2023-06-28 00:00:00","last_work_date":"2023-06-27"}},
{"event_type":"staff_resign","params":{"staff_info_id":131305,"source":5,"operation_staff_id":-1,"staff_state":"2","wait_leave_state":0,"leave_date":"2023-06-26 00:00:00","last_work_date":"2023-06-25"}},
{"event_type":"staff_resign","params":{"staff_info_id":136996,"source":5,"operation_staff_id":-1,"staff_state":"2","wait_leave_state":0,"leave_date":"2023-06-28 00:00:00","last_work_date":"2023-06-27"}},
{"event_type":"staff_resign","params":{"staff_info_id":143612,"source":5,"operation_staff_id":-1,"staff_state":"2","wait_leave_state":0,"leave_date":"2023-06-26 00:00:00","last_work_date":"2023-06-25"}},
{"event_type":"staff_resign","params":{"staff_info_id":144920,"source":5,"operation_staff_id":-1,"staff_state":"2","wait_leave_state":0,"leave_date":"2023-06-28 00:00:00","last_work_date":"2023-06-27"}},
{"event_type":"staff_resign","params":{"staff_info_id":145140,"source":5,"operation_staff_id":-1,"staff_state":"2","wait_leave_state":0,"leave_date":"2023-06-28 00:00:00","last_work_date":"2023-06-27"}},
{"event_type":"change_manager","params":{"staff_info_id":155490,"before_manager_id":119700,"after_manager_id":119951,"operation_staff_id":10000}},
{"event_type":"staff_resign","params":{"staff_info_id":145140,"source":5,"operation_staff_id":-1,"staff_state":"2","wait_leave_state":0,"leave_date":"2023-06-28 00:00:00","last_work_date":"2023-06-27"}}]';

        $str_arr = json_decode($str, true);

        foreach ($str_arr as $key => $value) {
            //$r = StaffService::getInstance()->pushRedisSyncResignAssetsStaff($value);
            //$this->echo($value['params']['staff_info_id'], '结果', $r);
        }

        $this->echo('pushRedisSyncResignAssetsStaff end');


        $this->echo('pushUpdateStaffSupport begin');
        $str_staff_support = '{"staff_info_id":155490,"store_id":"PH27040600"}';
        $str_staff_support_arr = json_decode($str_staff_support, true);
        //$support_result = StaffSupportService::getInstance()->pushUpdateStaffSupport($str_staff_support_arr);
        //$this->echo('结果', $support_result);
        $this->echo('pushUpdateStaffSupport end');


        //$add_black_list_result = Staff::addStaffBlacklist(145140, -1);
        //$this->echo('加入黑名单,结果', json_encode($add_black_list_result));
    }

    //检查并更新sys_department_id
    public function actionCheckStaffDepartment()
    {
        $this->echo('begin');
        $department_list = SysDepartment::find()->where(['deleted' => SysDepartment::DELETE_0])->asArray()->all();
        foreach ($department_list as $key => $value) {
            $sys_department_id = (string)SysGroupDeptV2::SearchSysDeptId($value['id']);
            $staff_list = BaseStaffInfo::find()
                ->where(['formal' => [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE]])
                ->andWhere(['state' => [BaseStaffInfo::STATE_ON_JOB, BaseStaffInfo::STATE_SUSPENSION]])
                ->andWhere(['node_department_id' => $value['id']])
                ->andWhere(['!=', 'sys_department_id', $sys_department_id])
                ->asArray()
                ->all();

            if (!empty($staff_list)) {
                $this->echo('new_sys_department_id', $sys_department_id, 'node_department_id', $value['id']);
                foreach ($staff_list as $staff) {
                    try {
//                        $staff_view = Staff::view($staff['staff_info_id']);
//                        $staff_view['sys_department_id'] = 0;
//                        $staff_model = Staff::combination($staff_view);
//                        $result = Staff::save($staff_model, '-1');
                        Staff::$update_items_is_save_operate_log = false;
                        $result = Staff::updateItems($staff['staff_info_id'],['sys_department_id' => $sys_department_id]);
                        if ($result !== true) {
                            $this->echo($staff['staff_info_id'], '更新失败', $staff['node_department_id'], '原sys_department_id:' . $staff['sys_department_id'], 'result', json_encode($result, JSON_UNESCAPED_UNICODE));
                        } else {
                            $this->echo($staff['staff_info_id'], '更新成功', $staff['node_department_id'], '原sys_department_id:' . $staff['sys_department_id']);
                        }
                    } catch (Exception $e) {
                        $this->echo('staff_info_id:'. $staff['staff_info_id'], 'error:' . $e->getMessage(), 'line:' . $e->getLine(),'file:' . $e->getFile(), 'TraceAsString:' . $e->getTraceAsString());
                        Yii::$app->logger->write_log([
                            'message'       => $e->getMessage(),
                            'line'          => $e->getLine(),
                            'file'          => $e->getFile(),
                            'TraceAsString' => $e->getTraceAsString(),
                            'staff_info_id' => $staff['staff_info_id'],
                        ]);
                    }
                }
            } else {
                $this->echo('未找到一级部门不正确工号');
            }
        }
        $this->echo('end');
    }


    public function actionFixNode()
    {
        /**
         * SELECT
         * i.`staff_info_id` ,i.`node_department_id` ,dsr.`department_id` ,i.`hire_date`
         * FROM
         * `hr_staff_info` as i
         * INNER JOIN `sys_store` as st on i.`sys_store_id` = st.id
         * INNER JOIN `hr_organization_department_store_relation` as dsr on st.`id` = dsr.`store_id`
         * WHERE
         * dsr.`state` in (1,4)
         * and dsr.`level_state` = 1
         * and i.`node_department_id` != dsr.`department_id` and i.`formal` in (1,4) and i.`state` in (1,3)
         */
        $array = [
            [147358,5565],
            [141997,5565],
            [170305,5565],
            [128650,5565],
            [179391,5565],
            [179539,5565],
            [148077,5565],
            [149561,5565],
            [175752,5565],
            [183789,5566],
            [175097,5565],
            [122572,5565],
            [148374,5565],
            [135018,5564],
            [126200,5565],
            [165083,5566],
            [152287,5565],
            [183446,5566],
            [169123,5566],
            [149084,5565],
            [157819,5565],
            [176073,5565],
            [137744,5565],
            [157247,5565],
            [179455,5565],
            [119394,5566],
            [153156,5565],
            [174740,5565],
            [182311,5565],
            [154455,5565],
            [123218,5565],
            [132388,5565],
            [143391,5565],
            [144314,5565],
        ];

        foreach ($array as $item) {
            $result = Staff::updateItems($item[0], ['node_department_id' => $item[1]]);
            var_dump($result);
            Yii::$app->logger->write_log([
                'params'        => $item,
                'result' => $result,
            ], 'info');
        }

    }

    //菲律宾执行 更新未回款/未出勤离职员工 离职日期和停职日期保持一致
    public function actionUpdateStaffLeaveDate($is_update = 0)
    {
        $this->echo('begin');
        $staff_list = BaseStaffInfo::find()->where(['state' => BaseStaffInfo::STATE_RESIGN])
            ->andWhere(['formal' => [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE]])
            ->andWhere(['is_sub_staff' => 0])
            ->andWhere(['leave_source' => [3, 4]])
            ->andWhere(['>=', 'leave_date', '2023-09-16 00:00:00'])
            ->andWhere(['<=', 'leave_date', '2023-09-30 00:00:00'])
            ->andWhere('stop_duties_date != leave_date')
            ->asArray()->all();
        $this->echo('更新总数', count($staff_list));
        foreach ($staff_list as $staff) {
            $staff_info_id    = $staff['staff_info_id'];
            $stop_duties_date = $staff['stop_duties_date'];
            if ($is_update) {
                $res = Staff::updateItems($staff_info_id, ['leave_date' => $stop_duties_date]);
                if ($res === true) {
                    $this->echo($staff_info_id, '更新离职日期', '原离职日期', $staff['leave_date'], '修改后离职日期',
                        $stop_duties_date);
                } else {
                    $this->echo($staff_info_id, '更新离职日期失败，结果', json_encode($res));
                }
            } else {
                $this->echo($staff_info_id, '原离职日期', $staff['leave_date'], '更新后的离职日期', $stop_duties_date);
            }
        }
        $this->echo('end');
    }

    /**
     * 快递员车辆稽查 给子账号发消息
     * @return void
     */
    public function actionVehicleMessageSub()
    {
        $this->echo("actionVehicleInspectionToSubMessage start");
        $data = Yii::$app->redis->rpop(RedisListKeyEnums::VEHICLE_INSPECTION_TO_SUB_MESSAGE);
        if (!empty($data)) {
            sleep(3);
            $params = json_decode($data, true);
            $this->echo('actionVehicleInspectionToSubMessage', $data);
            $result = Yii::$app->jrpc->vehicleInspectionToSubMessage($params);
            $this->echo('actionVehicleInspectionToSubMessage result', $result);
            Yii::$app->logger->write_log('actionVehicleInspectionToSubMessage require:' . $data . 'response: ' . $result,
                'info');
        } else {
            $this->echo('actionVehicleInspectionToSubMessage data empty');
        }

        $this->echo("actionVehicleInspectionToSubMessage end");
    }

    /**
     * 异步导入批量修改员工信息
     * @return void
     * @throws Exception
     */
    public function actionAsyncImportUpdate()
    {
        $first = AsyncImportTaskService::getInstance()->getTask(AsyncImportTask::STAFF_INFO_BATCH_UPDATE);
        if (empty($first)) {
            return;
        }
        $tmpFilePath = sys_get_temp_dir() . '/' . basename($first->import_path);
        if (!file_put_contents($tmpFilePath, file_get_contents($first->import_path))) {
            Yii::$app->logger->write_log('异步导入批量修改员工信息的文件有问题请检查 ' . json_encode($first->toArray(),
                    JSON_UNESCAPED_UNICODE), 'error');
            return;
        }
        $params = json_decode($first->args_json, true);
        Yii::$app->lang->setLang($params['lang'] ?? 'en');
        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 读取文件
        $excel_data  = $excel->openFile($tmpFilePath)
            ->openSheet()
            ->getSheetData();
        array_walk_recursive($excel_data, function(&$value) {
            $value = trim($value);
        });
        $header      = $excel_data[0];
        $result_data = [];
        for ($i = 1; $i < count($excel_data); $i++) {
            $subArray = [];
            for ($j = 0; $j < count($header); $j++) {
                $subArray[$header[$j]] = $excel_data[$i][$j];
            }
            $result_data[(string)($i - 1)] = $subArray;
        }
        $header[]      = 'result';
        unset($excel_data);
        $result        = StaffService::getInstance()->batchUpd($result_data, $first->operator_id);
        $result        = array_column($result, 'result', 'staff_info_id');
        $success_count = 0;
        $error_count   = 0;
        foreach ($result_data as $k => $v) {
            if (!empty($result[$v['staff_info_id'] ?? ''])) {
                if (isset($result[$v['staff_info_id']]) && $result[$v['staff_info_id']] == 'ok') {
                    $result_data[$k]['result'] = 'success';
                    $success_count++;
                } else {
                    $error_count++;
                    if (!empty($result[$v['staff_info_id']]) && is_array($result[$v['staff_info_id']])){
                        if (!empty($result[$v['staff_info_id']][1])){
                            $result_data[$k]['result'] = ($result[$v['staff_info_id']][0] ?? '') . Yii::$app->lang->get($result[$v['staff_info_id']][1]);
                        }else{
                            $result_data[$k]['result'] = $result[$v['staff_info_id']][0] ?? 'error';
                        }
                    }else{
                        $result_data[$k]['result'] = $result[$v['staff_info_id']] ?? 'error';
                    }
                }
            } else {
                Yii::$app->logger->write_log('异步导入批量修改员工信息该工号未获取到执行结果请检查 ' . json_encode($first->toArray(),
                        JSON_UNESCAPED_UNICODE), 'error');
                $error_count++;
                $result_data[$k]['result'] = 'Please manually check, no results obtained';
            }
            $result_data[$k] = array_values($result_data[$k]);
        }

        $excelFilePath   = excelToFile($header, $result_data, $first->result_file_name);
        $ossObject       = 'hris/' . date('Ymd') . '/' . $first->result_file_name;
        $uploadOssResult = Yii::$app->FlashOss->uploadFile($ossObject, $excelFilePath);
        if (!$uploadOssResult) {
            Yii::$app->logger->write_log('异步导入批量修改员工信息，上传出问题了 ' . json_encode($first->toArray(),
                    JSON_UNESCAPED_UNICODE), 'error');
            return;
        }
        $first->success_number = $success_count;
        $first->fail_number    = $error_count;
        $first->status         = AsyncImportTask::STATE_EXECUTED;
        $first->result_path    = $ossObject;
        $first->update();
        Yii::$app->logger->write_log('异步导入批量修改员工信息成功' . json_encode($first->toArray(),
                JSON_UNESCAPED_UNICODE), 'info');
        return;
    }

    /**
     * 异步导入批量修改员工信息
     * @return void
     * @throws Exception
     */
    public function actionAsyncImportUpdateJob()
    {
        $first = AsyncImportTaskService::getInstance()->getTask(AsyncImportTask::STAFF_INFO_BATCH_UPDATE_JOB);
        if (empty($first)) {
            return;
        }
        $tmpFilePath = sys_get_temp_dir() . '/' . basename($first->import_path);
        if (!file_put_contents($tmpFilePath, file_get_contents($first->import_path))) {
            Yii::$app->logger->write_log('异步导入批量修改员工信息岗位的文件有问题请检查 ' . json_encode($first->toArray(),
                    JSON_UNESCAPED_UNICODE), 'error');
            return;
        }
        $params = json_decode($first->args_json, true);
        Yii::$app->lang->setLang($params['lang'] ?? 'en');
        $result = Staff::ImportStaffInfo($tmpFilePath, $first->operator_id,$first->result_file_name ?? '');
        $success_count = 0;
        $error_count   = 0;
        if ($result['code'] != 1) {
            Yii::$app->logger->write_log('异步导入批量修改员工信息岗位，上传出问题了 ' . json_encode($first->toArray(),
                    JSON_UNESCAPED_UNICODE), 'error');
            return;
        }
        foreach ($result['data']['data'] as $k=>$v){
            if ($v['result'] === 'success'){
                $success_count++;
            }else{
                $error_count++;
            }
        }
        $first->success_number = $success_count;
        $first->fail_number    = $error_count;
        $first->status         = AsyncImportTask::STATE_EXECUTED;
        $first->result_path    = $result['data']['excel_path'] ?? '';
        $first->update();
        Yii::$app->logger->write_log('异步导入批量修改员工岗位信息成功' . json_encode($first->toArray(),
                JSON_UNESCAPED_UNICODE), 'info');
        return;
    }

}
