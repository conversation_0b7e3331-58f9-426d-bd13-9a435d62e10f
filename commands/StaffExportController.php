<?php

/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\commands;

use app\libs\Enums\SuspendReasonEnums;
use app\models\backyard\HcmExcelTask;
use app\models\backyard\HrStaffAnnexInfo;
use app\models\backyard\OsStaffInfoExtend;
use app\models\backyard\StaffLeaveReason;
use app\models\Csv;
use app\models\fle\StaffInfo as StaffInfo_fle;
use app\models\fle\SysDepartment;
use app\models\fle\SysStore;
use app\models\manage\BaseStaffInfo;
use app\models\manage\ExportManage;
use app\models\manage\HrStaffInfoPosition;
use app\models\manage\StaffItems;
use app\models\manage\SysManagePiece;
use app\models\manage\SysManageRegion;
use app\models\manage\SysStoreTemp;
use app\models\manage\StaffInfo;
use app\modules\v1\business\Staff;
use app\modules\v1\business\StaffHold;
use app\modules\v1\business\StaffManager;
use app\modules\v1\business\SysArea;
use app\modules\v1\config\AreaManager;
use app\modules\v1\config\SysConfig;
use app\services\base\CompanyService;
use app\services\base\HrJobTitleService;
use app\services\base\HrStaffInsuranceBeneficiaryService;
use app\services\base\LeaveService;
use app\services\base\SalaryService;
use app\services\base\SysDepartmentService;
use app\services\base\SysStoreService;
use app\services\base\VehicleInfoService;
use app\services\base\SettingEnvService;
use Yii;
use yii\db\Query;
use app\models\manage\HrJobTitle;
use app\services\base\StaffService;
use app\services\base\HrStaffAnnexInfoService;
use \swoole_process;

/**
 * This command echoes the first argument that you have entered.
 *
 * This command is provided as an example for you to learn how to create console commands.
 *
 * <AUTHOR> Xue <<EMAIL>>
 * @since 2.0
 *
 */
class StaffExportController extends ConsoleController
{

    private function getDepartmentLevelNamesV2($department_id,$department_list) {
        $department_info = $department_list[$department_id] ?? '';
        if(empty($department_info)){
            return [];
        }
        $ancestry_arr = explode('/',$department_info['ancestry_v3'] ?? '');

        $dept = [
            0 => '-', //组织
            1 => '-', //公司名称
            2 => '-', //一级部门
            3 => '-', //二级部门
            4 => '-', //三级部门
            5 => '-', //四级部门
        ];
        //type扩展，原有1：公司；2：公司下的部门；3：是组织下的部门，新增扩展 4：boss级别（coo/cto/c..o）；5:gropu ceo 级别
        switch ($department_info['type']) {
            case 1:
                $dept = [
                    0 => isset($ancestry_arr[1]) ? $department_list[$ancestry_arr[1]]['name'] : '-',
                    1 => isset($ancestry_arr[2]) ? $department_list[$ancestry_arr[2]]['name'] : '-',
                    2 => '-',
                    3 => '-',
                    4 => '-',
                    5 => '-',
                ];
                break;
            case 2:
                $dept = [
                    0 => isset($ancestry_arr[1]) ? $department_list[$ancestry_arr[1]]['name'] : '-',
                    1 => isset($ancestry_arr[2]) ? $department_list[$ancestry_arr[2]]['name'] : '-',
                    2 => isset($ancestry_arr[3]) ? $department_list[$ancestry_arr[3]]['name'] : '-',
                    3 => isset($ancestry_arr[4]) ? $department_list[$ancestry_arr[4]]['name'] : '-',
                    4 => isset($ancestry_arr[5]) ? $department_list[$ancestry_arr[5]]['name'] : '-',
                    5 => isset($ancestry_arr[6]) ? $department_list[$ancestry_arr[6]]['name'] : '-',
                ];
                break;
            case 3:
                $department_ancestry_type = $department_list[$ancestry_arr[1]]['type'] ?? 3;
                if($department_info['ancestry'] == 999 || $department_ancestry_type == 3) {
                    $dept = [
                        0 => isset($ancestry_arr[0]) ?$department_list[$ancestry_arr[0]]['name'] : '-',
                        1 => '-',
                        2 => isset($ancestry_arr[1]) ? $department_list[$ancestry_arr[1]]['name'] : '-',
                        3 => isset($ancestry_arr[2]) ? $department_list[$ancestry_arr[2]]['name'] : '-',
                        4 => isset($ancestry_arr[3]) ? $department_list[$ancestry_arr[3]]['name'] : '-',
                        5 => isset($ancestry_arr[4]) ? $department_list[$ancestry_arr[4]]['name'] : '-',
                    ];
                } else {
                    $dept = [
                        0 => $department_list[$ancestry_arr[1]]['name'] ?? '-',
                        1 => '-',
                        2 => isset($ancestry_arr[2]) ? $department_list[$ancestry_arr[2]]['name'] : '-',
                        3 => isset($ancestry_arr[3]) ? $department_list[$ancestry_arr[3]]['name'] : '-',
                        4 => isset($ancestry_arr[4]) ? $department_list[$ancestry_arr[4]]['name'] : '-',
                        5 => isset($ancestry_arr[5]) ? $department_list[$ancestry_arr[5]]['name'] : '-',
                    ];
                }
                break;
            case 4:
                $dept = [
                    0 => isset($ancestry_arr[1]) ? $department_list[$ancestry_arr[1]]['name'] : '-',
                    1 => '-',
                    2 => '-',
                    3 => '-',
                    4 => '-',
                    5 => '-',
                ];
                break;
            case 5:
                $dept = [
                    0 => $department_list[$department_id]['name'] ?? '-',
                    2 => '-',
                    3 => '-',
                    4 => '-',
                    5 => '-',
                ];
                break;
        }
        return $dept;
    }

    private function getDepartmentList($lang = 'TH') {
        $sysDepartment = SysDepartment::find()
            ->select(
                ['id','name','ancestry','ancestry_v2','ancestry_v3','type','level','group_boss_id','manager_id','manager_name','company_id','company_name','deleted']
            )->where(['IN','deleted', SysDepartment::ALL_DELETED])->indexBy('id')->asArray()->all();
        foreach ($sysDepartment AS $key => &$val) {
            if ($val['deleted'] == SysDepartment::DELETE_1 && !empty($val['name'])) {
                $val['name'] .= $this->lang->get('deleted','',$lang);;
            }
        }
        return $sysDepartment;
    }

    public function actionExcelSarryStruct($args,$excel_task_id){
        $this->echo('actionExcelSarryStruct begin');
        try {
            $args_arr = json_decode(base64_decode($args),true);

            $lang = $args_arr['date']['lang'];
            //新版查询
            $whereDepartment = $args_arr['date']['department_id']??null;
            if(!empty($whereDepartment)) {
                $dept_detail = SysDepartment::find()
                    ->select(['id', 'name', 'ancestry', 'ancestry_v2','ancestry_v3', 'type', 'level', 'group_boss_id'])
                    ->where(['deleted' => 0])
                    ->andWhere(['id' => $whereDepartment])
                    ->asArray()
                    ->one();
                if(!empty($dept_detail)) {
                    $ancestry_v3 = empty($dept_detail['ancestry_v3']) ? $dept_detail['id'] : $dept_detail['ancestry_v3'];

                    $dept_list_query = SysDepartment::find()->select(['id', 'name', 'ancestry', 'ancestry_v2','ancestry_v3', 'type', 'level', 'group_boss_id'])
                        ->where(['deleted' => 0])
                        ->andWhere(['LIKE', 'ancestry_v3', $ancestry_v3.'/%', false])
                        ->orWhere(['id' => $whereDepartment]);
                    $dept_list = $dept_list_query->asArray()->indexBy('id')->all();
                    $whereDepartment = array_column($dept_list,'id');
                }
            }
            $param=$args_arr['date'];
            $name = null;
            if (!empty($param['name'])) {
                $name = trim($param['name']);
            }

            if (!empty($param['job_title'])) {
                $jobTitle = $param['job_title'];
            }
            $state = null;
            if (!empty($param['state'])) {
                $state = $param['state'];
            }
            $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');

            if( !empty($param['is_salary'])) {
                $query->where(['IN', 'hr_staff_info.formal', [1, 4]]);
            }
            //职级
            if(isset($param['job_title_grade'])&&is_numeric($param['job_title_grade'])) {
                $job_title_grade = $param['job_title_grade'];
            }

            if(!empty($state)) {
                if(in_array(999, $state)) {
                    $query->andFilterWhere(
                        [
                            'OR', ['hr_staff_info.state' => $state], ['hr_staff_info.wait_leave_state' => 1],
                        ]
                    );
                } else {
                    $query->andFilterWhere(['hr_staff_info.state' => $state])
                        ->andFilterWhere(['hr_staff_info.wait_leave_state' => 0]);
                }
            }
            $query->andFilterWhere(['hr_staff_info.node_department_id' => $whereDepartment ?? null])
                ->andFilterWhere(['hr_staff_info.job_title' => $jobTitle ?? null])
                ->andFilterWhere(['hr_staff_info.sys_store_id' => $store ?? null])
                ->andFilterWhere(['hr_staff_info.sys_store_id' => $stores ?? null])
                ->andFilterWhere(['hr_staff_info.staff_type' => $staffType ?? null])
                ->andFilterWhere(['hr_staff_info.job_title_grade_v2' => $job_title_grade ?? null])
                ->andFilterWhere(['OR',
                    ['LIKE', 'hr_staff_info.name', $name],
                    ['LIKE', 'hr_staff_info.staff_info_id', $name],
                    ['LIKE', 'hr_staff_info.emp_id', $name],
                    ['LIKE', 'hr_staff_info.name_en', $name],
                    ['LIKE', 'hr_staff_info.nick_name', $name],
                ]);

            // 角色查询
            if (!empty($param['position'])) {
                if (!is_array($param['position'])) {
                    $positions = explode(',', $param['position']);
                } else {
                    $positions = $param['position'];
                }
                // $positions =
                foreach ($positions as $key => $value) {
                    if ($key == 0) {
                        $roles[] = 'OR';
                    }
                    $roles[] = ['=', 'hr_staff_info_position.position_category', $value];
                }

                if ($roles ?? false) {
                    $query->innerJoin('hr_staff_info_position', 'hr_staff_info_position.staff_info_id=hr_staff_info.staff_info_id');
                    $query->andFilterWhere($roles);
                }
            }

            // 入职日期-开始
            if (!empty($param['hire_date_begin'])) {
                $begin = date('Y-m-d', strtotime($param['hire_date_begin']));
                $query->andFilterWhere(['>=', 'hr_staff_info.hire_date', $begin]);
            }
            // 入职日期-结束
            if (!empty($param['hire_date_end'])) {
                $begin = date('Y-m-d', strtotime($param['hire_date_end']));
                $query->andFilterWhere(['<=', 'hr_staff_info.hire_date', $begin]);
            }

            // x薪资列表
            if (!empty($param['is_salary'])) {
                foreach ($query->where as &$row) {
                    if (isset($row['hr_staff_info.sys_store_id'])) {
                        unset($row['hr_staff_info.sys_store_id']);
                    }
                }
                $query = (SalaryService::getInstance())->createHireTypeSelect($query,$param['hire_type']??[]);
                if (strtoupper(YII_COUNTRY) == 'MY') {
                    $query->select('hr_staff_info.staff_info_id,hr_staff_info.hire_type,
                hr_staff_salary.base_salary,
                hr_staff_salary.exp_allowance,
                hr_staff_salary.position_allowance,
                hr_staff_salary.car_rental,
                hr_staff_salary.gasoline_allowance,
                hr_staff_salary.notebook_rental,
                hr_staff_salary.food_allowance,
                hr_staff_salary.dangerous_area,
                hr_staff_salary.house_rental,
                hr_staff_salary.island_allowance,
                hr_staff_salary.firestarer_award,
                hr_staff_salary.performance_allowance,
                hr_staff_salary.other_non_taxable_allowance,
                hr_staff_salary.other_taxable_allowance,
                hr_staff_salary.other_taxable_allowance,
                hr_staff_salary.phone_subsidy,
                hr_staff_salary.mobile_allowance,
                hr_staff_salary.attendance_allowance,
                hr_staff_salary.fuel_allowance,
                hr_staff_salary.car_allowance,
                hr_staff_salary.vehicle_allowance,
                hr_staff_salary.gdl_allowance,
                hr_staff_salary.deminimis_benefits,
                hr_staff_salary.site_allowance,
                hr_staff_salary.internship_salary
                '
                    );
                } elseif (strtoupper(YII_COUNTRY) == 'VN') {

                    $query->select('hr_staff_info.staff_info_id,hr_staff_info.hire_type,
                hr_staff_salary.base_salary,
                hr_staff_salary.exp_allowance,
                hr_staff_salary.position_allowance,
                hr_staff_salary.car_rental,
                hr_staff_salary.gasoline_allowance,
                hr_staff_salary.notebook_rental,
                hr_staff_salary.food_allowance,
                hr_staff_salary.dangerous_area,
                hr_staff_salary.house_rental,
                hr_staff_salary.island_allowance,
                hr_staff_salary.firestarer_award,
                hr_staff_salary.performance_allowance,
                hr_staff_salary.other_non_taxable_allowance,
                hr_staff_salary.other_taxable_allowance,
                hr_staff_salary.other_taxable_allowance,
                hr_staff_salary.phone_subsidy,
                hr_staff_salary.mobile_allowance,
                hr_staff_salary.attendance_allowance,
                hr_staff_salary.fuel_allowance,
                hr_staff_salary.car_allowance,
                hr_staff_salary.vehicle_allowance,
                hr_staff_salary.transport_allowance,
                hr_staff_salary.performance_allowance,
                hr_staff_salary.gdl_allowance,
                hr_staff_salary.deminimis_benefits
                '
                    );
                }  elseif (strtoupper(YII_COUNTRY) == 'ID') {

                    $query->select('hr_staff_info.staff_info_id,hr_staff_info.hire_type,
                hr_staff_salary.base_salary,
                hr_staff_salary.welfare_allowance,
                hr_staff_salary.food_allowance,
                hr_staff_salary.attendance_allowance,
                hr_staff_salary.transport_allowance,
                hr_staff_salary.notebook_rental,
                hr_staff_salary.house_rental,
                hr_staff_salary.firestarer_award,
                hr_staff_salary.car_rental,
                hr_staff_salary.language_allowance,
                hr_staff_salary.camera_allowance,
                ');
                }else {

                    $query->select('hr_staff_info.staff_info_id,hr_staff_info.hire_type,hr_staff_info.contract_company_id,
                hr_staff_salary.base_salary,
                hr_staff_salary.exp_allowance,
                hr_staff_salary.position_allowance,
                hr_staff_salary.car_rental,
                hr_staff_salary.gasoline_allowance,
                hr_staff_salary.notebook_rental,
                hr_staff_salary.food_allowance,
                hr_staff_salary.dangerous_area,
                hr_staff_salary.house_rental,
                hr_staff_salary.island_allowance,
                hr_staff_salary.firestarer_award,
                hr_staff_salary.performance_allowance,
                hr_staff_salary.other_non_taxable_allowance,
                hr_staff_salary.other_taxable_allowance,
                hr_staff_salary.other_taxable_allowance,
                hr_staff_salary.phone_subsidy,
                hr_staff_salary.deminimis_benefits
                '
                    );
                }
                $query->andWhere(['hr_staff_info.is_sub_staff' => 0]);
                $isHeadOffice = $param['is_salay_headeroffice'];
                if ($isHeadOffice) {
                    $query->andWhere(['hr_staff_info.sys_store_id' => '-1']);
                } else {
                    $store = $param['store']?? '';
                    if (empty($store) || $store == -1) {
                        $query->andWhere(['<>', 'hr_staff_info.sys_store_id', '-1']);
                    } else {
                        $query->andWhere(['hr_staff_info.sys_store_id' => $store]);
                    }
                }
                $query->leftJoin('hr_staff_salary', 'hr_staff_salary.staff_info_id=hr_staff_info.staff_info_id');
            }

            $query->groupBy('hr_staff_info.staff_info_id');
            $origins = $query->indexBy('staff_info_id')->all(Yii::$app->get('r_backyard'));
            $header[] = $this->lang->get('staff_info_id','',$lang);
            $header[] = $this->lang->get('hire_type','',$lang);
            if(strtoupper(YII_COUNTRY)=='PH'){
                $header[] = $this->lang->get('base_salary','',$lang);
                $header[] = $this->lang->get('car_rental','',$lang);
                $header[] = $this->lang->get('gasoline_allowance','',$lang);
                $header[] = $this->lang->get('notebook_rental','',$lang);
                $header[] = $this->lang->get('house_rental','',$lang);

                $header[] = $this->lang->get('food_allowance','',$lang);
                $header[] = $this->lang->get('dangerous_area','',$lang);
                $header[] = $this->lang->get('island_allowance','',$lang);

                $header[] = $this->lang->get('performance_allowance','',$lang);
                $header[] = $this->lang->get('other_taxable_allowance','',$lang);
                $header[] = $this->lang->get('deminimis_benefits','',$lang);
                $header[] = $this->lang->get('other_non_taxable_allowance','',$lang);
                $header[] = $this->lang->get('phone_subsidy','',$lang);
                $header[] = $this->lang->get('firestarer_award','',$lang);

            } elseif (strtoupper(YII_COUNTRY) == 'MY') {
                $header[] = $this->lang->get('base_salary','',$lang);
                $header[] = $this->lang->get('notebook_rental','',$lang);
                $header[] = $this->lang->get('mobile_allowance','',$lang);
                $header[] = $this->lang->get('attendance_allowance','',$lang);
                $header[] = $this->lang->get('fuel_allowance','',$lang);
                $header[] = $this->lang->get('car_allowance','',$lang);
                $header[] = $this->lang->get('vehicle_allowance','',$lang);
                $header[] = $this->lang->get('gdl_allowance','',$lang);
                $header[] = $this->lang->get('house_rental','',$lang);
                $header[] = $this->lang->get('site_allowance','',$lang);
                $header[] = $this->lang->get('firestarer_award','',$lang);
                $header[] = $this->lang->get('internship_salary','',$lang); //实习生津贴

            } elseif (strtoupper(YII_COUNTRY) == 'VN') {
                $header[] = $this->lang->get('base_salary','',$lang);
                $header[] = $this->lang->get('performance_allowance','',$lang);
                $header[] = $this->lang->get('attendance_allowance','',$lang);
                $header[] = $this->lang->get('transport_allowance','',$lang);
                $header[] = $this->lang->get('phone_subsidy','',$lang);
                $header[] = $this->lang->get('food_allowance','',$lang);
                $header[] = $this->lang->get('notebook_rental','',$lang);
                $header[] = $this->lang->get('house_rental','',$lang);
                $header[] = $this->lang->get('firestarer_award','',$lang);

            } elseif (strtoupper(YII_COUNTRY) == 'ID') {
                $header[] = $this->lang->get('base_salary','',$lang);
                $header[] = $this->lang->get('welfare_allowance','',$lang);
                $header[] = $this->lang->get('food_allowance','',$lang);
                $header[] = $this->lang->get('attendance_allowance','',$lang);
                $header[] = $this->lang->get('transport_allowance','',$lang);
                $header[] = $this->lang->get('notebook_rental','',$lang);
                $header[] = $this->lang->get('house_rental','',$lang);
                $header[] = $this->lang->get('firestarer_award','',$lang);
                $header[] = $this->lang->get('car_rental','',$lang);
                $header[] = $this->lang->get('language_allowance','',$lang);
                $header[] = $this->lang->get('camera_allowance','',$lang);
            } elseif (strtoupper(YII_COUNTRY) == 'TH') {
                $header[] = $this->lang->get('payroll_company_name','',$lang);
                $header[] = $this->lang->get('base_salary','',$lang);
                $header[] = $this->lang->get('exp_allowance','',$lang);
                $header[] = $this->lang->get('position_allowance','',$lang);
                $header[] = $this->lang->get('car_rental','',$lang);
                $header[] = $this->lang->get('gasoline_allowance','',$lang);
                $header[] = $this->lang->get('notebook_rental','',$lang);
                $header[] = $this->lang->get('food_allowance','',$lang);
                $header[] = $this->lang->get('dangerous_area','',$lang);
                $header[] = $this->lang->get('house_rental','',$lang);
                $header[] = $this->lang->get('island_allowance','',$lang);
                $header[] = $this->lang->get('firestarer_award','',$lang);
            } else {
                $header[] = $this->lang->get('base_salary','',$lang);
                $header[] = $this->lang->get('exp_allowance','',$lang);
                $header[] = $this->lang->get('position_allowance','',$lang);
                $header[] = $this->lang->get('car_rental','',$lang);
                $header[] = $this->lang->get('gasoline_allowance','',$lang);
                $header[] = $this->lang->get('notebook_rental','',$lang);
                $header[] = $this->lang->get('food_allowance','',$lang);
                $header[] = $this->lang->get('dangerous_area','',$lang);
                $header[] = $this->lang->get('house_rental','',$lang);
                $header[] = $this->lang->get('island_allowance','',$lang);
                $header[] = $this->lang->get('firestarer_award','',$lang);
            }
            $contractCompanyMap = CompanyService::getInstance()->getContractCompanyMap();
            //$excel_data[] = $header;
            $excel_data=[];
            $row=array();
            foreach ($origins as $one){
                $row['staff_info_id'] = $one['staff_info_id'];
                $row['hire_type']     = $this->lang->get('hire_type_' . $one['hire_type'], '', $lang);
                if(strtoupper(YII_COUNTRY)=='PH'){
                    $row['base_salary']=$one['base_salary']/100??0;
                    $row['car_rental']=$one['car_rental']/100??0;
                    $row['gasoline_allowance']=$one['gasoline_allowance']/100??0;
                    $row['notebook_rental']=$one['notebook_rental']/100??0;
                    $row['house_rental']=$one['house_rental']/100??0;

                    $row['food_allowance']=$one['food_allowance']/100??0;
                    $row['dangerous_area']=$one['dangerous_area']/100??0;
                    $row['island_allowance']=$one['island_allowance']/100??0;

                    $row['performance_allowance']=$one['performance_allowance']/100??0;
                    $row['other_taxable_allowance']=$one['other_taxable_allowance']/100??0;
                    $row['deminimis_benefits']=$one['deminimis_benefits']/100??0;
                    $row['other_non_taxable_allowance']=$one['other_non_taxable_allowance']/100??0;
                    $row['phone_subsidy']=$one['phone_subsidy']/100??0;
                    $row['firestarer_award']=$one['firestarer_award']/100??0;
                    $excel_data[] = array_values($row);
                } elseif (strtoupper(YII_COUNTRY) == 'VN') {

                    $row['base_salary'] = $one['base_salary']/100??0;
                    $row['performance_allowance'] = $one['performance_allowance']/100??0;
                    $row['attendance_allowance'] = $one['attendance_allowance']/100??0;
                    $row['transport_allowance'] = $one['transport_allowance']/100??0;
                    $row['phone_subsidy'] = $one['phone_subsidy']/100??0;
                    $row['food_allowance'] = $one['food_allowance']/100??0;
                    $row['notebook_rental'] = $one['notebook_rental']/100??0;
                    $row['house_rental'] = $one['house_rental']/100??0;
                    $row['firestarer_award']=$one['firestarer_award']/100??0;
                    $excel_data[] = array_values($row);
                } elseif (strtoupper(YII_COUNTRY) == 'MY') {
                    $row['base_salary']=$one['base_salary']/100??0;
                    $row['notebook_rental']=$one['notebook_rental']/100??0;
                    $row['mobile_allowance']=$one['mobile_allowance']/100??0;
                    $row['attendance_allowance']=$one['attendance_allowance']/100??0;
                    $row['fuel_allowance']=$one['fuel_allowance']/100??0;
                    $row['car_allowance']=$one['car_allowance']/100??0;
                    $row['vehicle_allowance']=$one['vehicle_allowance']/100??0;
                    $row['gdl_allowance']=$one['gdl_allowance']/100??0;
                    $row['house_rental']=$one['house_rental']/100??0;
                    $row['site_allowance']=isset($one['site_allowance']) ? $one['site_allowance']/100 : 0;
                    $row['firestarer_award']=$one['firestarer_award']/100??0;
                    $row['internship_salary']=$one['internship_salary']/100??0;

                    $excel_data[] = array_values($row);
                }elseif (strtoupper(YII_COUNTRY) == 'ID') {
                    $columns = ['base_salary','welfare_allowance','food_allowance','attendance_allowance','transport_allowance',
                        'notebook_rental','house_rental','firestarer_award','car_rental','language_allowance','camera_allowance'];
                    foreach ($columns as $column) {
                        $row[$column] = (int) ($one[$column] ?? 0);
                    }
                    $excel_data[] = array_values($row);
                } elseif(strtoupper(YII_COUNTRY) == 'TH') {
                    $row['payroll_company_name']=$contractCompanyMap[$one['contract_company_id']] ?? '';
                    $row['base_salary']=$one['base_salary']/100??0;
                    $row['exp_allowance']=$one['exp_allowance']/100??0;
                    $row['position_allowance']=$one['position_allowance']/100??0;
                    $row['car_rental']=$one['car_rental']/100??0;
                    $row['gasoline_allowance']=$one['gasoline_allowance']/100??0;

                    $row['notebook_rental']=$one['notebook_rental']/100??0;
                    $row['food_allowance']=$one['food_allowance']/100??0;
                    $row['dangerous_area']=$one['dangerous_area']/100??0;
                    $row['house_rental']=$one['house_rental']/100??0;
                    $row['island_allowance']=$one['island_allowance']/100??0;
                    $row['firestarer_award']=$one['firestarer_award']/100??0;
                    $excel_data[] = array_values($row);
                } else {
                    $row['base_salary']=$one['base_salary']/100??0;
                    $row['exp_allowance']=$one['exp_allowance']/100??0;
                    $row['position_allowance']=$one['position_allowance']/100??0;
                    $row['car_rental']=$one['car_rental']/100??0;
                    $row['gasoline_allowance']=$one['gasoline_allowance']/100??0;

                    $row['notebook_rental']=$one['notebook_rental']/100??0;
                    $row['food_allowance']=$one['food_allowance']/100??0;
                    $row['dangerous_area']=$one['dangerous_area']/100??0;
                    $row['house_rental']=$one['house_rental']/100??0;
                    $row['island_allowance']=$one['island_allowance']/100??0;
                    $row['firestarer_award']=$one['firestarer_award']/100??0;
                    $excel_data[] = array_values($row);
                }
            }
            $hcmRes = $biRes = true ;

            if($hcmExcelTask = HcmExcelTask::find()->where(['id' => $excel_task_id])->one()){
                $excel_data = array_merge([$header],$excel_data) ;
                $path = Yii::$app->csv->filePutAndUploadOss($excel_data, $args_arr['file_name']);
                $hcmExcelTask->path= $path;
                $hcmExcelTask->status = 1;
                $hcmExcelTask->finish_at = gmdate('Y-m-d H:i:s',time()+TIME_ADD_HOUR*3600);
                $hcmRes = $hcmExcelTask->save();
            }

            return $hcmRes && $biRes;
        } catch (\Exception $e) {
            $this->echo('error:'.$e->getMessage().';行号:'.$e->getLine().'---'.$e->getMessage().'----'.$e->getTraceAsString());
            Yii::$app->logger->write_log('flash 员工下载任务，可能出现的问题' . $e->getMessage().';行号:'.$e->getLine().'---'.$e->getFile());
        }
        $this->echo('actionExcelSarryStruct end');
    }



    //excel 表头
    private function getExcelHeader($field,$lang) {
        $config_field = Yii::$app->sysconfig->export_field_all;
        $header = [];
        foreach ($field as $key => $value) {
            if($value == 12) {
                $header[] = $this->lang->get('c_level','',$lang);
                $header[] = $this->lang->get('company_name','',$lang);
                $header[] = $this->lang->get('dept_level_1','',$lang);
                $header[] = $this->lang->get('dept_level_2','',$lang);
                $header[] = $this->lang->get('dept_level_3','',$lang);
                $header[] = $this->lang->get('dept_level_4','',$lang);
            } elseif ($value == 100){  // 户口所在地地址(分项)
                if (YII_COUNTRY == 'MY') {
                    $header[] = $this->lang->get('register_detail_address', '', $lang);  // {户口所在地-详细地址}
                    $header[] = $this->lang->get('register_city', '', $lang);            // {户口所在地-城市}
                    $header[] = $this->lang->get('register_province_state', '', $lang);  // {户口所在地-州}
                    $header[] = $this->lang->get('register_postcodes', '', $lang);       // {户口所在地-邮编}
                } elseif (YII_COUNTRY == 'ID') {
                    $header[] = $this->lang->get('register_street_house_num', '', $lang);     // 户口所在地-街道
                    $header[] = $this->lang->get('register_rt', '', $lang);                   // 户口所在地-邻组
                    $header[] = $this->lang->get('register_rw', '', $lang);                   // 户口所在地-居委会
                    $header[] = $this->lang->get('register_village', '', $lang);              // 户口所在地-村庄
                    $header[] = $this->lang->get('register_district', '', $lang);             // 户口所在地-乡
                    $header[] = $this->lang->get('register_city', '', $lang);                 // 户口所在地-市
                    $header[] = $this->lang->get('register_province', '', $lang);             // 户口所在地-省
                    $header[] = $this->lang->get('register_postcodes', '', $lang);            // 户口所在地-邮编
                } else {
                    $header[] = $this->lang->get('register_house_num','',$lang);  // 户口所在地-门牌号
                    $header[] = $this->lang->get('register_village_num','',$lang);// 户口所在地-村号
                    $header[] = $this->lang->get('register_village','',$lang);    // 户口所在地-村庄
                    $header[] = $this->lang->get('register_alley','',$lang);      // 户口所在地-巷
                    $header[] = $this->lang->get('register_street','',$lang);     // 户口所在地-街道
                    $header[] = $this->lang->get('register_district','',$lang);   // 户口所在地-乡
                    $header[] = $this->lang->get('register_city','',$lang);       // 户口所在地-市
                    $header[] = $this->lang->get('register_province','',$lang);   // 户口所在地-省
                    $header[] = $this->lang->get('register_postcodes','',$lang);  // 户口所在地-邮编
                }
            } elseif ($value == 101) { // 居住所在地地址(分项)
                if (YII_COUNTRY == 'MY') {
                    $header[] = $this->lang->get('residence_detail_address','',$lang); // {居住所在地-详细地址}
                        $header[] = $this->lang->get('residence_city','',$lang);           // {居住所在地-城市}
                    $header[] = $this->lang->get('residence_province_state','',$lang);       // {居住所在地-州}
                    $header[] = $this->lang->get('residence_postcodes','',$lang);      // {居住所在地-邮编}
                } elseif (YII_COUNTRY == 'ID') {
                    $header[] = $this->lang->get('residence_street_house_num', '', $lang); // 居住所在地-街道
                    $header[] = $this->lang->get('residence_rt', '', $lang);               // 居住所在地-邻组
                    $header[] = $this->lang->get('residence_rw', '', $lang);               // 居住所在地-居委会
                    $header[] = $this->lang->get('residence_village', '', $lang);          // 居住所在地-村庄
                    $header[] = $this->lang->get('residence_district', '', $lang);         // 居住所在地-乡
                    $header[] = $this->lang->get('residence_city', '', $lang);             // 居住所在地-市
                    $header[] = $this->lang->get('residence_province', '', $lang);         // 居住所在地-省
                    $header[] = $this->lang->get('residence_postcodes', '', $lang);        // 居住所在地-邮编
                } else {
                    $header[] = $this->lang->get('residence_house_num','',$lang);  // 居住所在地-门牌号
                    $header[] = $this->lang->get('residence_village_num','',$lang);// 居住所在地-村号
                    $header[] = $this->lang->get('residence_village','',$lang);    // 居住所在地-村庄
                    $header[] = $this->lang->get('residence_alley','',$lang);      // 居住所在地-巷
                    $header[] = $this->lang->get('residence_street','',$lang);     // 居住所在地-街道
                    $header[] = $this->lang->get('residence_district','',$lang);   // 居住所在地-乡
                    $header[] = $this->lang->get('residence_city','',$lang);       // 居住所在地-市
                    $header[] = $this->lang->get('residence_province','',$lang);   // 居住所在地-省
                    $header[] = $this->lang->get('residence_postcodes','',$lang);  // 居住所在地-邮编
                }
            } elseif ($value == 91 && YII_COUNTRY == 'PH') {
                $header[] = $this->lang->get('tax_card_ph','',$lang);  // 税号
            } else {
                $header[] = $this->lang->get($config_field[$value],'',$lang);
            }
        }
        return $header;
    }
    

    /**
     * @param $row      array 需要格式化的数据 - 马来 户口所在地地址
     * @return string
     */
    private function formatMyPermanentAddress ($row) {
        $address = "";
        if (empty($row) || !is_array($row)) {
            return $address;
        }
        if (!empty($row['register_detail_address'])) {
            $address .= "{$row['register_detail_address']}";
        }
        if (!empty($row['register_city_name'])) {
            $address .= " {$row['register_city_name']}";
        }
        if (!empty($row['register_province_name'])) {
            $address .= " {$row['register_province_name']}";
        }
        if (!empty($row['register_postcodes'])) {   // 邮编
            $address .= " {$row['register_postcodes']}";
        }
        return trim($address);
    }

    /**
     * @param $row      array 需要格式化的数据 除 马来、菲律宾之外的所有数据 户口所在地地址
     * @param $country  string 对应的国家
     * @param $format   int 展示的格式
     * @return string
     */
    private function formatOtherPermanentAddress($row, $country = 'TH', $format = 1) {
        $address = '';
        if (empty($row) || !is_array($row)) {
            return $address;
        }

        if (in_array($country, ['MY'])) {
            return $address;
        }

        $formats = [1,2];
        if (!empty($row['register_house_num'])) {
            $prefix = in_array($format, $formats) ? "บ้านเลขที่" : "";
            $address .= "$prefix {$row['register_house_num']}";
        }
        if (!empty($row['register_village_num'])) {
            $prefix = in_array($format, $formats) ? "หมู่" : "";
            $address .= " $prefix {$row['register_village_num']}";
        }
        if (!empty($row['register_village'])) {
            $prefix = in_array($format, $formats) ? "หมู่บ้าน" : "";
            $address .= " $prefix {$row['register_village']}";
        }
        if (!empty($row['register_alley'])) {
            $prefix = in_array($format, $formats) ? "ซอย" : "";
            $address .= " $prefix {$row['register_alley']}";
        }
        if (!empty($row['register_street'])) {
            $prefix = in_array($format, $formats) ? "ถนน" : "";
            $address .= " $prefix {$row['register_street']}";
        }
        if (!empty($row['register_district_name'])) {
            $prefix = in_array($format, $formats) ? (1 == $format ? "แขวง" : "ตำบล") : "";
            $address .= " $prefix {$row['register_district_name']}";
        }
        if (!empty($row['register_city_name'])) {
            $prefix = in_array($format, $formats) ? (1 == $format ? "เขต" : "อำเภอ") : "";
            $address .= " $prefix {$row['register_city_name']}";
        }
        if (!empty($row['register_province_name'])) {
            $prefix = in_array($format, $formats) ? "จังหวัด" : "";
            $address .= " $prefix {$row['register_province_name']}";
        }

        if (!empty($row['register_postcodes'])) {   // 邮编
            $prefix = in_array($format, $formats) ? "รหัสไปรษณีย์" : "";
            $address .= " $prefix {$row['register_postcodes']}";
        }

        return trim($address);
    }

    /**
     * @param $row      array   需要格式化的数据 居住地地址 马来
     * @param $country  string  国家
     * @param $format   int     格式
     * @return string   string  返回数据
     */
    private function formatMyPresentAddress($row) {
        $address = "";
        if (empty($row) || !is_array($row)) {
            return $address;
        }

        if (!empty($row['residence_detail_address'])) {
            $address .= "{$row['residence_detail_address']}";
        }
        if (!empty($row['residence_city_name'])) {
            $address .= " {$row['residence_city_name']}";
        }
        if (!empty($row['residence_province_name'])) {
            $address .= " {$row['residence_province_name']}";
        }
        if (!empty($row['residence_postcodes'])) {  // 邮编
            $address .= " {$row['residence_postcodes']}";
        }

        return trim($address);
    }

    /**
     * @param $row      --需要格式化的数据 居住地地址 除马来之外的所有
     * @param $country  --国家
     * @param $format   --展示的格式
     * @return string   返回数据
     */
    private function formatOtherPresentAddress($row, $country, $format) {
        $address = "";
        if (empty($row) || !is_array($row)) {
            return  $address;
        }

        if (in_array($country, ['MY'])) { // 如果是马来 返回空
            return $address;
        }

        $formats = [1,2];
        if (!empty($row['residence_house_num'])) {
            $prefix = in_array($format, $formats) ? "บ้านเลขที่" : "";
            $address .= "$prefix {$row['residence_house_num']}";
        }
        if (!empty($row['residence_village_num'])) {
            $prefix = in_array($format, $formats) ? "หมู่" : "";
            $address .= " $prefix {$row['residence_village_num']}";
        }
        if (!empty($row['residence_village'])) {
            $prefix = in_array($format, $formats) ? "หมู่บ้าน" : "";
            $address .= " $prefix {$row['residence_village']}";
        }
        if (!empty($row['residence_alley'])) {
            $prefix = in_array($format, $formats) ? "ซอย" : "";
            $address .= " $prefix {$row['residence_alley']}";
        }
        if (!empty($row['residence_street'])) {
            $prefix = in_array($format, $formats) ? "ถนน" : "";
            $address .= " $prefix {$row['residence_street']}";
        }
        if (!empty($row['residence_district_name'])) {
            $prefix = in_array($format, $formats) ? (1 == $format) ? "แขวง" : "ตำบล" : "";
            $address .= " $prefix {$row['residence_district_name']}";
        }
        if (!empty($row['residence_city_name'])) {
            $prefix = in_array($format, $formats) ? (1 == $format) ? "เขต" : "อำเภอ" : "";
            $address .= " $prefix {$row['residence_city_name']}";
        }
        if (!empty($row['residence_province_name'])) {
            $prefix = in_array($format, $formats) ? "จังหวัด" : "";
            $address .= " $prefix {$row['residence_province_name']}";
        }

        if (!empty($row['residence_postcodes'])) {  // 邮编
            $prefix = in_array($format, $formats) ? "รหัสไปรษณีย์" : "";
            $address .= " $prefix {$row['residence_postcodes']}";
        }

        return trim($address);
    }


    /**
     * excel/staff-hold
     * hold 导出
     * @param $args
     * @param $hcm_excel_task_id
     * @return void
     */
    public function actionStaffHold($args, $hcm_excel_task_id) {
        $this->echo('staff hold begin');
        $before_1_year = date('Y-01-01 00:00:00',strtotime(date('Y-01-01'). '- 1 year'));
        if (!isCountry('TH')) {
            $before_1_year = '2019-01-01 00:00:00';
        }
        try {
            $args_arr = json_decode(base64_decode($args),true);
            $lang = $args_arr['date']['lang']??'en';
            $header_from = $args_arr['date']['header_from']??'';
            $excel_header = [
                $this->lang->get('staff_no', '', $lang),
                $this->lang->get('name', '', $lang),
                $this->lang->get('name_en', '', $lang),
                $this->lang->get('gender', '', $lang),
                $this->lang->get('job_title', '', $lang),
                $this->lang->get('respective_region', '', $lang),
                $this->lang->get('department', '', $lang),
                $this->lang->get('state', '', $lang),
                $this->lang->get('hire_date', '', $lang),
                $this->lang->get('leave_date', '', $lang),
                'Updated At',
                $this->lang->get('stop_payment_type', '', $lang),
                $this->lang->get('payment_state_block_reason', '', $lang),
                $this->lang->get('staff_hold_cycle', '', $lang),
            ];
            if(YII_COUNTRY == 'PH') {
                unset($excel_header[5]);
                $excel_header = array_values($excel_header);
            }
            $data_arr[] = $excel_header;

            $storeTemp = SysStoreTemp::temp();
            $all_origins = StaffService::getInstance()->getStaffListQueryObject($args_arr['date'])
                ->andWhere([
                    'OR',
                    [
                        'AND',
                        ['hr_staff_info.state' => BaseStaffInfo::STATE_RESIGN],
                        ['>=', 'hr_staff_info.leave_date', $before_1_year],
                    ],
                    ['IN', 'hr_staff_info.state', [BaseStaffInfo::STATE_ON_JOB, BaseStaffInfo::STATE_SUSPENSION]],
                ])
                ->andFilterWhere(['payment_state' => 2])->indexBy('staff_info_id')->all(Yii::$app->get('backyard'));
          // dd( $all_origins->createCommand()->getRawSql());
            if(!empty($all_origins)) {
                $all_department_list = array_column(SysDepartment::find()->select(['id', 'name'])->asArray()->all(),'name','id');
                $config_job_title = Yii::$app->sysconfig->jobTitle;
                $sorting_no_my = array_flip(AreaManager::$areasMy);
                $sorting_no_la = array_flip(AreaManager::$areasLa);

                $page_size = 2000;//每页多少条
                $origins_count = count($all_origins);//总条数
                $page_count = ceil($origins_count / $page_size);//总页数

                for($i = 1 ; $i<= $page_count; $i++) {
                    $start = ($i -1) * $page_size;//偏移量
                    $origins = array_slice($all_origins, $start, $page_size, true);

                    $staff_info_ids = array_column($origins, 'staff_info_id');
                    $hold_cycle_list = StaffHold::getStaffHoldCycleList(['staff_info_ids' => $staff_info_ids, 'lang' => $lang]);
                    foreach ($origins as $one) {
                        $job_title = $config_job_title[$one['job_title']] ?? $one['job_title'];
                        $respective_region = '';
                        if ($one['sys_store_id'] != -1) {
                            $storeInfoOne = $storeTemp[$one['sys_store_id']] ?? [];
                            switch (YII_COUNTRY) {
                                case 'MY':
                                    $respective_region = isset($storeInfoOne['sorting_no']) && isset($sorting_no_my[$storeInfoOne['sorting_no']]) ? $storeInfoOne['sorting_no'] : '';
                                    break;
                                case 'LA':
                                    $respective_region = isset($storeInfoOne['sorting_no']) && isset($sorting_no_la[$storeInfoOne['sorting_no']]) ? $storeInfoOne['sorting_no'] : '';
                                    break;
                                case 'PH'://菲律宾去掉 所属区域
                                    $respective_region = '';
                                    break;
                                default:
                                    $area = Yii::$app->sysconfig->getAreaByProvinceCode($storeTemp[$one['sys_store_id']]['province_code'] ?? '');
                                    if (!empty($area)) {
                                        $respective_region = $area['name'] ?? ''; //'所属区域';
                                    }
                            }
                        }
                        $department_name = $all_department_list[$one['sys_department_id']] ?? $one['sys_department_id'];
                        if($one['state'] == 1 && $one['wait_leave_state'] == 1) {
                            $state = $this->lang->get('wait_leave_state', '', $lang);
                        } else {
                            $state = $this->lang->get('state_' . $one['state'], '', $lang);
                        }

                        $hire_date = date('Y-m-d', strtotime($one['hire_date']));
                        $leave_date = $one['state'] == 2 && $one['leave_date'] ? date('Y-m-d', strtotime($one['leave_date'])) : '';


                        //hold类型
                        $hold_payment_type = '';
                        if(!empty($one['stop_payment_type'])) {
                            $stop_payment_type = explode(',', $one['stop_payment_type']);
                            $stop_payment = [];
                            foreach ($stop_payment_type as $k => $v) {
                                $stop_payment[] = $this->lang->get('stop_payment_type_'.$v, '', $lang);
                            }
                            $hold_payment_type = implode(",",$stop_payment);
                        }

                        //hold原因
                        $payment_markup = '';
                        if(!empty($one['payment_markup'])) {
                            $payment_markup_arr = array_filter(explode(',',$one['payment_markup']));
                            $payment_markup_name = [];
                            foreach ($payment_markup_arr as $p_k => $p_v) {
                                $paymentRemarkKey = Staff::getPaymentRemark($p_v);
                                $payment_markup_name[] = $this->lang->get($paymentRemarkKey, '', $lang);
                            }
                            $payment_markup = implode(";", $payment_markup_name);
                        }

                        //hold 周期
                        $hold_cycle = $hold_cycle_list[$one['staff_info_id']] ?? '';
                        $staff_hold_cycle = !empty($hold_cycle) ? implode(',', $hold_cycle) : '';

                        $row = [
                            $one['staff_info_id'],
                            $one['name'],
                            $one['name_en'],
                            $this->lang->get('sex_' . $one['sex'], '', $lang),
                            $job_title,
                            $respective_region,
                            $department_name,
                            $state,
                            $hire_date,
                            $leave_date,
                            $one['updated_at'],
                            $hold_payment_type,
                            $payment_markup,
                            $staff_hold_cycle,
                        ];
                        if(YII_COUNTRY == 'PH') {
                            unset($row[5]);
                            $data_arr[] = array_values($row);
                        } else {
                            $data_arr[] = $row;
                        }
                    }
                }
            }
            //上传oss
            $path = Yii::$app->csv->filePutAndUploadOss($data_arr, $args_arr['file_name']);
            $hcmExcelTask = HcmExcelTask::find()->where(['id' => $hcm_excel_task_id])->one();
            if($header_from == 'fbi'){
                $exportManage = ExportManage::find()->where(['staff_id' => $hcmExcelTask->staff_info_id,'module_code'=>$hcmExcelTask->action_name.'_'.$hcmExcelTask->id])->one();
                if($exportManage){
                    $exportManage->file_path = Yii::$app->FlashOss->signUrl($path,86400*20);
                    $exportManage->state = ExportManage::STATUS_SUCCESS;
                    $exportManage->save();
                    $hcmExcelTask->is_delete = 1;
                }
            }
            $hcmExcelTask->path= $path;
            $hcmExcelTask->status = 1;
            $hcmExcelTask->finish_at = gmdate('Y-m-d H:i:s',time()+TIME_ADD_HOUR*3600);
            $hcmExcelTask->save();
        } catch (\Exception $e) {
            $this->echo('error:'.$e->getMessage().';行号:'.$e->getLine().'---'.$e->getMessage().'----'.$e->getTraceAsString());
            Yii::$app->logger->write_log([
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
        $this->echo('end');
    }


    /**
     * staff-export/excel-assist
     * 处理hcm内生成的外协员工下载任务
     * @param $args
     * @param $hcm_excel_task_id
     * @return bool
     */
    public function actionExcelAssist($args, $hcm_excel_task_id)
    {
        $this->echo('actionExcelAssist begin');
        try {
            $args_arr    = json_decode(base64_decode($args), true);
            $lang        = $args_arr['data']['lang'] ?? 'en';
            $header_from = $args_arr['data']['header_from'] ?? '';
            Yii::$app->logger->write_log('actionExcelAssist :flash_外协员工下载任务，开始，参数：'.json_encode($args_arr),
                'info');
            $storeTemp = SysStoreTemp::temp();
            $all_origins = StaffService::getInstance()->getStaffListQueryObject($args_arr['data'])->indexBy('staff_info_id')->all(Yii::$app->get('r_backyard'));
            $this->echo('actionExcelAssist :flash_外协员工下载任务 -  args:'.json_encode($args_arr).' - 总条数:'.count($all_origins));
            $items            = [];
            $staffids = array_keys($all_origins);
            if (!empty($staffids)) {
                $postions = HrStaffInfoPosition::find()
                    ->select(['staff_info_id', 'position_category'])
                    ->where(['IN', 'staff_info_id', $staffids])
                    ->orderBy('staff_info_id')
                    ->asArray()
                    ->all(Yii::$app->get('r_backyard'));
                foreach ($postions as $key => $pos) {
                    $staffPos[$pos['staff_info_id']][] = $this->lang->get('role_'.$pos['position_category'],'',$lang);
                }

                $staff_items_list = StaffItems::find()->where(['staff_info_id' => $staffids])->all(Yii::$app->get('r_backyard'));
                foreach ($staff_items_list as $key => $obj) {
                    $items[$obj->staff_info_id][$obj->item] = $obj->value;
                }
            }
            $allDepeartments = SysDepartmentService::getInstance()->getDataUseIdIndex(['id', 'name'], 'name');

            $config_JobTitle = HrJobTitle::find()
                ->select('job_name')
                ->where(['status' => 1])
                ->indexBy('id')
                ->asArray()
                ->column();
            $area_list = SysStoreService::getInstance()->getAreaByProvinceCode();

            $all_bank_type   = Yii::$app->sysconfig->all_bank_type();

            $sorting_no_my = array_flip(AreaManager::$areasMy);
            $sorting_no_la = array_flip(AreaManager::$areasLa);

            $province_list = array_column(Yii::$app->sysconfig->getProvince(), null, 'code');
            $city_list     = array_column(Yii::$app->sysconfig->getCityList(), null, 'code');
            $district_list = array_column(Yii::$app->sysconfig->getDistrict(), null, 'code');

            $osExtendInfo = $outsourceCompanyListToId = [];
            if(isCountry('TH')) {
                $osExtendInfo = OsStaffInfoExtend::find()->select(['staff_info_id', 'company_item_id', 'is_complete_address'])->where(['IN', 'staff_info_id', $staffids])->asArray()->indexBy('staff_info_id')->all(Yii::$app->get('r_backyard'));
                $outsourceCompanyList = Yii::$app->jrpc->getOsCompanyList();
                $outsourceCompanyListToId = !empty($outsourceCompanyList) ? array_column($outsourceCompanyList, 'label', 'value') : [];
            }

            foreach ($all_origins as $one) {
                $row['Employee No.']        = $one['staff_info_id'] ?? '';//员工工号
                $row['Name']                = $one['name'] ?? '';         //姓名
                $row['Englsh Name']         = $one['name_en'] ?? '';      //英文名称
                $row['Gender']              = $this->lang->get($one['sex'] == 1 ? 'male' : ($one['sex'] == 2 ? 'female' : ''),'',$lang);
                $row['Mobile Number']       = ' '.($one['mobile'] ?? '')."\t";                                                          //手机号
                $row['ID No./Passport No.'] = ' '.$one['identity']."\t";                                                    //身份证护照
                $row['Email']               = $one['personal_email'] ?? '';                                                           //邮箱
                $row['Department']          = $allDepeartments[$one['sys_department_id']] ?? $one['sys_department_id'];//部门
                $row['Position']            = $config_JobTitle[$one['job_title']] ?? $one['job_title'];                //职位
                $storeInfoOne               = $storeTemp[$one['sys_store_id']] ?? [];

                if (YII_COUNTRY != 'PH') {
                    $row['Region'] = ''; //'所属区域';
                }

                if ($one['sys_store_id'] != -1) {
                    switch (YII_COUNTRY) {
                        case 'MY':
                            $row['Region'] = isset($storeInfoOne['sorting_no']) && isset($sorting_no_my[$storeInfoOne['sorting_no']]) ? $storeInfoOne['sorting_no'] : '';
                            break;
                        case 'LA':
                            $row['Region'] = isset($storeInfoOne['sorting_no']) && isset($sorting_no_la[$storeInfoOne['sorting_no']]) ? $storeInfoOne['sorting_no'] : '';
                            break;
                        case 'PH':
                            break;
                        default:
                            $store_province_code = $storeTemp[$one['sys_store_id']]['province_code'] ?? '';
                            $row['Region'] = $area_list[$store_province_code] ?? ''; //'所属区域';
                    }
                }
                $row['Branch']               = $storeTemp[$one['sys_store_id']]['name'] ?? $one['sys_store_id']; //'所属网点'
                $row['Role']                 = isset($staffPos[$one['staff_info_id']]) ? implode(',',
                    $staffPos[$one['staff_info_id']]) : '';                                                      //角色
                $row['Courier Vehicle Type'] = $items[$one['staff_info_id']]['CAR_TYPE'] ?? '';                  //车辆类型
                $row['License Plate Number'] = ' '.($items[$one['staff_info_id']]['CAR_NO'] ?? '')."\t";                    //车牌号
                $row['Work Status']          = $this->lang->get('state_'.$one['state'],'',$lang);                         //在职状态
                $row['Employment Date']      = (date('Y-m-d', strtotime($one['hire_date'])));                   //入职时间
                $row['Date Expecting To Resign'] =
                    ($one['state'] == BaseStaffInfo::STATE_ON_JOB && $one['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_YES && !empty($one['leave_date'])) ? (date('Y-m-d',
                        strtotime($one['leave_date']))) : ''; // 待离职日期

                $row['Resignation Date']    = $one['state'] == BaseStaffInfo::STATE_RESIGN && $one['leave_date'] ? (date('Y-m-d',
                    strtotime($one['leave_date']))) : '';                                 //离职时间
                $row['Employee attributes'] = $this->lang->get('formal_'.$one['formal'],'',$lang); //员工属性,

                $payType          = $items[$one['staff_info_id']]['PAY_TYPE'] ?? '';
                $outsourcing_type = $items[$one['staff_info_id']]['OUTSOURCING_TYPE'] ?? '';
                $sm               = $items[$one['staff_info_id']]['SUPERVISOR_MOBILE'] ?? '';

                $row['Outsourcing staff type'] = $outsourcing_type ? $this->lang->get(strtolower('outsourcing_type_'.$outsourcing_type),'',$lang) : '';                                            //外协类型
                $row['Creation Channel'] = '';
                if($one['staff_type'] != 0) {
                    $row['Creation Channel']   = in_array($one['staff_type'],[2, 3]) ? $this->lang->get('staff_type_2','',$lang) : $this->lang->get('staff_type_'.$one['staff_type'],'',$lang); //创建类型
                }

                $row['Types of settlement']    = $payType ? $this->lang->get(strtolower('pay_type_'.$payType),'',$lang) : '';                                                                      //外协类型

                if (YII_COUNTRY == 'PH' || YII_COUNTRY == 'TH' || YII_COUNTRY == 'MY') {                                                            //只有 ph th my 有
                    $row['Outsourcing hire type'] = $this->lang->get('hire_type_'.$one['hire_type'],'',$lang); //'外协雇佣类型';
                }

                if(YII_COUNTRY == 'TH') {
                    $row['Outsourcing Company(Old)'] = $one['company_name_ef'] ?? '';                            //'外协公司名称',
                    $row['Outsourcing Company']      = isset($osExtendInfo[$one['staff_info_id']]) ? ($outsourceCompanyListToId[$osExtendInfo[$one['staff_info_id']]['company_item_id']] ?? '') : '';                            //'外协公司名称',
                } else {
                    $row['Outsourcing Company'] = $one['company_name_ef'] ?? '';                            //'外协公司名称',
                }
                $row['Manager Mobile']      = ' '.($sm ? $this->lang->get(strtolower($sm),'',$lang) : '')."\t";       //主管电话,
                $row['Driver License number'] = ' '.($items[$one['staff_info_id']]['DRIVER_LICENSE'] ?? '')."\t";//驾驶证号
                $row['Bank Name']           = $all_bank_type[$one['bank_type']] ?? '';                  //银行
                $row['Cardholder']          = $items[$one['staff_info_id']]['BANK_NO_NAME'] ?? '';//持卡人
                $row['Bank Account']        = ' '.($one['bank_no'] ?? '')."\t";                              //银行账号

                if (YII_COUNTRY == 'MY') {
                    $relatives_relationship                          = $items[$one['staff_info_id']]['RELATIVES_RELATIONSHIP'] ?? '';
                    $residential_address                             = $items[$one['staff_info_id']]['RESIDENTIAL_ADDRESS'] ?? '';
                    $relatives_call_name                             = $items[$one['staff_info_id']]['RELATIVES_CALL_NAME'] ?? '';
                    $row['Residential address']                      = empty($residential_address) ? "" : $residential_address . "\t";                                                                   //居住地址
                    $row['Name of emergency contact person']         = is_numeric($relatives_call_name) ? $relatives_call_name . "\t" : $relatives_call_name;                                            //紧急联系人称呼
                    $row['Relationship of emergency contact person'] = $relatives_relationship ? $this->lang->get('relatives_relationship_' . $relatives_relationship,
                        '',
                        $lang) : '';                                                                                                                                                       //紧急联系人关系
                    $row['Phone number of emergency contact person'] = ($items[$one['staff_info_id']]['RELATIVES_MOBILE'] ?? '') . "\t";                                                   //紧急联系人电话
                }

                $row['Creation time'] = gmdate('Y-m-d H:i:s', strtotime($one['created_at']) + TIME_ADD_HOUR * 3600);
                $row['Creator']       = $one['creater'] ?? '';

                if(isCountry('TH')) {
                    $default_country_value = 1;

                    $register_country = $items[$one['staff_info_id']]['REGISTER_COUNTRY'] ?? $default_country_value;//户口所在国家（默认1-泰国，老数据没有此字段）

                    $address['register_house_num']   = $items[$one['staff_info_id']]['REGISTER_HOUSE_NUM'] ?? '';  //户口所在门牌号
                    $address['register_village_num'] = $items[$one['staff_info_id']]['REGISTER_VILLAGE_NUM'] ?? '';//户口所在村号
                    $address['register_village']     = $items[$one['staff_info_id']]['REGISTER_VILLAGE'] ?? '';    //户口所在村
                    $address['register_alley']       = $items[$one['staff_info_id']]['REGISTER_ALLEY'] ?? '';      //户口所在巷
                    $address['register_street']      = $items[$one['staff_info_id']]['REGISTER_STREET'] ?? '';     //户口所在街道

                    $register_district                 = $items[$one['staff_info_id']]['REGISTER_DISTRICT'] ?? '';//户口所在乡
                    $address['register_district_name'] = !empty($register_district) ? ($register_country == $default_country_value ? ($district_list[$register_district]['name'] ?? '') : $register_district) : '';

                    $register_city                 = $items[$one['staff_info_id']]['REGISTER_CITY'] ?? '';//户口所在市
                    $address['register_city_name'] = !empty($register_city) ? ($register_country == $default_country_value ? ($city_list[$register_city]['name'] ?? '') : $register_city) : '';

                    $register_province                 = $items[$one['staff_info_id']]['REGISTER_PROVINCE'] ?? '';               //户口所在省
                    $address['register_province_name'] = !empty($register_province) ? ($register_country == $default_country_value ? ($province_list[$register_province]['name'] ?? '') : $register_province) : '';

                    $address['register_postcodes'] = $items[$one['staff_info_id']]['REGISTER_POSTCODES'] ?? '';  //户口所在邮编

                    //泰国国家 户籍地址格式：
                    if (strtolower($register_province) == 'th01') {
                        //泰国国家且省份是曼谷（后缀名不一样）
                        //$export_data[$config_field[$value]] = "บ้านเลขที่ {$row['register_house_num']} หมู่ {$row['register_village_num']} หมูบ้าน{$row['register_village']} ซอย {$row['register_alley']} ถนน {$row['register_street']} แขวง {$row['register_district_name']} เขต {$row['register_city_name']} จังหวัด {$row['register_province_name']}";
                        $addressAll = $this->formatOtherPermanentAddress($address,
                            'TH', 1);
                    } else {
                        //非曼谷省份后缀名不一样）
                        //$export_data[$config_field[$value]] = "บ้านเลขที่ {$row['register_house_num']} หมู่ {$row['register_village_num']} หมูบ้าน {$row['register_village']} ซอย {$row['register_alley']} ถนน {$row['register_street']} ตำบล {$row['register_district_name']} อำเภอ {$row['register_city_name']}  จังหวัด {$row['register_province_name']}";
                        $addressAll = $this->formatOtherPermanentAddress($address,
                            'TH', 2);
                    }

                    $row['Complete address'] = isset($osExtendInfo[$one['staff_info_id']]) ? $this->lang->get('is_disability_' . $osExtendInfo[$one['staff_info_id']]['is_complete_address'], '', $lang) : '';

                    $row['Register house num']     = $address['register_house_num'];
                    $row['Register village num']   = $address['register_village_num'];
                    $row['Register village']       = $address['register_village'];
                    $row['Register lane']          = $address['register_alley'];
                    $row['Register road']          = $address['register_street'];
                    $row['Register Township']      = $address['register_district_name'];
                    $row['Register city name']     = $address['register_city_name'];
                    $row['Register province name'] = $address['register_province_name'];
                    $row['Register postcodes']     = ' '.$address['register_postcodes']."\t";
                    $row['Register address']       = $addressAll;
                }

                if (!isset($arr)) {
                    $arr[] = array_keys($row);
                }
                $arr[] =  array_values($row);
            }

            if(empty($arr)){
                $arr[] = ['no data!'];
            }
            $this->echo(gmdate('Y-m-d H:i:s', time() + 8 * 3600).'生成文件开始'.'总数量：'.count($arr ?? []));

            $path = Yii::$app->csv->filePutAndUploadOss($arr ??[], $args_arr['file_name']);
            Yii::$app->logger->write_log('actionExcelAssist  flash_外协员工下载任务，生成文件完成:'.$path.' ，参数：'.json_encode($args_arr),
                'info');

            $hcmExcelTask = HcmExcelTask::find()->where(['id' => $hcm_excel_task_id])->one();
            if ($header_from == 'fbi') {
                $exportManage = ExportManage::find()->where([
                    'staff_id'    => $hcmExcelTask->staff_info_id,
                    'module_code' => $hcmExcelTask->action_name.'_'.$hcmExcelTask->id,
                ])->one();
                if ($exportManage) {
                    $exportManage->file_path = Yii::$app->FlashOss->signUrl($path, 86400 * 20);
                    $exportManage->state     = ExportManage::STATUS_SUCCESS;
                    $exportManage->save();
                    $hcmExcelTask->is_delete = 1;
                }
            }

            $hcmExcelTask->path      = $path;
            $hcmExcelTask->status    = 1;
            $hcmExcelTask->finish_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
            return $hcmExcelTask->save();
        } catch (\Exception $e) {
            $this->echo('error:'.$e->getMessage().';行号:'.$e->getLine().'---'.$e->getMessage().'----'.$e->getTraceAsString());
            Yii::$app->logger->write_log('actionExcelAssist flash_外协员工下载任务，可能出现的问题'.$e->getMessage().';行号:'.$e->getLine().'---'.$e->getFile());
        }
        $this->echo('end');
    }

    /**
     * 印尼-户口所在地地址
     * @param $row Array 需要格式化的数据
     * @return string
     */
    private function formatIdPermanentAddress($row)
    {
        $address = '';
        if (empty($row) || !is_array($row)) {
            return $address;
        }

        if (!empty($row['register_street'])) {
            $address .= " {$row['register_street']} ,";
        }

        if (!empty($row['register_rt'])) {
            $address .= " RT {$row['register_rt']} ,";
        }

        if (!empty($row['register_rw'])) {
            $address .= " RW {$row['register_rw']} ,";
        }

        if (!empty($row['register_village_name'])) {
            $address .= " {$row['register_village_name']} ,";
        }

        if (!empty($row['register_district_name'])) {
            $address .= " {$row['register_district_name']} ,";
        }
        if (!empty($row['register_city_name'])) {
            $address .= " {$row['register_city_name']} ,";
        }
        if (!empty($row['register_province_name'])) {
            $address .= " {$row['register_province_name']} ,";
        }

        if (!empty($row['register_postcodes'])) {   // 邮编
            $address .= " {$row['register_postcodes']}";
        }

        return trim($address);
    }

    /**
     * 印尼-居住地地址处理
     * @param $row Array 需要格式化的数据
     * @return string
     */
    public function formatIdPresentAddress($row)
    {
        $address = '';
        if (empty($row) || !is_array($row)) {
            return $address;
        }

        if (!empty($row['residence_street'])) {
            $address .= " {$row['residence_street']} ,";
        }

        if (!empty($row['residence_rt'])) {
            $address .= " RT {$row['residence_rt']} ,";
        }

        if (!empty($row['residence_rw'])) {
            $address .= " RW {$row['residence_rw']} ,";
        }

        if (!empty($row['residence_village_name'])) {
            $address .= " {$row['residence_village_name']} ,";
        }

        if (!empty($row['residence_district_name'])) {
            $address .= " {$row['residence_district_name']} ,";
        }
        if (!empty($row['residence_city_name'])) {
            $address .= " {$row['residence_city_name']} ,";
        }
        if (!empty($row['residence_province_name'])) {
            $address .= " {$row['residence_province_name']} ,";
        }

        if (!empty($row['residence_postcodes'])) {   // 邮编
            $address .= " {$row['residence_postcodes']}";
        }

        return trim($address);
    }

    /**
     * v6
     * @param $args
     * @param $hcm_excel_task_id
     * @return bool|void
     */
    public function actionExcelV6($args, $hcm_excel_task_id)
    {
        $this->echo('v6new begin '.$hcm_excel_task_id);
        $args            = is_json($args) ? json_decode($args, true) : json_decode(base64_decode($args), true);
        $args_arr        = $args;
        $workerNum       = env('staff_info_download_worker_num',2);
        $processArr      = [];
        $header_from     = $args_arr['date']['header_from'] ?? '';
        $lang            = $args_arr['date']['lang'] ?? 'en';
        $fileName        = Yii::$app->params['csv_file_name_prefix'] . $args_arr['file_name'];//文件名前缀
        $path            = Yii::$app->basePath . '/web/excel/' . $fileName;
        $storeTemp       = SysStoreTemp::temp();
        $regionAndPieces = [];

        $pieces         = SysManagePiece::find()->asArray()->all();
        $pieces         = array_column($pieces, 'name', 'id');
        $regions        = SysManageRegion::find()->asArray()->all();
        $regions        = array_column($regions, 'name', 'id');
        $allDepartments = $this->getDepartmentList($lang);

        $province_list = array_column(Yii::$app->sysconfig->getProvince(), null, 'code');
        $city_list     = array_column(Yii::$app->sysconfig->getCityList(), null, 'code');
        $district_list = array_column(Yii::$app->sysconfig->getDistrict(), null, 'code');

        $config_field = Yii::$app->sysconfig->export_field_all;

        $area_list = SysStoreService::getInstance()->getAreaByProvinceCode();

        $vehicleJobTitle = VehicleInfoService::getInstance()->getVehicleTypeJobTitle();

        $show_vehicle_type_category_job_title     = SettingEnvService::getInstance()->getSetVal('show_vehicle_type_category_job_title');
        $show_vehicle_type_category_job_title_arr = [];
        if (!empty($show_vehicle_type_category_job_title)) {
            $show_vehicle_type_category_job_title_arr = explode(',', $show_vehicle_type_category_job_title);
        }
        //新旧离职原因关联
        $leave_scenario_reason_map = LeaveService::getInstance()->getLeaveScenarioReasonRelationMap();
        //获取所有离职原因
        $all_leave_type_reason = Yii::$app->jrpc->leaveReasonMap($lang);
        //离职背景
        $all_leave_scenario = Yii::$app->jrpc->leaveScenarioMap($lang);
        //离职原因版本
        $all_leave_reason_version = LeaveService::getInstance()->getLeaveReasonVersionMap();
        //老离职原因与离职场景
        $all_old_new_leave_reason = LeaveService::getInstance()->getOldNewLeaveReasonMap();


        // 获取国籍
        $nationalityList = Yii::$app->jrpc->getTotalDictionaryItemsByDictCode(['dict_code' => 'nationality_region'],
            $lang);
        $nationalityList = array_column($nationalityList, 'label', 'value');

        // 获取工作所在国家
        $workingCountryList = Yii::$app->jrpc->getTotalDictionaryItemsByDictCode(['dict_code' => 'working_country'],
            $lang);
        $workingCountryList = array_column($workingCountryList, 'label', 'value');
        //文件表头
        $field_arr = array_intersect($args_arr['date']['export_field'], array_keys($config_field));
        $header    = $this->getExcelHeader($field_arr, $lang);
        $this->addFileBom($path, [$header]);

        //获取职位性质，成本类型 枚举
        $positionCostList = Yii::$app->jrpc->get_position_cost_list($lang);
        //获取所有 部门关联职位的 职位性质 成本类型
        $jobDepartmentToPositionCost = HrJobTitleService::getInstance()->getJobDepartmentPositionAll();
        $hidden_job_grade_staffs     = StaffService::getInstance()->getCEmployee();
        $contractCompanyMap = CompanyService::getInstance()->getContractCompanyMap();

        for ($p = 0; $p < $workerNum; $p++) {
            $process = new swoole_process(function (swoole_process $worker) use (
                $workerNum,
                $p,
                $args_arr,
                $field_arr,
                $fileName,
                $path,
                $storeTemp,
                $regionAndPieces,
                $area_list,
                $config_field,
                $district_list,
                $city_list,
                $province_list,
                $allDepartments,
                $regions,
                $pieces,
                $workingCountryList,
                $nationalityList,
                $all_leave_type_reason,
                $show_vehicle_type_category_job_title_arr,
                $vehicleJobTitle,
                $positionCostList,
                $jobDepartmentToPositionCost,
                $hidden_job_grade_staffs,
                $contractCompanyMap,
                $all_leave_scenario,
                $leave_scenario_reason_map,
                $all_leave_reason_version,
                $all_old_new_leave_reason
            ) {
                $this->dbClose();
                $lang      = $args_arr['date']['lang'] ?? 'en';
                $all_origins = StaffService::getInstance()->getStaffListQueryObject($args_arr['date'])->andWhere("mod(hr_staff_info.id,$workerNum) = $p")->indexBy('staff_info_id')->all(Yii::$app->get('backyard'));
                $positionList = !empty($positionCostList['position_type']) ? array_column($positionCostList['position_type'],
                    'label', 'value') : [];
                $costList = !empty($positionCostList['cost_type']) ? array_column($positionCostList['cost_type'],
                    'label', 'value') : [];


                $default_country_value = strval(SettingEnvService::getInstance()->getSetVal('s_f_d_nationality') ?: '1');

                if (empty($all_origins)) {
                    $worker->write('1');
                    return ;
                }

                $page_size     = 500;                              //每页多少条
                $origins_count = count($all_origins);              //总条数
                $page_count    = ceil($origins_count / $page_size);//总页数

                for ($i = 1; $i <= $page_count; $i++) {
                    $excel_data  = [];
                    $start   = ($i - 1) * $page_size;//偏移量
                    $origins = array_slice($all_origins, $start, $page_size, true);

                    $staffids = array_keys($origins);
                    if ($staffids ?? false) {
                        $postions = HrStaffInfoPosition::find()
                            ->select(['staff_info_id', 'position_category'])
                            ->where(['IN', 'staff_info_id', $staffids])
                            ->orderBy('staff_info_id')
                            ->asArray()
                            ->all();
                        foreach ($postions as $key => $pos) {
                            $staffPos[$pos['staff_info_id']][] = $this->lang->get('role_' . $pos['position_category'],
                                '', $lang);
                        }

                        $items            = [];
                        $items_manger     = [];
                        $annexList        = [];
                        $staff_items_list = StaffItems::find()->where(['staff_info_id' => $staffids])->all();

                        $villageCodes = $villageListKy = [];

                        foreach ($staff_items_list as $key => $obj) {
                            $items[$obj->staff_info_id][$obj->item] = $obj->value;
                            if ($obj->item == 'MANGER' && !empty($obj->value)) {
                                $items_manger[] = (int)$obj->value;
                            }

                            //收集所有的村庄信息
                            if (YII_COUNTRY == 'ID') {
                                if ($obj->item == 'RESIDENCE_VILLAGE' && SysArea::checkVillageCode($obj->value)) {
                                    $villageCodes[] = $obj->value;
                                }

                                if ($obj->item == 'REGISTER_VILLAGE' && SysArea::checkVillageCode($obj->value)) {
                                    $villageCodes[] = $obj->value;
                                }
                            }
                        }

                        //收集村庄信息
                        if (!empty($villageCodes)) {
                            $villageList   = SysArea::getVillageByCodes($villageCodes);
                            $villageListKy = array_column($villageList, 'name', 'code');
                        }

                        // 泰国、菲律宾 需要查询该表
                        $annexList = HrStaffAnnexInfoService::getInstance()->getHrStaffAnnexInfoByStaffIds($staffids);


                        $manger_staff_list = StaffInfo_fle::find()->where(['id' => $items_manger])->asArray()->indexBy('id')->all();

                        // 5321 【PHP】 【FBI】 【HRIS】记录员工未回公款情况 - panjia - 2019年8月9日17:59:09
                        $staffBill      = Staff::staffBill($staffids);     // 快递员|仓管员
                        $remittanceBill = Staff::remittanceBill($staffids);// 网点出纳
                    }

                    $today = new \DateTime('now', new \DateTimeZone(TIMEZONE));

                    $masters = [];
                    $subss   = [];



                    $staffManager    = new StaffManager();
                    $allow_staff_ids = $staffManager->hasGradePermissionStaffs($staffids, $args_arr['date']);
                    $allow_staff_ids = array_diff($allow_staff_ids, $hidden_job_grade_staffs);

                    // 获取所有职位信息
                    $allJobTitles = $staffManager->getAllJobTitleByIds(array_column($origins, 'job_title'));

                    //保险受益人信息
                    $insurance_beneficiary_list = HrStaffInsuranceBeneficiaryService::getInstance()->getInsuranceBeneficiaryList($staffids,
                        $lang);

                    foreach ($origins as $one) {
                        $row['staff_no'] = $one['staff_info_id'];
                        $row['name']     = $one['name'];

                        $row['HRID']    = $one['emp_id'];
                        $row['name_en'] = $one['name_en'];
                        $row['remarks'] = $one['remarks'];
                        $row['gender']  = $this->lang->get($one['sex'] == 1 ? 'male' : ($one['sex'] == 2 ? 'female' : ''),
                            '', $lang);

                        //$row['mobile'] = $one['mobile']."\t";
                        //$row['mobile_company'] = $one['mobile_company']."\t";
                        $row['mobile']         = "";
                        $row['mobile_company'] = "";

                        $row['idtentity']        = $one['identity'] . "\t";
                        $row['personal_email']   = $one['personal_email'];
                        $row['business_mailbox'] = $one['email'];
                        $department_level        = $this->getDepartmentLevelNamesV2($one['node_department_id'] != 0 ? $one['node_department_id'] : $one['sys_department_id'],
                            $allDepartments);
                        $row['c_level']          = $department_level[0] ?? '';
                        $row['company_name']     = $department_level[1] ?? '';
                        $row['dept_level_1']     = $department_level[2] ?? '';
                        $row['dept_level_2']     = $department_level[3] ?? '';
                        $row['dept_level_3']     = $department_level[4] ?? '';
                        $row['dept_level_4']     = $department_level[5] ?? '';
                        $row['contract_company_id'] = $one['contract_company_id'];

                        $row['job_title'] = $allJobTitles[$one['job_title']]['job_name'] ?? (int)$one['job_title'];
                        if (is_string($row['job_title']) && !is_numeric($row['job_title']) && HrJobTitle::STATUS_2 == $allJobTitles[$one['job_title']]['status']) {
                            $row['job_title'] .= $this->lang->get('deleted', '', $lang);
                        }

                        $row['job_title_level'] = Yii::$app->sysconfig->config_job_title_level[$one['job_title_level']] ?? $one['job_title_level'];//职等

                        if (in_array($one['staff_info_id'], $allow_staff_ids)) {
                            $row['job_title_grade'] = Yii::$app->sysconfig->config_job_title_grade_v2[$one['job_title_grade_v2']] ?? $one['job_title_grade_v2'];//职级
                        } else {
                            $row['job_title_grade'] = '';//职级
                        }


                        $row['respective_region'] = ''; //'所属区域';
                        if ($one['sys_store_id'] != -1) {
                            switch (YII_COUNTRY) {
                                case 'MY':
                                case 'LA':
                                    $row['respective_region'] = $one['store_sorting_no'];
                                    break;
                                default:
                                    $store_province_code = $one['store_province_code'] ?? '';
                                    if (!empty($store_province_code)) {
                                        $row['respective_region'] = $area_list[$store_province_code] ?? ''; //'所属区域';
                                    }
                            }
                        }
                        //大区/片区
                        $row['respective_area']     = $regions[$one['store_manage_region']] ?? '';
                        $row['respective_district'] = $pieces[$one['store_manage_piece']] ?? '';

                        $car_type                = $items[$one['staff_info_id']]['CAR_TYPE'] ?? '';
                        $row['branch']           = $one['sys_store_id'] == -1 ? $this->lang->get('head_office') : $one['store_name']; //'所属网点';
                        $row['role']             = isset($staffPos[$one['staff_info_id']]) ? implode(',',
                            $staffPos[$one['staff_info_id']]) : '';
                        $row['car_type']         = $car_type;
                        $row['car_no']           = $items[$one['staff_info_id']]['CAR_NO'] ?? '';
                        $row['vehicle_source']   = '-';                     //车辆来源
                        $row['week_working_day'] = $one['week_working_day'];//每周工作天数
                        $row['rest_type']        = $one['rest_type'];       //轮休规则
                        $row['vehicle_use_date'] = '-';                     //车辆开始使用时间
                        $row['project_num']      = $one['project_num']?: '-';   //项目期数
                        $row['van_container_state'] = ($one['van_container_state'] == 1) ? $this->lang->get('have_container') : $this->lang->get('have_no_container');
                        if(is_null($one['van_container_state'])){
                            $row['van_container_state'] = '';
                        }
                        $row['van_container_start'] = $one['van_container_start'];
                        $row['van_container_end']   = $one['van_container_end'];


                        if ($car_type == 'Van' || $car_type == 'Bike' || $car_type == 'Truck' || $car_type == 'Car') {
                            $row['vehicle_source']   = $this->lang->get('vehicle_source_' . $one['vehicle_source'], '',
                                $lang);                                         //车辆来源
                            $row['vehicle_use_date'] = $one['vehicle_use_date'];//车辆开始使用时间
                            if ($one['vehicle_source'] == 1) {
                                $row['vehicle_use_date'] = '-';
                            }
                        }

                        if (!in_array($one['job_title'], $vehicleJobTitle)) { //除 van/bike职位 外 不显示车辆信息
                            $row['car_type']         = '-';
                            $row['car_no']           = '-';
                            $row['vehicle_source']   = '-';
                            $row['vehicle_use_date'] = '-';
                        }

                        if (YII_COUNTRY == 'MY') {
                            if (in_array($one['job_title'], $show_vehicle_type_category_job_title_arr)) {
                                $row['vehicle_type_category'] = $items[$one['staff_info_id']]['VEHICLE_TYPE_CATEGORY'] ?? '';//车类型
                            } else {
                                $row['vehicle_type_category'] = '-';
                            }
                            $row['signing_date'] = $one['signing_date'];
                        }

                        if (YII_COUNTRY == 'PH') {
                            $row['conversion_permanent_date'] = $items[$one['staff_info_id']]['CONVERSION_PERMANENT_DATE'] ?? '';//转正式员工日期
                        }

                        if ($one['state'] == 1 && $one['wait_leave_state'] == 1) {
                            $row['state'] = $this->lang->get('wait_leave_state', '', $lang);
                        } else {
                            $row['state'] = $this->lang->get('state_' . $one['state'], '', $lang);
                        }
                        $stop_duty_reason        = SuspendReasonEnums::SUSPEND_TYPE_REASON_MAP;
                        $stop_duty_reason_key    = $stop_duty_reason[$one['stop_duty_reason']] ?? '';
                        $row['stop_duty_reason'] = $this->lang->get($stop_duty_reason_key, '', $lang);
                        $row['hire_date']        = date('Y-m-d', strtotime($one['hire_date']));
                        $row['leave_date']       = $one['state'] == 2 && $one['leave_date'] ? (date('Y-m-d',
                            strtotime($one['leave_date']))) : '';

                        $row['stop_duties_date'] = $one['state'] == 3 ? $one['stop_duties_date'] : '';
                        if ($one['state'] == 1 && $one['wait_leave_state'] == 1) {
                            $row['wait_leave_date'] = $one['leave_date'] ? (date('Y-m-d',
                                strtotime($one['leave_date']))) : '';
                        } else {
                            $row['wait_leave_date'] = '';
                        }

                        $row['formal']              = $this->lang->get('formal_' . $one['formal'], '', $lang); //员工属性,
                        $row['imm_supervisor']   = $items[$one['staff_info_id']]['MANGER'] ?? '';     //直线主管id
                        if (!empty($items[$one['staff_info_id']]['MANGER'])) {
                            $row['imm_supervisor_name'] = $manger_staff_list[$items[$one['staff_info_id']]['MANGER']]['name'] ?? '';//直线主管姓名
                            if (isset($manger_staff_list[$items[$one['staff_info_id']]['MANGER']]['state']) && !empty($manger_staff_list[$items[$one['staff_info_id']]['MANGER']]['state'])) {
                                $row['imm_supervisor_state'] = $this->lang->get('state_' . $manger_staff_list[$items[$one['staff_info_id']]['MANGER']]['state'],
                                    '', $lang);//直线主管在职状态
                            } else {
                                $row['imm_supervisor_state'] = '';//直线主管在职状态
                            }
                        } else {
                            $row['imm_supervisor_name']  = '';//直线主管姓名
                            $row['imm_supervisor_state'] = '';//直线主管在职状态
                        }

                        if ($one['is_sub_staff'] == 1) {
                            $row['bank_card_type'] = '';
                            $row['bank_no']        = '';
                        } else {
                            $row['bank_card_type'] = Yii::$app->sysconfig->all_bank_type()[$one['bank_type']] ?? '';
                            if (in_array(YII_COUNTRY, ['LA', 'MY'])) {
                                $row['bank_no'] = trim($one['bank_no']) . "\t";
                            } else {
                                $row['bank_no'] = trim($one['bank_no']);
                            }
                        }

                        $row['created_at'] = $one['created_at'];
                        $row['creater']    = $one['creater'];
                        $hire_date         = new \DateTime($one['hire_date'], new \DateTimeZone(TIMEZONE));
                        switch ($one['state']) {
                            case 1: //在职
                            case 3: //停职
                                $row['create_days'] = $today->diff($hire_date)->days;
                                break;
                            case 2: //离职
                                $leave_today        = new \DateTime($one['leave_date'], new \DateTimeZone(TIMEZONE));
                                $row['create_days'] = $leave_today->diff($hire_date)->days;
                                break;
                            default:
                                $row['create_days'] = '';
                        }
                        $row['stop_duties_count']          = $one['stop_duties_count'];
                        $equipment_cost                    = $items[$one['staff_info_id']]['EQUIPMENT_COST'] ?? '';
                        $equipment_deduction_type          = $items[$one['staff_info_id']]['EQUIPMENT_DEDUCTION_TYPE'] ?? '';
                        $row['equipment_cost']             = $equipment_cost ? $equipment_cost / 100 : '';
                        $row['deduction_type']             = $equipment_deduction_type ? $this->lang->get('equipment_deduction_' . $equipment_deduction_type) : '';
                        $row['payment_state']              = $this->lang->get('payment_state_' . $one['payment_state'],
                            '', $lang);

                        if (!empty($one['payment_markup'])) {
                            $payment_markup_arr  = array_filter(explode(',', $one['payment_markup']));
                            $payment_markup_name = [];
                            foreach ($payment_markup_arr as $p_k => $p_v) {
                                $paymentRemarkKey      = Staff::getPaymentRemark($p_v);
                                $payment_markup_name[] = $this->lang->get($paymentRemarkKey, '', $lang);
                            }
                            $row['payment_state_block_reason'] = implode(";", $payment_markup_name);
                        } else {
                            $row['payment_state_block_reason'] = '';
                        }

                        // 5321 【PHP】 【FBI】 【HRIS】记录员工未回公款情况 - panjia - 2019年8月9日17:59:09
                        $row['staffBill_01'] = $staffBill[$one['staff_info_id']]['num'] ?? 0;
                        $row['staffBill_02'] = $staffBill[$one['staff_info_id']]['amount'] ?? 0;
                        $row['staffBill_03'] = $remittanceBill[$one['staff_info_id']]['num'] ?? 0;
                        $row['staffBill_04'] = $remittanceBill[$one['staff_info_id']]['amount'] ?? 0;

                        $row['disability_certificate'] = $one['disability_certificate'];

                        $row['leave_type']          = '';
                        $row['leave_reason']        = '';
                        $row['leave_reason_old']    = '';
                        $row['leave_reason_remark'] = '';
                        $row['leave_scenario']      = '';

                        if ($one['state'] == BaseStaffInfo::STATE_RESIGN || $one['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_YES) {
                            if (!empty($one['leave_scenario'])) {
                                $row['leave_scenario'] = $all_leave_scenario[$one['leave_scenario']] ?? '';
                            }
                            if (!empty($one['leave_type'])) {
                                $row['leave_type'] = $this->lang->get('leave_type_' . $one['leave_type'], '', $lang);
                            }
                            //在老原因里，则从map里获取新原因进行赋值
                            if (isset($all_leave_reason_version[$one['leave_reason']]) && $all_leave_reason_version[$one['leave_reason']] != StaffLeaveReason::LEAVER_REASON_VERSION_NEW) {
                                $row['leave_reason_old'] = $all_leave_type_reason[$one['leave_reason']] ?? '';
                                $new_leave_reason        = $all_old_new_leave_reason[$one['leave_reason']] ?? 0;
                                $row['leave_reason']     = $all_leave_type_reason[$new_leave_reason] ?? $row['leave_reason_old'];
                            } else {
                                $row['leave_reason'] = $all_leave_type_reason[$one['leave_reason']] ?? '';
                            }
                            if (!isset($all_leave_reason_version[$one['leave_reason']])) {
                                $row['leave_reason_old'] = $row['leave_reason'];
                            }
                            $row['leave_reason_remark'] = $one['leave_reason_remark'];
                        }

                        $row['nick_name']   = $one['nick_name'] ?? '';
                        $row['first_name']  = $one['first_name'] ?? '';
                        $row['middle_name'] = $one['middle_name'] ?? '';
                        $row['last_name']   = $one['last_name'] ?? '';
                        $row['suffix_name'] = $one['suffix_name'] ?? '';

                        $row['graduate_school'] = $items[$one['staff_info_id']]['GRADUATE_SCHOOL'] ?? '';// 毕业学校
                        $row['major']           = $items[$one['staff_info_id']]['MAJOR'] ?? '';          //专业
                        $row['graduate_time']   = $items[$one['staff_info_id']]['GRADUATE_TIME'] ?? '';  //毕业时间

                        $education        = $items[$one['staff_info_id']]['EDUCATION'] ?? '';
                        $row['education'] = $education ? $this->lang->get('education_' . $education, '',
                            $lang) : '';//最高学历

                        $nationality        = $one['nationality'] ?: ($items[$one['staff_info_id']]['NATIONALITY'] ?? '');
                        $row['nationality'] = $nationalityList[$nationality] ?? '';//国籍1泰国2中国99其他


                        $row['birthday'] = $items[$one['staff_info_id']]['BIRTHDAY'] ?? '';                                    //生日

                        // 社保离职日期
                        $row['social_security_leave_date'] = $items[$one['staff_info_id']]['SOCIAL_SECURITY_LEAVE_DATE'] ?? '';//户口所在国家（默认1-泰国，老数据没有此字段）

                        //户口所在地信息
                        $row['register_country'] = $items[$one['staff_info_id']]['REGISTER_COUNTRY'] ?? $default_country_value;//户口所在国家（默认1-泰国，老数据没有此字段）

                        $row['register_province']      = $items[$one['staff_info_id']]['REGISTER_PROVINCE'] ?? '';               //户口所在省
                        $row['register_province_name'] = !empty($row['register_province']) ? ($row['register_country'] == $default_country_value ? ($province_list[$row['register_province']]['name'] ?? '') : $row['register_province']) : '';

                        //居住地信息
                        $row['residence_country'] = $items[$one['staff_info_id']]['RESIDENCE_COUNTRY'] ?? $default_country_value;//居住地所在国家（默认1-泰国，老数据没有此字段）

                        $row['residence_province']      = $items[$one['staff_info_id']]['RESIDENCE_PROVINCE'] ?? '';//居住地所在省
                        $row['residence_province_name'] = !empty($row['residence_province']) ? ($row['residence_country'] == $default_country_value ? ($province_list[$row['residence_province']]['name'] ?? '') : $row['residence_province']) : '';

                        $row['backup_bank_no_name'] = $items[$one['staff_info_id']]['BACKUP_BANK_NO_NAME'] ?? $one['name'];//备用卡持卡人信息
                        $row['backup_bank_no']      = $items[$one['staff_info_id']]['BACKUP_BANK_NO'] ?? '';               //备用卡银行卡号
                        //$row['backup_bank_type'] = $items[$one['staff_info_id']]['BACKUP_BANK_TYPE'] ? Yii::$app->sysconfig->all_bank_type[$items[$one['staff_info_id']]['BACKUP_BANK_TYPE']]
                        //    : '';    //备用卡银行类型
                        $row['backup_bank_type'] = '';
                        if (!empty($row['backup_bank_no']) || !empty($row['backup_bank_no_name'])) {
                            $row['backup_bank_type'] = 'BDO';
                        }
                        if (!empty($items[$one['staff_info_id']]['BACKUP_BANK_TYPE'])) {
                            $row['backup_bank_type'] = Yii::$app->sysconfig->all_bank_type()[$items[$one['staff_info_id']]['BACKUP_BANK_TYPE']];
                        }

                        if (YII_COUNTRY == 'MY') {
                            $row['register_city']      = $items[$one['staff_info_id']]['REGISTER_CITY'] ?? '';//户口所在市
                            $row['register_city_name'] = $items[$one['staff_info_id']]['REGISTER_CITY'] ?? '';//户口所在市

                            $row['register_district']      = $items[$one['staff_info_id']]['REGISTER_DISTRICT'] ?? '';//户口所在乡
                            $row['register_district_name'] = $items[$one['staff_info_id']]['REGISTER_DISTRICT'] ?? '';//户口所在乡

                            $row['residence_city']      = $items[$one['staff_info_id']]['RESIDENCE_CITY'] ?? '';//居住地所在市
                            $row['residence_city_name'] = $items[$one['staff_info_id']]['RESIDENCE_CITY'] ?? '';//居住地所在市

                            $row['residence_district']      = $items[$one['staff_info_id']]['RESIDENCE_DISTRICT'] ?? '';//居住地所在乡
                            $row['residence_district_name'] = $items[$one['staff_info_id']]['RESIDENCE_DISTRICT'] ?? '';//居住地所在乡
                        } else {
                            $row['register_city']      = $items[$one['staff_info_id']]['REGISTER_CITY'] ?? '';//户口所在市
                            $row['register_city_name'] = !empty($row['register_city']) ? ($row['register_country'] == $default_country_value ? ($city_list[$row['register_city']]['name'] ?? '') : $row['register_city']) : '';

                            $row['register_district']      = $items[$one['staff_info_id']]['REGISTER_DISTRICT'] ?? '';//户口所在乡
                            $row['register_district_name'] = !empty($row['register_district']) ? ($row['register_country'] == $default_country_value ? ($district_list[$row['register_district']]['name'] ?? '') : $row['register_district']) : '';

                            $row['residence_city']      = $items[$one['staff_info_id']]['RESIDENCE_CITY'] ?? '';//居住地所在市
                            $row['residence_city_name'] = !empty($row['residence_city']) ? ($row['residence_country'] == $default_country_value ? ($city_list[$row['residence_city']]['name'] ?? '') : $row['residence_city']) : '';

                            $row['residence_district']      = $items[$one['staff_info_id']]['RESIDENCE_DISTRICT'] ?? '';//居住地所在乡
                            $row['residence_district_name'] = !empty($row['residence_district']) ? ($row['residence_country'] == $default_country_value ? ($district_list[$row['residence_district']]['name'] ?? '') : $row['residence_district']) : '';
                        }

                        $row['register_postcodes']   = $items[$one['staff_info_id']]['REGISTER_POSTCODES'] ?? '';  //户口所在邮编
                        $row['register_house_num']   = $items[$one['staff_info_id']]['REGISTER_HOUSE_NUM'] ?? '';  //户口所在门牌号
                        $row['register_village_num'] = $items[$one['staff_info_id']]['REGISTER_VILLAGE_NUM'] ?? '';//户口所在村号
                        $row['register_village']     = $items[$one['staff_info_id']]['REGISTER_VILLAGE'] ?? '';    //户口所在村
                        $row['register_alley']       = $items[$one['staff_info_id']]['REGISTER_ALLEY'] ?? '';      //户口所在巷
                        $row['register_street']      = $items[$one['staff_info_id']]['REGISTER_STREET'] ?? '';     //户口所在街道

                        $row['residence_postcodes']   = $items[$one['staff_info_id']]['RESIDENCE_POSTCODES'] ?? '';  //居住地所在邮编
                        $row['residence_house_num']   = $items[$one['staff_info_id']]['RESIDENCE_HOUSE_NUM'] ?? '';  //居住地所在门牌号
                        $row['residence_village_num'] = $items[$one['staff_info_id']]['RESIDENCE_VILLAGE_NUM'] ?? '';//居住地所在村号
                        $row['residence_village']     = $items[$one['staff_info_id']]['RESIDENCE_VILLAGE'] ?? '';    //居住地所在村
                        $row['residence_alley']       = $items[$one['staff_info_id']]['RESIDENCE_ALLEY'] ?? '';      //居住地所在巷
                        $row['residence_street']      = $items[$one['staff_info_id']]['RESIDENCE_STREET'] ?? '';     //居住地所在的街道

                        $row['register_detail_address']  = $items[$one['staff_info_id']]['REGISTER_DETAIL_ADDRESS'] ?? '';  //户口地详情
                        $row['residence_detail_address'] = $items[$one['staff_info_id']]['RESIDENCE_DETAIL_ADDRESS'] ?? ''; //居住地详情

                        $row['dad_first_name'] = $items[$one['staff_info_id']]['DAD_FIRST_NAME'] ?? '';//父亲名
                        $row['dad_last_name']  = $items[$one['staff_info_id']]['DAD_LAST_NAME'] ?? ''; //父亲姓
                        $row['mum_first_name'] = $items[$one['staff_info_id']]['MUM_FIRST_NAME'] ?? '';//母亲名
                        $row['mum_last_name']  = $items[$one['staff_info_id']]['MUM_LAST_NAME'] ?? ''; //母亲姓
                        $row['bank_no_name']   = $items[$one['staff_info_id']]['BANK_NO_NAME'] ?? '';  // 持卡人

                        if (YII_COUNTRY == 'ID') {
                            $row['register_rt']  = $items[$one['staff_info_id']]['REGISTER_RT'] ?? ''; //户口所在地-邻组
                            $row['register_rw']  = $items[$one['staff_info_id']]['REGISTER_RW'] ?? ''; //户口所在地-居委会
                            $row['residence_rt'] = $items[$one['staff_info_id']]['RESIDENCE_RT'] ?? '';//居住地-邻组
                            $row['residence_rw'] = $items[$one['staff_info_id']]['RESIDENCE_RW'] ?? '';// 居住地-居委会

                            //户口所在地村庄特殊处理
                            if ($row['register_country'] == $default_country_value) {
                                $row['register_village_name'] = $villageListKy[$row['register_village']] ?? '';
                            } else {
                                $row['register_village_name'] = $info['residence_village'] ?? '';
                            }

                            //居住地村庄特殊处理
                            if ($row['residence_country'] == $default_country_value) {
                                $row['residence_village_name'] = $villageListKy[$row['residence_village']] ?? '';
                            } else {
                                $row['residence_village_name'] = $row['residence_village'] ?? '';
                            }
                        }

                        $relatives_relationship        = $items[$one['staff_info_id']]['RELATIVES_RELATIONSHIP'] ?? '';
                        $row['relatives_relationship'] = $relatives_relationship ? $this->lang->get('relatives_relationship_' . $relatives_relationship,
                            '', $lang) : '';//亲属关系(紧急联系人)

                        $row['relatives_first_name'] = $items[$one['staff_info_id']]['RELATIVES_FIRST_NAME'] ?? '';//亲属名(紧急联系人)
                        $row['relatives_last_name']  = $items[$one['staff_info_id']]['RELATIVES_LAST_NAME'] ?? ''; //亲属姓(紧急联系人)

                        $relatives_call_name        = $items[$one['staff_info_id']]['RELATIVES_CALL_NAME'] ?? '';
                        $row['relatives_call_name'] = $relatives_call_name ? $this->lang->get('relatives_call_name_' . $relatives_call_name,
                            '', $lang) : '';//亲属称呼(紧急联系人)

                        $row['relatives_mobile'] = $items[$one['staff_info_id']]['RELATIVES_MOBILE'] ?? '';//亲属电话(紧急联系人)

                        $is_disability          = empty($one['is_disability']) ? 0 : $one['is_disability'];
                        $row['is_disability']   = $this->lang->get('is_disability_' . $is_disability, '', $lang);
                        $working_country_id     = $items[$one['staff_info_id']]['WORKING_COUNTRY'] ?? '';
                        $row['working_country'] = $workingCountryList[$working_country_id] ?? '';

                        $row['hire_type']  = $one['hire_type'];
                        $row['hire_times'] = $one['hire_times'];
                        //正式员工不能导出 合同到期日
                        $row['contract_expiry_date']     = $one['hire_type'] == BaseStaffInfo::HIRE_TYPE_FORMAL ? '' : $one['contract_expiry_date'];
                        $row['fund_num']                 = $one['fund_num'];                                             //社保号
                        $row['social_security_num']      = $one['social_security_num'];                                  //公积金号
                        $row['medical_insurance_num_ph'] = $row['medical_insurance_num'] = $one['medical_insurance_num'];//医疗保险号
                        $race                            = $items[$one['staff_info_id']]['RACE'] ?? '';
                        $religion                        = $items[$one['staff_info_id']]['RELIGION'] ?? '';
                        $row['race']                     = empty($race) ? '' : $this->lang->get('race_' . $race, '',
                            $lang);
                        $row['religion']                 = empty($religion) ? '' : $this->lang->get('religion_' . $religion,
                            '', $lang);
                        $row['staff_province_code']      = isset($items[$one['staff_info_id']]['STAFF_PROVINCE_CODE']) ? $province_list[$items[$one['staff_info_id']]['STAFF_PROVINCE_CODE']]['name'] : ''; //工作所在州 只有马来 员工网点 为-1 时有;
                        $row['bank_branch_name']         = $items[$one['staff_info_id']]['BANK_BRANCH_NAME'] ?? '';                                                                                         //银行分行名称
                        $row['household_registration']   = ($items[$one['staff_info_id']]['HOUSEHOLD_REGISTRATION'] ?? '') . "\t";                                                                          //户籍照号
                        $ptkp_state                      = $items[$one['staff_info_id']]['PTKP_STATE'] ?? '';
                        $row['ptkp_state']               = empty($ptkp_state) ? '' : $this->lang->get('ptkp_state_' . $ptkp_state,
                            '',
                            $lang);                                                                                                                                 //PTKP状态
                        $row['tax_card']                 = isset($items[$one['staff_info_id']]['TAX_CARD']) ? $items[$one['staff_info_id']]['TAX_CARD'] . "\t" : '';//税卡号

                        $row['beneficiary_name']     = '';
                        $row['beneficiary_identity'] = '';
                        $row['beneficiary_mobile']   = '';
                        $row['beneficiary_relation'] = '';
                        if (YII_COUNTRY != 'TH' && isset($insurance_beneficiary_list[$one['staff_info_id']])) {
                            $row['beneficiary_name']     = $insurance_beneficiary_list[$one['staff_info_id']]['beneficiary_name'];
                            $row['beneficiary_identity'] = $insurance_beneficiary_list[$one['staff_info_id']]['beneficiary_identity'];
                            $row['beneficiary_mobile']   = $insurance_beneficiary_list[$one['staff_info_id']]['beneficiary_mobile'];
                            $row['beneficiary_relation'] = $insurance_beneficiary_list[$one['staff_info_id']]['beneficiary_relation_text'];
                        }

                        $export_data = [];
                        foreach ($field_arr as $key => $value) {
                            if ($value == 12) {
                                $export_data['c_level']      = $row['c_level'];
                                $export_data['company_name'] = $row['company_name'];
                                $export_data['dept_level_1'] = $row['dept_level_1'];
                                $export_data['dept_level_2'] = $row['dept_level_2'];
                                $export_data['dept_level_3'] = $row['dept_level_3'];
                                $export_data['dept_level_4'] = $row['dept_level_4'];
                            } elseif ($value == 24) {
                                if (!empty($row['week_working_day']) && !empty($row['rest_type'])) {
                                    $working_day_rest_type                = $row['week_working_day'] . $row['rest_type'];
                                    $export_data['working_day_rest_type'] = $this->lang->get('working_day_rest_type_' . $working_day_rest_type,
                                        '', $lang);
                                } else {
                                    $export_data['working_day_rest_type'] = '';
                                }
                            } elseif ($value == 58) { //permanent_address 户口所在地地址
                                if (YII_COUNTRY == 'TH') {
                                    if ($row['register_country'] == '1') {
                                        //泰国国家 户籍地址格式：
                                        if (strtolower($row['register_province']) == 'th01') {
                                            //泰国国家且省份是曼谷（后缀名不一样）
                                            //$export_data[$config_field[$value]] = "บ้านเลขที่ {$row['register_house_num']} หมู่ {$row['register_village_num']} หมูบ้าน{$row['register_village']} ซอย {$row['register_alley']} ถนน {$row['register_street']} แขวง {$row['register_district_name']} เขต {$row['register_city_name']} จังหวัด {$row['register_province_name']}";
                                            $export_data[$config_field[$value]] = $this->formatOtherPermanentAddress($row,
                                                'TH', 1);
                                        } else {
                                            //非曼谷省份后缀名不一样）
                                            //$export_data[$config_field[$value]] = "บ้านเลขที่ {$row['register_house_num']} หมู่ {$row['register_village_num']} หมูบ้าน {$row['register_village']} ซอย {$row['register_alley']} ถนน {$row['register_street']} ตำบล {$row['register_district_name']} อำเภอ {$row['register_city_name']}  จังหวัด {$row['register_province_name']}";
                                            $export_data[$config_field[$value]] = $this->formatOtherPermanentAddress($row,
                                                'TH', 2);
                                        }
                                    } else {
                                        //非泰国国家户籍地址格式：
                                        //$export_data[$config_field[$value]] = "{$row['register_house_num']} {$row['register_village_num']} {$row['register_village']} {$row['register_alley']} {$row['register_street']} {$row['register_district_name']} {$row['register_city_name']} {$row['register_province_name']}";
                                        $export_data[$config_field[$value]] = $this->formatOtherPermanentAddress($row,
                                            'TH', 3);
                                    }
                                } elseif (YII_COUNTRY == 'MY') {
                                    //$export_data[$config_field[$value]] = " {$row['register_detail_address']} {$row['register_city_name']} {$row['register_province_name']}";
                                    $export_data[$config_field[$value]] = $this->formatMyPermanentAddress($row);
                                    //} elseif(YII_COUNTRY == 'PH') {
                                    //  $export_data[$config_field[$value]] = "{$row['register_house_num']} {$row['register_village_num']} {$row['register_village']} {$row['register_alley']} {$row['register_street']} {$row['register_district_name']} {$row['register_city_name']} {$row['register_province_name']} {$row['register_postcodes']}";
                                } elseif (YII_COUNTRY == 'ID') {
                                    $export_data[$config_field[$value]] = $this->formatIdPermanentAddress($row);
                                } else {
                                    //$export_data[$config_field[$value]] = "{$row['register_house_num']} {$row['register_village_num']} {$row['register_village']} {$row['register_alley']} {$row['register_street']} {$row['register_district_name']} {$row['register_city_name']} {$row['register_province_name']}";
                                    $export_data[$config_field[$value]] = $this->formatOtherPermanentAddress($row,
                                        'OTHER', 3);
                                }
                            } elseif ($value == 59) { //present_aaddress 居住地地址
                                if (YII_COUNTRY == 'TH') {
                                    if ($row['residence_country'] == '1') {
                                        //泰国国家 户籍地址格式：
                                        if (strtolower($row['residence_province']) == 'th01') {
                                            //泰国国家且省份是曼谷（后缀名不一样）
                                            //$export_data[$config_field[$value]] = "บ้านเลขที่ {$row['residence_house_num']} หมู่ {$row['residence_village_num']} หมูบ้าน{$row['residence_village']} ซอย {$row['residence_alley']} ถนน {$row['residence_street']} แขวง {$row['residence_district_name']} เขต {$row['residence_city_name']} จังหวัด {$row['residence_province_name']}";
                                            $export_data[$config_field[$value]] = $this->formatOtherPresentAddress($row,
                                                'TH', 1);
                                        } else {
                                            //非曼谷省份后缀名不一样）
                                            //$export_data[$config_field[$value]] = "บ้านเลขที่ {$row['residence_house_num']} หมู่ {$row['residence_village_num']} หมูบ้าน {$row['residence_village']} ซอย {$row['residence_alley']} ถนน {$row['residence_street']} ตำบล {$row['residence_district_name']} อำเภอ {$row['residence_city_name']}  จังหวัด {$row['residence_province_name']}";
                                            $export_data[$config_field[$value]] = $this->formatOtherPresentAddress($row,
                                                'TH', 2);
                                        }
                                    } else {
                                        //非泰国国家户籍地址格式：
                                        //$export_data[$config_field[$value]] = "{$row['residence_house_num']} {$row['residence_village_num']} {$row['residence_village']} {$row['residence_alley']} {$row['residence_street']} {$row['residence_district_name']} {$row['residence_city_name']} {$row['residence_province_name']}";
                                        $export_data[$config_field[$value]] = $this->formatOtherPresentAddress($row,
                                            'TH', 3);
                                    }
                                } elseif (YII_COUNTRY == 'MY') {
                                    //$export_data[$config_field[$value]] = "{$row['residence_detail_address']} {$row['residence_city_name']} {$row['residence_province_name']}";
                                    $export_data[$config_field[$value]] = $this->formatMyPresentAddress($row);
                                    //} elseif(YII_COUNTRY == 'PH') {
                                    //    $export_data[$config_field[$value]] = "{$row['residence_house_num']} {$row['residence_village_num']} {$row['residence_village']} {$row['residence_alley']} {$row['residence_street']} {$row['residence_district_name']} {$row['residence_city_name']} {$row['residence_province_name']} {$row['residence_postcodes']}";
                                } elseif (YII_COUNTRY == 'ID') {
                                    $export_data[$config_field[$value]] = $this->formatIdPresentAddress($row);
                                } else {
                                    //$export_data[$config_field[$value]] = "{$row['residence_house_num']} {$row['residence_village_num']} {$row['residence_village']} {$row['residence_alley']} {$row['residence_street']} {$row['residence_district_name']} {$row['residence_city_name']} {$row['residence_province_name']}";
                                    $export_data[$config_field[$value]] = $this->formatOtherPresentAddress($row,
                                        'OTHER', 3);
                                }
                            } elseif ($value == 60) { //relatives_relationship_1父亲名、姓
                                $export_data[$config_field[$value]] = $row['dad_first_name'] . ' ' . $row['dad_last_name'];
                            } elseif ($value == 61) { //relatives_relationship_2母亲名、姓
                                $export_data[$config_field[$value]] = $row['mum_first_name'] . ' ' . $row['mum_last_name'];
                            } elseif ($value == 63) { //emergency_contact 紧急联系人称呼姓名
                                $export_data[$config_field[$value]] = $row['relatives_call_name'] . ' ' . $row['relatives_first_name'] . ' ' . $row['relatives_last_name'];
                            } elseif ($value == 73) {
                                $export_data[$config_field[$value]] = $row['hire_type'] ? $this->lang->get('hire_type_' . $row['hire_type'],
                                    '', $lang) : '';
                            } elseif ($value == 74) {
                                if (in_array($row['hire_type'], [BaseStaffInfo::HIRE_TYPE_DAILY_SALARY, BaseStaffInfo::HIRE_TYPE_HOURLY_WAGE])) {
                                    $export_data[$config_field[$value]] = $row['hire_times'] . $this->lang->get('daily',
                                            '', $lang);
                                } else {
                                    if (in_array($row['hire_type'], [
                                        BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY,
                                        BaseStaffInfo::HIRE_TYPE_UN_PAID,
                                        BaseStaffInfo::HIRE_TYPE_PART_TIME_AGENT,
                                    ])) {
                                        $export_data[$config_field[$value]] = $row['hire_times'] . $this->lang->get('monthly',
                                                '', $lang);
                                    } else {
                                        $export_data[$config_field[$value]] = '';
                                    }
                                }
                            } elseif ($value == 100) { // 户口所在地（分项）
                                if (YII_COUNTRY == 'MY') {
                                    $export_data['register_detail_address'] = $row['register_detail_address'];  // {户口所在地-详细地址}
                                    $export_data['register_city']           = $row['register_city_name'];       //$row['register_city'];            // {户口所在地-城市}
                                    $export_data['register_province_state'] = $row['register_province_name'];   //$row['register_province'];        // {户口所在地-州}
                                    $export_data['register_postcodes']      = $row['register_postcodes'];       // {户口所在地-邮编}
                                }

                                if (YII_COUNTRY == 'ID') {
                                    $export_data['register_street']       = $row['register_street'];                // 户口所在地-街道
                                    $export_data['register_rt']           = $row['register_rt'];                    // 户口所在地-邻组
                                    $export_data['register_rw']           = $row['register_rw'];                    // 户口所在地-居委会
                                    $export_data['register_village_name'] = $row['register_village_name'];          // 户口所在地-村庄
                                    $export_data['register_district']     = $row['register_district_name'];         // 户口所在地-乡
                                    $export_data['register_city']         = $row['register_city_name'];             // 户口所在地-市
                                    $export_data['register_province']     = $row['register_province_name'];         // 户口所在地-省
                                    $export_data['register_postcodes']    = $row['register_postcodes'];             // 户口所在地-邮编
                                }

                                if (YII_COUNTRY != 'MY' && YII_COUNTRY != 'ID') {
                                    $export_data['register_house_num']   = $row['register_house_num'];     // 户口所在地-门牌号
                                    $export_data['register_village_num'] = $row['register_village_num'];   // 户口所在地-村号
                                    $export_data['register_village']     = $row['register_village'];       // 户口所在地-村庄
                                    $export_data['register_alley']       = $row['register_alley'];         // 户口所在地-巷
                                    $export_data['register_street']      = $row['register_street'];        // 户口所在地-街道
                                    $export_data['register_district']    = $row['register_district_name']; //$row['register_district'];    // 户口所在地-乡
                                    $export_data['register_city']        = $row['register_city_name'];     //$row['register_city'];        // 户口所在地-市
                                    $export_data['register_province']    = $row['register_province_name']; //$row['register_province'];    // 户口所在地-省
                                    $export_data['register_postcodes']   = $row['register_postcodes'];     // 户口所在地-邮编
                                }

                                if (YII_COUNTRY == 'TH') {
                                    if (!empty($export_data['register_province'])) {
                                        $export_data['register_province'] = 'จังหวัด ' . $export_data['register_province']; // 户口所在地-省
                                    }

                                    $prefixCity     = 'อำเภอ'; // 非曼谷市区
                                    $prefixDistrict = 'ตำบล';  // 非曼谷乡
                                    if (strtolower($row['register_province']) == 'th01') {
                                        //泰国国家且省份是曼谷（后缀名不一样）
                                        $prefixCity     = "เขต";
                                        $prefixDistrict = "แขวง";
                                    }

                                    if (!empty($export_data['register_city'])) {
                                        $export_data['register_city'] = $prefixCity . " " . $export_data['register_city']; // 户口所在地-市
                                    }

                                    if (!empty($export_data['register_district'])) {
                                        $export_data['register_district'] = $prefixDistrict . " " . $export_data['register_district']; //户口所在地-乡
                                    }

                                    if (!empty($export_data['register_street'])) {
                                        $export_data['register_street'] = "ถนน " . $export_data['register_street']; // 户口所在地-街道
                                    }

                                    if (!empty($export_data['register_alley'])) {
                                        $export_data['register_alley'] = "ซอย " . $export_data['register_alley']; // 户口所在地-巷
                                    }

                                    if (!empty($export_data['register_village'])) {
                                        $export_data['register_village'] = "หมู่บ้าน " . $export_data['register_village']; // 户口所在地-村庄
                                    }

                                    if (!empty($export_data['register_village_num'])) {
                                        $export_data['register_village_num'] = "หมู่ " . $export_data['register_village_num']; // 户口所在地-村号
                                    }

                                    if (!empty($export_data['register_house_num'])) {
                                        $export_data['register_house_num'] = "บ้านเลขที่ " . $export_data['register_house_num']; // 户口所在地-门牌号
                                    }

                                    if (!empty($export_data['register_postcodes'])) {
                                        $export_data['register_postcodes'] = "รหัสไปรษณีย์ " . $export_data['register_postcodes']; // 户口所在地-邮编
                                    }
                                }
                            } elseif ($value == 101) { // 居住所在地地址(分项)
                                if (YII_COUNTRY == 'MY') {
                                    $export_data['residence_detail_address'] = $row['residence_detail_address']; // {居住所在地-详细地址}
                                    $export_data['residence_city']           = $row['residence_city_name'];      //$row['residence_city'];           // {居住所在地-城市}
                                    $export_data['residence_province_state'] = $row['residence_province_name'];  //$row['residence_province'];       // {居住所在地-州}
                                    $export_data['residence_postcodes']      = $row['residence_postcodes'];      // {居住所在地-邮编}
                                }

                                if (YII_COUNTRY == 'ID') {
                                    $export_data['residence_street']       = $row['residence_street'];           // 居住所在地-街道
                                    $export_data['residence_rt']           = $row['residence_rt'];               // 居住所在地-邻组
                                    $export_data['residence_rw']           = $row['residence_rw'];               // 居住所在地-居委会
                                    $export_data['residence_village_name'] = $row['residence_village_name'];     // 居住所在地-村庄
                                    $export_data['residence_district']     = $row['residence_district_name'];    // 居住所在地-乡
                                    $export_data['residence_city']         = $row['residence_city_name'];        // 居住所在地-市
                                    $export_data['residence_province']     = $row['residence_province_name'];    // 居住所在地-省
                                    $export_data['residence_postcodes']    = $row['residence_postcodes'];        // 居住所在地-邮编
                                }

                                if (YII_COUNTRY != 'MY' && YII_COUNTRY != 'ID') {
                                    $export_data['residence_house_num']   = $row['residence_house_num'];     // 居住所在地-门牌号
                                    $export_data['residence_village_num'] = $row['residence_village_num'];   // 居住所在地-村号
                                    $export_data['residence_village']     = $row['residence_village'];       // 居住所在地-村庄
                                    $export_data['residence_alley']       = $row['residence_alley'];         // 居住所在地-巷
                                    $export_data['residence_street']      = $row['residence_street'];        // 居住所在地-街道
                                    $export_data['residence_district']    = $row['residence_district_name']; //$row['residence_district'];   // 居住所在地-乡
                                    $export_data['residence_city']        = $row['residence_city_name'];     //$row['residence_city'];       // 居住所在地-市
                                    $export_data['residence_province']    = $row['residence_province_name']; //$row['residence_province'];   // 居住所在地-省
                                    $export_data['residence_postcodes']   = $row['residence_postcodes'];     // 居住所在地-邮编
                                }

                                if (YII_COUNTRY == 'TH') {
                                    if (!empty($export_data['residence_province'])) {
                                        $export_data['residence_province'] = 'จังหวัด ' . $export_data['residence_province']; // 居住所在地-省
                                    }

                                    $prefixCity     = 'อำเภอ'; // 非曼谷市区
                                    $prefixDistrict = 'ตำบล';  // 非曼谷乡
                                    if (strtolower($row['residence_province']) == 'th01') {
                                        //泰国国家且省份是曼谷（后缀名不一样）
                                        $prefixCity     = "เขต";
                                        $prefixDistrict = "แขวง";
                                    }

                                    if (!empty($export_data['residence_city'])) {
                                        $export_data['residence_city'] = $prefixCity . ' ' . $export_data['residence_city'];// 居住所在地-市
                                    }

                                    if (!empty($export_data['residence_district'])) {
                                        $export_data['residence_district'] = $prefixDistrict . ' ' . $export_data['residence_district'];  // 居住所在地-乡
                                    }

                                    if (!empty($export_data['residence_street'])) {
                                        $export_data['residence_street'] = "ถนน " . $export_data['residence_street']; // 居住所在地-街道
                                    }

                                    if (!empty($export_data['residence_alley'])) {
                                        $export_data['residence_alley'] = "ซอย " . $export_data['residence_alley']; // 居住所在地-巷
                                    }

                                    if (!empty($export_data['residence_village'])) {
                                        $export_data['residence_village'] = "หมู่บ้าน " . $export_data['residence_village']; // 居住所在地-村庄
                                    }

                                    if (!empty($export_data['residence_village_num'])) {
                                        $export_data['residence_village_num'] = "หมู่ " . $export_data['residence_village_num']; // 居住所在地-村号
                                    }

                                    if (!empty($export_data['residence_house_num'])) {
                                        $export_data['residence_house_num'] = "บ้านเลขที่ " . $export_data['residence_house_num']; //  居住所在地-门牌号
                                    }

                                    if (!empty($export_data['residence_postcodes'])) {
                                        $export_data['residence_postcodes'] = "รหัสไปรษณีย์ " . $export_data['residence_postcodes']; // 居住所在地-邮编
                                    }
                                }
                            } elseif ($value == 102) { // backup_bank_card_audit_state 备用银行账号审核状态 PH
                                $export_data[$config_field[$value]] = "";
                                $staffId                            = (int)$one['staff_info_id'];

                                $card_type = HrStaffAnnexInfo::TYPE_BACKUP_BANK_CARD;
                                if (isset($annexList[$staffId][$card_type]) && is_numeric($annexList[$staffId][$card_type]['audit_state'])) {
                                    $export_data[$config_field[$value]] = $this->lang->get('bank_card_audit_state_' . $annexList[$staffId][$card_type]['audit_state'],
                                        '', $lang);
                                }

                                // 附件表如果不存在BACKUP_BANK_NO字段 或者 存在 且 为"" 都为待上传
                                // todo hcm后台可以清空卡号，同时状态标记为空，表示为待上传
                                if (empty($row['backup_bank_no'])) {
                                    $export_data[$config_field[$value]] = $this->lang->get('bank_card_audit_state_999',
                                        '',
                                        $lang);
                                }
                            } elseif (in_array($value, [105, 106, 107, 108])) {
                                $staffId                            = (int)$one['staff_info_id'];
                                $type                               = [
                                    105 => HrStaffAnnexInfo::TYPE_SOCIAL_SECURITY,
                                    // 社保号审核状态
                                    106 => HrStaffAnnexInfo::TYPE_FUND,
                                    // 公积金号审核状态
                                    107 => HrStaffAnnexInfo::TYPE_MEDICAL_INSURANCE,
                                    // 医疗保险号 审核状态
                                    108 => HrStaffAnnexInfo::TYPE_TAX_CARD,
                                    // 税卡号审核状态
                                ];
                                $card_type                          = $type[$value];
                                if (isset($annexList[$staffId][$card_type]) && is_numeric($annexList[$staffId][$card_type]['audit_state'])) {
                                    $export_data[$config_field[$value]] = $this->lang->get('bank_card_audit_state_' . $annexList[$staffId][$card_type]['audit_state'],
                                        '', $lang);
                                } else {
                                    $export_data[$config_field[$value]] = $this->lang->get('bank_card_audit_state_999',
                                        '',
                                        $lang);
                                }
                            } elseif ($value == 103) { // bank_card_audit_state 银行账号审核状态
                                $staffId                            = (int)$one['staff_info_id'];
                                // 产品说 之前优先根据银行卡号判断逻辑 可以删掉了
                                if (isset($annexList[$staffId][HrStaffAnnexInfo::TYPE_BANK_CARD]['audit_state'])) {
                                    $export_data[$config_field[$value]] = $this->lang->get('bank_card_audit_state_' . $annexList[$staffId][HrStaffAnnexInfo::TYPE_BANK_CARD]['audit_state'],
                                        '', $lang);;
                                } else {
                                    $export_data[$config_field[$value]] = $this->lang->get('bank_card_audit_state_999',
                                        '', $lang);
                                }
                            } elseif ($value == SysConfig::RESIDENCE_BOOKLET_AUDIT) { // 户口簿审核状态
                                $staffId                            = (int)$one['staff_info_id'];
                                $audit_state                        = isset($annexList[$staffId][HrStaffAnnexInfo::TYPE_RESIDENCE_BOOKLET_CARD]) && !is_null($annexList[$staffId][HrStaffAnnexInfo::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state']) ? $annexList[$staffId][HrStaffAnnexInfo::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state'] : HrStaffAnnexInfo::AUDIT_STATE_TO_BE_UPLOAD;
                                $export_data[$config_field[$value]] = $this->lang->get(HrStaffAnnexInfo::$audit_state_key[$audit_state],
                                    '', $lang);
                            } elseif ($value == SysConfig::POSITION_TYPE) {//职位性质
                                $positionText = '';
                                $positionKey  = $one['node_department_id'] . '_' . $one['job_title'];
                                if (isset($jobDepartmentToPositionCost[$positionKey]) && !empty($jobDepartmentToPositionCost[$positionKey]['position_type'])) {
                                    $positionText = isset($positionList[$jobDepartmentToPositionCost[$positionKey]['position_type']]) ? $positionList[$jobDepartmentToPositionCost[$positionKey]['position_type']] : '';
                                }

                                $export_data[$config_field[$value]] = $positionText;
                            } elseif ($value == SysConfig::COST_TYPE) {//成本类型
                                $costText = '';
                                $costKey  = $one['node_department_id'] . '_' . $one['job_title'];
                                if (isset($jobDepartmentToPositionCost[$costKey]) && !empty($jobDepartmentToPositionCost[$costKey]['cost_type'])) {
                                    $costText = isset($costList[$jobDepartmentToPositionCost[$costKey]['cost_type']]) ? $costList[$jobDepartmentToPositionCost[$costKey]['cost_type']] : '';
                                }
                                $export_data[$config_field[$value]] = $costText;
                            } elseif($value == SysConfig::EXPORT_FIELD_AGE){
                                 //年龄
                                $export_data[$config_field[$value]] = $row['birthday'] ? calculate_age($row['birthday']) : null;
                            } elseif(isCountry(['MY','TH']) && $value == SysConfig::EXPORT_FIELD_CONTRACT_COMPANY){
                                //合同公司
                                $export_data[$config_field[$value]] = $contractCompanyMap[$row['contract_company_id']] ?? null;
                            } else {
                                $export_data[$config_field[$value]] = $row[$config_field[$value]];
                            }
                        }

                        $isContinue = true;
                        foreach ($export_data as $key => $val) {
                            if (!empty($val)) {
                                $isContinue = false;
                                break;
                            }
                        }
                        if ($isContinue) {
                            continue;
                        }

                        $excel_data[] = array_values($export_data);
                    }
                    Yii::$app->logger->write_log( '子进程 '.$p.' 数据量 ' . count($excel_data), 'info');
                    $this->writeFile($path,$excel_data);
                }

                $worker->write('1');
            }, false);//true  配置后 让线程打印内容输出到管道
            $pid              = $process->start();
            $processArr[$pid] = $process;
        }
        $all = [];
        foreach ($processArr as $process) {
            $all[] = $process->read();
        }

        if (array_sum($all) !== $workerNum) {
            unlink($path);
            return false;
        }

        try {
            //重置数据库链接
            $this->dbClose();
            $hcmExcelTask = HcmExcelTask::find()->where(['id' => $hcm_excel_task_id])->one();

            $ossObject = 'hris/' . date('Y-m') . '/' . $hcmExcelTask->file_name;
            Yii::$app->FlashOss->uploadFile($ossObject, $path);
            unlink($path);

            $path = $ossObject;
            Yii::$app->logger->write_log('actionExcelV6  flash_员工下载任务，生成文件完成:' . $path . ' ，参数：' . json_encode($args_arr),
                'info');

            if ($header_from == 'fbi') {
                $exportManage = ExportManage::find()->where(['staff_id'    => $hcmExcelTask->staff_info_id,
                                                             'module_code' => $hcmExcelTask->action_name . '_' . $hcmExcelTask->id,
                ])->one();
                if ($exportManage) {
                    $exportManage->file_path = Yii::$app->FlashOss->signUrl($path, 86400 * 20);
                    $exportManage->state     = ExportManage::STATUS_SUCCESS;
                    $exportManage->save();
                    $hcmExcelTask->is_delete = 1;
                }
            }
            $hcmExcelTask->path      = $path;
            $hcmExcelTask->status    = 1;
            $hcmExcelTask->finish_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
            return $hcmExcelTask->save();
        } catch (\Exception $e) {
            unlink($path);
            throw $e;
        }
        $this->echo('end');

        //回收子进程
        while ($res = swoole_process::wait()) {
            var_dump($res);
        }
    }

    private function writeFile($path,$data)
    {
        $this->stream($path, $data);
    }


    private function addFileBom($output,$data)
    {
        $fp = fopen($output, 'w');
        fwrite($fp, $bom = (chr(0xEF) . chr(0xBB) . chr(0xBF)));
        foreach ($data as $key => $value) {
            $value = $this->processNumber($value);
            fputcsv($fp, $value, ",");
        }
        fclose($fp);
    }

    private function stream($output, $data)
    {
        $fp = fopen($output, 'a');
        if (flock($fp, LOCK_EX)) {  // 排他锁
            foreach ($data as $key => $value) {
                $value = $this->processNumber($value);
                fputcsv($fp, $value, ",");
            }
            flock($fp, LOCK_UN);  // 释放锁
        }
        fclose($fp);
    }

    private function processNumber($row)
    {
        return array_map(function ($v) {
            if (is_numeric($v) && $v[0] == '0') {
                $v = '="' . $v . '"';
            }
            return $v;
        }, $row);
    }

    private function dbClose()
    {
        Yii::$app->backyard_main->close();
        Yii::$app->db_ddl->close();
        Yii::$app->fle_ads->close();
        Yii::$app->fle->close();
        Yii::$app->backyard->close();
        Yii::$app->r_backyard->close();
        Yii::$app->roa->close();
        Yii::$app->rbi->close();
        Yii::$app->cache->redis->close();
    }

    /**
     * 处理hcm内生成的合作商员工下载任务
     * @param $args
     * @param $hcm_excel_task_id
     * @return bool
     */
    public function actionExcelPartner($args, $hcm_excel_task_id)
    {
        $this->echo('actionExcelPartner begin');
        try {
            $args_arr    = json_decode($args, true);
            $lang        = $args_arr['data']['lang'] ?? 'en';
            $header_from = $args_arr['data']['header_from'] ?? '';
            Yii::$app->logger->write_log('actionExcelPartner :flash_合作商员工下载任务，开始，参数：' . json_encode($args_arr),
                'info');
            $storeTemp = SysStoreTemp::temp();
            $order     = ['id' => SORT_DESC];
            if (YII_COUNTRY == 'PH') {
                $order = ['hr_staff_info.hire_date' => SORT_DESC];
            }
            $all_origins = StaffService::getInstance()->getStaffListQueryObject($args_arr['data'])->orderBy($order)->indexBy('staff_info_id')->all(Yii::$app->get('r_backyard'));
            $this->echo('actionExcelPartner :flash_合作商员工下载任务 -  args:' . json_encode($args_arr) . ' - 总条数:' . count($all_origins));
            $items    = [];
            $staffids = array_keys($all_origins);

            if (!empty($staffids)) {
                $postions = HrStaffInfoPosition::find()
                    ->select(['staff_info_id', 'position_category'])
                    ->where(['IN', 'staff_info_id', $staffids])
                    ->orderBy('staff_info_id')
                    ->asArray()
                    ->all(Yii::$app->get('r_backyard'));
                foreach ($postions as $key => $pos) {
                    $staffPos[$pos['staff_info_id']][] = $this->lang->get('role_' . $pos['position_category'], '',
                        $lang);
                }
                $staff_items_list = StaffItems::find()->where(['staff_info_id' => $staffids])->all(Yii::$app->get('r_backyard'));
                foreach ($staff_items_list as $key => $obj) {
                    $items[$obj->staff_info_id][$obj->item] = $obj->value;
                }
            }
            foreach ($all_origins as $one) {
                $row['staff_info_id']  = $one['staff_info_id'] ?? '';                                      //员工工号
                $row['name']           = $one['name'] ?? '';                                               //姓名
                $row['mobile']         = ' ' . ($one['mobile'] ?? '') . "\t";                              //手机号
                $row['sys_store_id']   = $storeTemp[$one['sys_store_id']]['name'] ?? $one['sys_store_id']; //所属网点
                $row['roles']          = isset($staffPos[$one['staff_info_id']]) ? implode(',',
                    $staffPos[$one['staff_info_id']]) : '';                                                //角色
                $row['car_type']       = $items[$one['staff_info_id']]['CAR_TYPE'] ?? '';                  //车辆类型
                $row['partner_formal'] = $this->lang->get('partner_formal_' . $one['formal'], '', $lang);   //合作商类型
                $row['partner_state']  = $this->lang->get('partner_state_' . $one['state'], '', $lang);    //状态
                $row['hire_date']      = (date('Y-m-d', strtotime($one['hire_date'])));                    //入职时间
                $row['leave_date']     = $one['state'] == BaseStaffInfo::STATE_RESIGN && $one['leave_date'] ? (date('Y-m-d',
                    strtotime($one['leave_date']))) : '';                                                  //离职时间
                $arr[]                 = array_values($row);
            }

            if (empty($arr)) {
                $arr[] = ['no data!'];
            }
            $header[] = $this->lang->get('staff_info_id', '', $lang);
            $header[] = $this->lang->get('excel_staff_name', '', $lang);
            $header[] = $this->lang->get('excel_mobile_number', '', $lang);
            $header[] = $this->lang->get('sys_store_id', '', $lang);
            $header[] = $this->lang->get('role', '', $lang);
            $header[] = $this->lang->get('car_type', '', $lang);
            $header[] = $this->lang->get('cooperator_type', '', $lang);
            $header[] = $this->lang->get('status', '', $lang);
            $header[] = $this->lang->get('active_date', '', $lang);
            $header[] = $this->lang->get('expiration_date', '', $lang);


            $this->echo(gmdate('Y-m-d H:i:s', time() + 8 * 3600) . '生成文件开始' . '总数量：' . count($arr ?? []));

            $path = Yii::$app->csv->filePutAndUploadOss(array_merge([$header], $arr), $args_arr['file_name']);
            Yii::$app->logger->write_log('actionExcelPartner  flash_合作商员工下载任务，生成文件完成:' . $path . ' ，参数：' . json_encode($args_arr),
                'info');

            $hcmExcelTask = HcmExcelTask::find()->where(['id' => $hcm_excel_task_id])->one();
            if ($header_from == 'fbi') {
                $exportManage = ExportManage::find()->where([
                    'staff_id'    => $hcmExcelTask->staff_info_id,
                    'module_code' => $hcmExcelTask->action_name . '_' . $hcmExcelTask->id,
                ])->one();
                if ($exportManage) {
                    $exportManage->file_path = Yii::$app->FlashOss->signUrl($path, 86400 * 20);
                    $exportManage->state     = ExportManage::STATUS_SUCCESS;
                    $exportManage->save();
                    $hcmExcelTask->is_delete = 1;
                }
            }

            $hcmExcelTask->path      = $path;
            $hcmExcelTask->status    = 1;
            $hcmExcelTask->finish_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
            return $hcmExcelTask->save();
        } catch (\Exception $e) {
            $this->echo('error:' . $e->getMessage() . ';行号:' . $e->getLine() . '---' . $e->getMessage() . '----' . $e->getTraceAsString());
            Yii::$app->logger->write_log('actionExcelPartner  flash_合作商员工下载任务，可能出现的问题' . $e->getMessage() . ';行号:' . $e->getLine() . '---' . $e->getFile());
        }
        $this->echo('end');
    }
    




    /**
     * 使用 yield 优化的员工导出方法
     * @param $args
     * @param $hcm_excel_task_id
     * @return bool|void
     */
    public function actionExcelV6WithYield($args, $hcm_excel_task_id)
    {
        $this->echo('v6 with yield begin '.$hcm_excel_task_id);

        try {
            $args = is_json($args) ? json_decode($args, true) : json_decode(base64_decode($args), true);
            $args_arr = $args;
            $lang = $args_arr['date']['lang'] ?? 'en';
            $fileName = Yii::$app->params['csv_file_name_prefix'] . $args_arr['file_name'];
            $path = Yii::$app->basePath . '/web/excel/' . $fileName;

            // 获取配置
            $config_field = Yii::$app->sysconfig->export_field_all;
            $field_arr = $args_arr['date']['export_field'];
            $header = $this->getExcelHeader($field_arr, $lang);

            // 写入表头
            $this->addFileBom($path, [$header]);

            $total_processed = 0;
            $start_time = microtime(true);

            // 使用生成器逐行处理数据
            foreach ($this->generateStaffExportData($args_arr, $field_arr, $config_field, $lang) as $row_data) {
                // 直接写入文件，不在内存中累积
                $this->streamWriteRow($path, $row_data);
                $total_processed++;

                // 每处理1000条记录记录一次进度
                if ($total_processed % 1000 === 0) {
                    $current_memory = memory_get_usage(true);
                    $memory_mb = round($current_memory / 1024 / 1024, 2);
                    $elapsed = microtime(true) - $start_time;
                    $speed = round($total_processed / $elapsed, 2);

                    Yii::$app->logger->write_log("已处理 {$total_processed} 条记录，内存使用: {$memory_mb}MB，速度: {$speed} 条/秒", 'info');

                    // 执行垃圾回收
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                }
            }

            // 上传到OSS
            $hcmExcelTask = HcmExcelTask::find()->where(['id' => $hcm_excel_task_id])->one();
            $ossObject = 'hris/' . date('Y-m') . '/' . $hcmExcelTask->file_name;
            Yii::$app->FlashOss->uploadFile($ossObject, $path);
            unlink($path);

            $hcmExcelTask->path = $ossObject;
            $hcmExcelTask->status = 1;
            $hcmExcelTask->finish_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);

            $end_memory = memory_get_peak_usage(true);
            $total_time = microtime(true) - $start_time;

            Yii::$app->logger->write_log("导出完成，总处理 {$total_processed} 条记录，耗时: " . round($total_time, 2) . "秒，峰值内存: " . round($end_memory/1024/1024, 2) . "MB", 'info');

            return $hcmExcelTask->save();

        } catch (\Exception $e) {
            Yii::$app->logger->write_log('导出异常: ' . $e->getMessage() . ', 行号: ' . $e->getLine(), 'error');
            if (file_exists($path)) {
                unlink($path);
            }
            throw $e;
        }
    }
}
