<?php

namespace app\commands;

use app\modules\v1\business\Staff;

use Yii;
use Exception;

class StaffUpdateController extends RocketMqBaseController
{
    protected $tq = 'update-staff-info';


    protected function processOneMsg($msgBody)
    {
        $params = $this->getJsonMessageData($msgBody);
        try {

            Yii::$app->logger->write_log(['processOneMsgParams' => $params],'info');
            $params = json_decode($params['jsonCondition'], true);

            Yii::$app->logger->write_log(['StaffUpdateController' => $params],'info');
            if(empty($params)){
                return false;
            }
            if(empty($params['staff_info_id'])){
                return false;
            }
            $staff_info_id = $params['staff_info_id'];

            $attributes = [];

            if (isset($params['state'])) {
                $attributes['state'] = $params['state'];
            }
            if (isset($params['leave_date'])) {
                $attributes['leave_date'] = $params['leave_date'];
            }
            if (isset($params['leave_source'])) {
                $attributes['leave_source'] = $params['leave_source'];
            }
            if (isset($params['leave_reason'])) {
                $attributes['leave_reason'] = $params['leave_reason'];
            }
            if (isset($params['leave_type'])) {
                $attributes['leave_type'] = $params['leave_type'];
            }
            if (isset($params['operator'])) {
                $operator = $params['operator'];
            }
            if (isset($params['mobile_company'])) {
                $attributes['mobile_company'] = $params['mobile_company'];
            }
            if (isset($params['name'])) {
                $attributes['name'] = $params['name'];
            }
            if (isset($params['is_edit_company'])) {
                $attributes['is_edit_company'] = $params['is_edit_company'];
            }

            if (isset($params['email'])) {
                $attributes['email'] = $params['email'];
            }

            if (empty($attributes)) {
                return false;
            }
            $result = Staff::updateItems($staff_info_id,$attributes,$operator??-1);
            Yii::$app->logger->write_log(['staff_info_id' => $staff_info_id,'attributes' => $attributes,'result' => $result],'info');
            if($result === true){
                return true;
            }
            Yii::$app->logger->write_log(['staff_info_id' => $staff_info_id,'attributes' => $attributes,'result' => $result]);
            return false;
        }catch (Exception $e){
            var_dump($e->getMessage());
            Yii::$app->logger->write_log(['params'=>$params,'exception'=>$e->getMessage()]);
        }
        return false;
    }


}
