<?php

namespace app\commands;

use Yii;

class TranslationController extends ConsoleController
{
    public function actionPull() {
        try{
            $this->echo('start '.date('Y-m-d H:i:s',time()));
            //$phpPath = __DIR__ .'/messages';
            $phpPath = realpath(dirname(__FILE__).'/../') .'/messages';
            if (!file_exists($phpPath)){
                mkdir($phpPath, 0777, true);
            }
            $langs = ['en','th','zh-CN','vi', 'id'];
            foreach ($langs as $lang) {
                $i18n = 'https://ard-static.flashexpress.com';
                $url = sprintf($i18n."/hris-api/lang/%s.json",substr($lang,0,2));
                $json_data = file_get_contents($url);
                $langData = json_decode($json_data, true);
                if (is_array($langData) ) {
                    $phpContent = '<?php return ' . var_export($langData, true) . ';';
                    file_put_contents("{$phpPath}/{$lang}.php", $phpContent);
                }
            }
            $this->echo('done  '.date('Y-m-d H:i:s',time()));
        }catch (\Exception $e){
            $this->echo('error:' . $e->getMessage());
        }
    }
    
}