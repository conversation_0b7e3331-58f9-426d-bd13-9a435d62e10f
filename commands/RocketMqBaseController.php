<?php

namespace app\commands;

use app\libs\RocketMQ;
use Yii;


abstract class RocketMqBaseController extends ConsoleController
{
    //子类去实现,这个接口,处理消息主逻辑 true表示成功,可以删除消息了
    abstract protected function processOneMsg($msgBody);

    protected $tq;

    protected $consumeOnceLimit = 3;

    public function init()
    {
        if (empty($this->tq)) {
            exit("please check topic groupID");
        }
        parent::init();
    }

    //子类去实现,这个接口,是否需要退出
    protected function beforeGetMsg()
    {
        //凌晨时,强制退出,为了每天的日志分隔 和 避免内存泄漏
        if (gmdate('H:i', time() + TIME_ADD_HOUR * 3600) == '00:01') {
            sleep(10);
            return true;
        }
        return false;
    }

    /**
     * 解密Base64消息体
     * @param string $msgBody
     * @return false|array
     */
    protected function getBase64MessageData(string $msgBody)
    {
        $data = json_decode(base64_decode($msgBody), true);
        if (!isset($data['data'])) {
            return false;
        }

        unset($msgBody);
        return $data ?? [];
    }


    /**
     * 解密Json消息体
     * @param string $msgBody
     * @return false|array
     */
    protected function getJsonMessageData(string $msgBody)
    {
        $data = json_decode($msgBody, true);
        if(empty($data)){
            return false;
        }
        return $data;
    }

    public function actionMain()
    {
        $client   = (new RocketMQ($this->tq));
        $consumer = $client->getConsumer();
        pcntl_signal_dispatch();

        $waitSeconds = 20;
        if (get_runtime() == 'dev') {
            $waitSeconds = 3;
        }

        try {
            $messages = $consumer->consumeMessage(
                $this->consumeOnceLimit, // 一次最多消费3条(最多可设置为16条)
                $waitSeconds                       // 长轮询时间3秒（最多可设置为30秒）
            );
        } catch (\Exception $e) {
            if ($e instanceof \MQ\Exception\MessageNotExistException) {//没有消息可以消费
                return;
            }
            Yii::$app->logger->write_log('instanceId:' . $client->getInstanceId() . ' topic:' . $client->getTopic() . ' groupId:' . $client->getGroupId() . ' Mq Exception : ' . $e->getMessage(),
                'error');
            return;
        }
        $receiptHandles = [];
        foreach ($messages as $message) {
            $msgBody    = $message->getMessageBody();
            $processSuc = $this->processOneMsg($msgBody);
            if ($processSuc) {
                $receiptHandles[] = $message->getReceiptHandle();
                Yii::$app->logger->write_log('processOneMsg success' . json_encode($message->getReceiptHandle()), 'info');
            } else {
                Yii::$app->logger->write_log('processOneMsg failed', 'error');
            }
        }
        unset($messages);
        if (empty($receiptHandles)) {
            return;
        }

        try {
            $consumer->ackMessage($receiptHandles);
            Yii::$app->logger->write_log('consumer ack Succeed!' . json_encode($receiptHandles), 'info');
            unset($receiptHandles);
        } catch (\Exception $e) {
            if ($e instanceof \MQ\Exception\AckMessageException) {
                Yii::$app->logger->write_log('Ack Error, RequestId:' . $e->getRequestId(), 'error');
                foreach ($e->getAckMessageErrorItems() as $errorItem) {
                    Yii::$app->logger->write_log(sprintf('ReceiptHandle:%s, ErrorCode:%s, ErrorMsg:%s',
                        $errorItem->getReceiptHandle(), $errorItem->getErrorCode(), $errorItem->getErrorCode()),
                        'error');
                }
            } else {
                Yii::$app->logger->write_log('Ack Exception : ' . $e->getMessage(), 'error');
            }
        }
        //主动释放内存
        gc_collect_cycles();
        Yii::$app->logger->write_log('MQend', 'info');
    }

    protected function initSignalHandler()
    {
        declare(ticks=1);
        // 注册
        pcntl_signal(SIGHUP, [$this, 'signalHandler']);
        pcntl_signal(SIGINT, [$this, 'signalHandler']);
        pcntl_signal(SIGTERM, [$this, 'signalHandler']);
    }

    /**
     * @param $signo
     */
    private function signalHandler($signo)
    {
        switch ($signo) {
            case SIGHUP:
            case SIGINT:
            case SIGTERM:
                exit(PHP_EOL . 'exit');
                break;
            default:
                // 处理所有其他信号
                break;
        }
    }

}