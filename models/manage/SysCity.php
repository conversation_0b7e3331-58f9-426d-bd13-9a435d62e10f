<?php

namespace app\models\manage;

use Yii;

/**
 * This is the model class for table "sys_city".
 *
 * @property int $id
 * @property string $code 编号
 * @property string $name 名称
 * @property string $en_name 英文名称
 * @property string $province_code 一级行政区划编号
 * @property string $geography_code 大区行政区划编号
 * @property string $country_code 国家编号
 * @property string $store_id 所属网点名称
 * @property string $appoint_district_code 指定三级行政区划
 * @property int $deleted 是否已经删除 0:否 1:是
 * @property string $created_at
 * @property string $updated_at
 */
class SysCity extends \yii\db\ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('r_backyard');
    }


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sys_city';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['code', 'province_code', 'country_code'], 'required'],
            [['deleted'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['code'], 'string', 'max' => 6],
            [['name', 'en_name'], 'string', 'max' => 150],
            [['province_code'], 'string', 'max' => 4],
            [['geography_code'], 'string', 'max' => 1],
            [['country_code'], 'string', 'max' => 2],
            [['store_id'], 'string', 'max' => 10],
            [['appoint_district_code'], 'string', 'max' => 8],
            [['code', 'province_code', 'country_code'], 'unique', 'targetAttribute' => ['code', 'province_code', 'country_code']],
            [['code'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'code' => 'Code',
            'name' => 'Name',
            'en_name' => 'En Name',
            'province_code' => 'Province Code',
            'geography_code' => 'Geography Code',
            'country_code' => 'Country Code',
            'store_id' => 'Store ID',
            'appoint_district_code' => 'Appoint District Code',
            'deleted' => 'Deleted',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
