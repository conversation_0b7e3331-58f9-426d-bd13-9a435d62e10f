<?php

namespace app\models\manage;

use Yii;

/**
 * This is the model class for table "hr_depeartment_job_title".
 *
 * @property int $id 部门职位对应表
 * @property int $sys_depeartment_id 部门ID
 * @property int $job_title_id 职位ID
 * @property string $created_at
 */
class HrDepeartmentJobTitle extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'hr_depeartment_job_title';
    }
    public static function getDb()
    {
        return Yii::$app->get('backyard_main');
    }
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sys_depeartment_id', 'job_title_id'], 'required'],
            [['sys_depeartment_id', 'job_title_id'], 'integer'],
            [['created_at'], 'safe'],
            [['sys_depeartment_id', 'job_title_id'], 'unique', 'targetAttribute' => ['sys_depeartment_id', 'job_title_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', '部门职位对应表'),
            'sys_depeartment_id' => Yii::t('app', '部门ID'),
            'job_title_id' => Yii::t('app', '职位ID'),
            'created_at' => Yii::t('app', 'Created At'),
        ];
    }
}
