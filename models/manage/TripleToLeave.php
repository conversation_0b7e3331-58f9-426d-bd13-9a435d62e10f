<?php

namespace app\models\manage;

use Yii;

/**
 * This is the model class for table "triple_to_leave".
 *
 * @property int $id
 * @property int $staff_info_id 员工ID
 * @property string $stop_begin_date 连续旷工的开始日期
 * @property string $stop_duties_date 停职日期
 * @property string $leave_date 离职日期
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property int $type 1:连续旷工 2:未提交公款
 * @property int $stop_state 停职状态 1 已停职 2 停职请求失败
 * @property int $hold_state hold状态 1 已hold 2 hold请求失败
 */
class TripleToLeave extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'triple_to_leave';
    }

    public static function getDb()
    {
        return Yii::$app->get('backyard_main');
    }
    
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['staff_info_id', 'type', 'stop_state', 'hold_state'], 'integer'],
            [['stop_begin_date', 'stop_duties_date', 'leave_date', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'staff_info_id' => 'Staff Info ID',
            'stop_begin_date' => 'Stop Begin Date',
            'stop_duties_date' => 'Stop Duties Date',
            'leave_date' => 'Leave Date',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'type' => 'Type',
            'stop_state' => 'Stop State',
            'hold_state' => 'Hold State',
        ];
    }
}
