<?php

namespace app\models\manage;

use Yii;

/**
 * This is the model class for table "hr_staff_info".
 *
 * @property int $id
 * @property int $staff_info_id 员工ID ms staff_info.id
 * @property string $emp_id HR emp.id
 * @property string $name 员工姓名
 * @property string $name_en 英文名
 * @property int $sex 性别 0 未知 1男 2女
 * @property string $identity 身份证/护照
 * @property string $mobile 手机
 * @property string $email 邮箱
 * @property string $job_title 职位
 * @property string $sys_store_id 网点Id
 * @property string $sys_department_id 部门Id
 * @property int $formal 员工属性（下拉列表：1 编制、0 非编制、2:加盟商（合作商）3: 其他（合作商）4:实习生 5:月薪制特殊合同工 6：日薪制特殊合同工 7：时薪制特殊合同工）
 * @property int $hire_type 雇佣类型
 * @property int $hire_times 雇佣期间
 * @property string contract_expiry_date 合同到期时间
 * @property string $company_name_ef 外协或合作商公司名称
 * @property int $state 员工状态 1 在职 2 离职
 * @property string $hire_date 入职时间
 * @property string $leave_date 离职时间
 * @property string $created_at 创建时间
 * @property string $updated_at
 * @property string $branch 备注网点
 * @property string $uuid
 * @property int $working_country
 * @property int $vehicle_source
 * @property int $manger
 * @property int $education
 * @property int $nationality
 * @property int $is_has_job_grade_permission
 * @property int $job_grade_edit_permission
 * @property string $job_grade_effective_date
 */
class BaseStaffInfo extends \yii\db\ActiveRecord
{

    //在职状态
    const STATE_ON_JOB = 1;    //在职
    const STATE_RESIGN = 2;    //离职
    const STATE_SUSPENSION = 3;//停职

    //编制
    const FORMAL_YES = 1;//正式员工
    const FORMAL_FRANCHISEE = 2;//加盟商
    const FORMAL_TRAINEE = 4;//实习生
    const FORMAL_OUTSOURCE = 0;//外协

    const OUTSOURCING_TYPE_INDIVIDUAL = 'individual';
    const OUTSOURCING_TYPE_COMPANY    = 'company';

    //国家
    const COUNTRY_TH = 1;
    const COUNTRY_CN = 2;
    const COUNTRY_MY = 3;
    const COUNTRY_PH = 4;
    const COUNTRY_VN = 5;
    const COUNTRY_LA = 6;
    const COUNTRY_ID = 7;

    const HIRE_OS_TYPE_COMMON = 11;//普通外协
    const HIRE_OS_TYPE_CROWD_SOURCING = 12; //众包外协
    const HIRE_OS_TYPE_EMPLOY = 15;//兼职外协

    const WAIT_LEAVE_STATE_YES = 1;
    const WAIT_LEAVE_STATE_NO = 0;

    const STAFF_TYPE_FORMAL = 0;//正式员工


    //员工创建类型：0正式员工1外协员工管理创建2外协工单创建短期3外协工单创建长期 4外协公司osm创建员工5众包外协
    const STAFF_TYPE_OS_STAFF = 1;          //外协员工管理
    const STAFF_TYPE_OS_ORDER_SHORT = 2;    //外协工单创建短期
    const STAFF_TYPE_OS_ORDER_LONG = 3;     //外协工单创建长期
    const STAFF_TYPE_OS_OSM = 4;            //OSM创建
    const STAFF_TYPE_OS_CROWD_SOURCING = 5; //众包任务

    //车辆来源
    const VEHICLE_SOURCE_PERSONAL_CODE = 1;// 使用个人车辆
    const VEHICLE_SOURCE_RENTAL_CODE = 2;// 租用公司车辆
    const VEHICLE_SOURCE_BORROW_CODE = 3;// 借用车辆

    //离职类型
    const  LEAVE_TYPE_ACTIVE        = 1;
    const  LEAVE_TYPE_NO_COMPENSATE = 2;
    const  LEAVE_TYPE_COMPENSATE    = 3;

    //是否子账号
    const  IS_SUB_STAFF_YES = 1;
    const  IS_SUB_STAFF_NO  = 0;


    //离职来源

    /**
     * by申请
     */
    const LEAVE_SOURCE_BACKYARD = 5;
    const  LEAVE_SOURCE_PROBATION = 8; //试用期未通过
    const  LEAVE_SOURCE_CONTRACT_EXPIRE =11; //合同到期
    const LEAVE_SOURCE_FACE_BLACKLIST = 14;//人脸黑名单

    //离职说明
    const  LEAVE_REASON_SKIP_WORK = 21;
    const  LEAVE_REASON_FAKE_INFO= 25;//向公司提供虚假信息

    // 离职原因
    const LEAVE_REASON_21 = 21; // 连续旷工三天以上

    const STOP_DUTY_REASON_1 = 1;//延期入职
    const STOP_DUTY_REASON_2 = 2;//员工无法申请假期
    const STOP_DUTY_REASON_3 = 3;//惩罚员工
    const STOP_DUTY_REASON_4 = 4;//调查中
    const STOP_DUTY_REASON_5 = 5;//其他
    const STOP_DUTY_REASON_6 = 6;
    const STOP_DUTY_REASON_7 = 7;
    const STOP_DUTY_REASON_8 = 8;//EHS立案调查

    //雇佣类型
    const HIRE_TYPE_NEW_STAFF = 0; //创建员工前，雇佣类型没有，设置初始值
    const HIRE_TYPE_FORMAL = 1; //正式员工
    const HIRE_TYPE_MONTHLY_SALARY = 2; //月薪制员工
    const HIRE_TYPE_DAILY_SALARY = 3; //日薪制员工
    const HIRE_TYPE_HOURLY_WAGE = 4; //时薪制员工
    const HIRE_TYPE_INTERN = 5; // 实习生
    const HIRE_TYPE_GENERAL_OUTSOURCING = 11; // 普通外协
    const HIRE_TYPE_CROWDSOURCING_OUTSOURCING = 12; // 众包外协
    const HIRE_TYPE_UN_PAID = 13; //无底薪
    const HIRE_TYPE_PART_TIME_AGENT = 14;//兼职个人代理

    public static $company_staff_hire_type = [
        self::HIRE_TYPE_FORMAL,
        self::HIRE_TYPE_MONTHLY_SALARY,
        self::HIRE_TYPE_DAILY_SALARY,
        self::HIRE_TYPE_HOURLY_WAGE,
        self::HIRE_TYPE_INTERN,
    ];

    //全部雇佣类型
    public static $all_hire_type = [
        self::HIRE_TYPE_FORMAL,
        self::HIRE_TYPE_MONTHLY_SALARY,
        self::HIRE_TYPE_DAILY_SALARY,
        self::HIRE_TYPE_HOURLY_WAGE,
        self::HIRE_TYPE_INTERN,
        self::HIRE_TYPE_GENERAL_OUTSOURCING,
        self::HIRE_TYPE_CROWDSOURCING_OUTSOURCING,
        self::HIRE_TYPE_UN_PAID,
        self::HIRE_TYPE_PART_TIME_AGENT,
        self::HIRE_OS_TYPE_EMPLOY,
    ];

    //个人代理雇佣类型
    public static $agentTypeTogether = [
        self::HIRE_TYPE_UN_PAID,
        self::HIRE_TYPE_PART_TIME_AGENT,
    ];

    const WEEK_WORKING_DAY_5 = 5;//5天
    const WEEK_WORKING_DAY_6 = 6;//6天
    const WEEK_WORKING_DAY_9 = 9;//自由

    const WEEK_WORKING_DAY_REST_TYPE_51 = 51;//5天轮休
    const WEEK_WORKING_DAY_REST_TYPE_52 = 52;//5天固定休
    const WEEK_WORKING_DAY_REST_TYPE_61 = 61;//6天轮休
    const WEEK_WORKING_DAY_REST_TYPE_62 = 62;//6天固定休
    const WEEK_WORKING_DAY_REST_TYPE_91 = 91;//自由轮休


    public static $batch_update_rest_type_enum = [
        'A' => self::WEEK_WORKING_DAY_REST_TYPE_52,
        'B' => self::WEEK_WORKING_DAY_REST_TYPE_51,
        'C' => self::WEEK_WORKING_DAY_REST_TYPE_62,
        'D' => self::WEEK_WORKING_DAY_REST_TYPE_61,
        // 'E' => self::WORK_DAY_REST_TYPE_91,
    ];


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'hr_staff_info';
    }

    public static function getDb()
    {
        return Yii::$app->get('backyard_main');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        $rules =  [
            [['identity', 'sys_department_id', 'job_title'], 'required', 'message' => 'common_empty_error'],
            [['name', 'job_title', 'identity', 'formal', 'mobile', 'sys_store_id', 'state', 'hire_date', 'sys_department_id'], 'required', 'message' => 'common_empty_error'],
            [['staff_info_id', 'sex', 'formal', 'state', 'is_sub_staff', 'payment_state', 'leave_reason', 'creater', 'is_auto_system_change','wait_leave_state','staff_type', 'node_department_id','contract_company_id','stop_duty_reason','week_working_day','job_title_grade','job_title_level','leave_source','leave_type','instructor_id','is_disability','is_have_disability_certificate','job_title_grade_v2', 'working_country', 'manger', 'nationality', 'education', 'is_has_job_grade_permission', 'job_grade_edit_permission', 'rest_type', 'is_history_leave'], 'integer'],
            [['hire_date', 'leave_date', 'created_at', 'updated_at', 'hire_date_origin', 'stop_duties_date','vehicle_use_date','job_grade_effective_date'], 'safe'],
            [['emp_id', 'job_title', 'uuid'], 'string', 'max' => 64],
            [['company_name_ef'], 'string', 'max' => 100],
            [['name', 'email', 'personal_email','stop_payment_type','nick_name', 'middle_name', 'first_name', 'last_name', 'suffix_name'], 'string', 'max' => 50],
            [['identity', 'branch'], 'string', 'max' => 32],
            [['fund_num'], 'string', 'max' => 12,'tooLong'=>'The longest 12 digit number'],
            [['social_security_num','medical_insurance_num'], 'string', 'max' => 20,'tooLong'=>'The longest 20 digit number'],
            [['name_en'], 'string', 'max' => 128],
            [['payment_markup','remarks'], 'string', 'max' => 200],
//            [['mobile'], 'string', 'length' => [5, 15], 'tooLong' => 'staff_mobile_length_error', 'tooShort' => 'staff_mobile_length_error'],
            [['sys_store_id', 'sys_department_id'], 'string', 'max' => 10],
            [['staff_info_id'], 'unique'],
            [['sex','oil_card_deposit','bank_type','stop_duties_count'], 'default', 'value' => 0],
            [['email', 'personal_email'], function ($attribute) {
                if (!filter_var($this->{$attribute},FILTER_VALIDATE_EMAIL)) {
                    return $this->addError($attribute, 'common_exception');
                }
            }],
            ['state', 'default', 'value' => 1],
            ['disability_certificate', 'string', 'length' => [8, 13], 'tooLong' => 'staff_identity_length_error', 'tooShort' => 'staff_identity_length_error'],
            ['state', 'in', 'range' => [1, 2, 3]],
            ['hire_type', 'in', 'range' => self::$all_hire_type],
            ['hire_times', 'integer', 'min' => 0, 'max' => 366],
            ['contract_expiry_date', 'string', 'max' => 10],
            ['wait_leave_state', 'in', 'range' => [0, 1]],
            ['payment_state', 'in', 'range' => [1, 2]],
            ['formal', 'in', 'range' => [0, 1, 2, 3, 4]],
            ['vehicle_source', 'in', 'range' => [0,1,2,3]],
            ['health_status', 'in', 'range' => [1, 2, 3]],
//            [['bank_no'], 'string', 'length' => [8, 16]],
            [['leave_reason_remark'], 'string', 'max' => 500],
            [['mobile','bank_no'], function ($attribute) {
                if (!preg_match('/^\d*$/i', $this->{$attribute})) {
                    return $this->addError($attribute, 'Is not number');
                }
            }],
            [
                'leave_reason', 'validateLeaveReson',
            ], // 1 个人原因 2 公司开除
            [
                'bank_no', 'validateBank',
            ],
            [
                'identity', 'validateIdentity',
            ],
            [
                'disability_certificate', 'validateDisabilityCertificate',
            ],
            [
                'mobile', 'validateMobile',
            ],
            [
                'mobile_company', 'validateMobileCompany'
            ],
            [
                ['leave_date', 'stop_duties_date'], function ($attribute) {
                    // 外协，加盟，子账号不验证
                    if (!in_array($this->formal, [1, 4]) || $this->is_sub_staff) {
                        return true;
                    }
                    if ($this->state != 1 && !empty($this->$attribute) && (strtotime($this->hire_date) > strtotime($this->$attribute))) {
                        return $this->addError($attribute, 'Not before hire date');
                    }
                },
            ],
            [
                ['bank_no', 'identity'], function ($attribute) {
                    if ($this->isNewRecord && !empty($this->{$attribute})) {
                        if (self::find()->where(['leave_reason' => 2, $attribute => $this->{$attribute}])->exists()) {
                            return $this->addError('black_staff');
                        }
                    }
                },
            ],
        ];

        if(YII_COUNTRY == 'TH') {
            // 泰国
            $rules[] =
              ['identity', 'string', 'length' => [8, 18]];
            $rules[] = [['bank_no'], 'string', 'length' => [1, 30]];
            $rules[] = [['mobile'], 'string', 'length' => [10, 11], 'tooLong' => 'staff_mobile_length_error', 'tooShort' => 'staff_mobile_length_error' ];

        } elseif (YII_COUNTRY == 'PH' && $this->formal != self::FORMAL_OUTSOURCE) {
            // 菲律宾
            $rules[] =
                ['identity', 'string', 'length' => [8, 30], 'tooLong' => 'staff_identity_length_error_2', 'tooShort' => 'staff_identity_length_error_2'];
            $rules[] = [['bank_no'], 'string', 'length' => [1, 30]];
            $rules[] = [['mobile'], 'string', 'length' => [10, 11], 'tooLong' => 'staff_mobile_length_error', 'tooShort' => 'staff_mobile_length_error' ];
        } elseif (YII_COUNTRY == 'VN' && $this->formal != self::FORMAL_OUTSOURCE) {
            // 越南
            $rules[] =
                ['identity', 'string', 'length' => [8, 13], 'tooLong' => 'staff_identity_length_error_2', 'tooShort' => 'staff_identity_length_error_2'];

            $rules[] = [['bank_no'], 'string', 'length' => [1, 30]];
            $rules[] = [['mobile'], 'string', 'length' => [10, 11], 'tooLong' => 'staff_mobile_length_error', 'tooShort' => 'staff_mobile_length_error' ];
        } elseif (YII_COUNTRY == 'MY') {
            // 马来西亚 
            $rules[] =
                ['identity', 'string', 'length' => [8, 13], 'tooLong' => 'staff_identity_length_error_2', 'tooShort' => 'staff_identity_length_error_2'];

            $rules[] = [['bank_no'], 'string', 'length' => [1, 30]];
            $rules[] = [['mobile'], 'string', 'length' => [10, 11], 'tooLong' => 'staff_mobile_length_error', 'tooShort' => 'staff_mobile_length_error' ];
        } elseif (YII_COUNTRY == 'LA') {
            // 老挝
            $rules[] =
                ['identity', 'string', 'length' => [8, 15], 'tooLong' => 'staff_identity_length_error_2', 'tooShort' => 'staff_identity_length_error_2'];
            $rules[] = [['bank_no'], 'string', 'length' => [1, 30]];
            $rules[] = [['mobile'], 'string', 'length' => [10, 11], 'tooLong' => 'staff_mobile_length_error', 'tooShort' => 'staff_mobile_length_error' ];
        }
        elseif (YII_COUNTRY == 'ID' && $this->formal != self::FORMAL_OUTSOURCE) {
            // 印尼
            $rules[] =
                ['identity', 'string', 'length' => [8, 16], 'tooLong' => 'id_staff_identity_length_error', 'tooShort' => 'staff_identity_length_error_2'];
            $rules[] = [['bank_no'], 'string', 'length' => [1, 30],'tooLong' => 'id_bank_no_lenth_error', 'tooShort' => 'id_bank_no_lenth_error'];
            $rules[] = [['mobile'], 'string', 'length' => [8, 13], 'tooLong' => 'mobile_exception_2', 'tooShort' => 'mobile_exception_2' ];
        }
        return $rules;
    }
    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'staff_info_id' => Yii::t('app', 'staff_no'),
            'emp_id' => Yii::t('app', 'HRID'),
            'name' => Yii::t('app', 'name'),
            'name_en' => Yii::t('app', 'name_en'),
            'sex' => Yii::t('app', 'gender'), //性别 0 未知 1男 2女 3 其他
            'identity' => Yii::$app->lang->get('identity'),
            'mobile' => Yii::t('app', 'mobile'),
            'mobile_company' => Yii::t('app','mobile_company'),
            'email' => Yii::t('app', 'email'),
            'job_title' => Yii::t('app', 'job_title'),
            'sys_store_id' => Yii::t('app', 'sys_store_id'),
            'sys_department_id' => Yii::t('app', 'sys_department_id'),
            'node_department_id' =>  Yii::t('app', 'node_department_id'),
            'contract_company_id' =>  Yii::t('app', 'contract_company_id'),
            'formal' => Yii::t('app', 'formal'), // 员工属性（下拉列表：1 编制、0 非编制
            'company_name_ef' => Yii::t('app', 'company_name_ef'), // 外协公司名称
            'state' => Yii::t('app', 'state'), // 员工状态 1 在职 2 离职
            'hire_date' => Yii::t('app', 'hire_date'), // 入职时间
            'leave_date' => Yii::t('app', 'leave_date'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'branch' => Yii::t('app', 'branch'),
            'uuid' => Yii::t('app', 'uuid'),
            'hire_date_origin' => Yii::t('app', '初次入职时间，在创建时间前四天之前入职，将会记录该日期'),
            'is_sub_staff' => Yii::t('app', '是否是子账号 1:是 0:否'),
            'bank_no' => Yii::$app->lang->get('bank_no'),
            'leave_reason' => '离职原因',
            'leave_reason_remark' => '离职原因备注',
            'personal_email' => '个人邮箱',
            'leave_type' => '离职类型',
            'is_auto_system_change' => '最后在职状态变更是否由系统触发',
            'oil_card_deposit' => Yii::t('app','oil_card_deposit'),
            'bank_type' => Yii::t('app','bank_type'),
            'stop_duties_count' => Yii::t('app','stop_duties_count'),
            'wait_leave_state' => Yii::t('app','待离职状态'), //待离职状态 0非待离职1待离职
            'health_status' => Yii::t('app','健康状况'),
            'disability_certificate' => Yii::t('app','残疾证号'),
            'vehicle_source' => Yii::t('app','车辆来源'),
            'vehicle_use_date' => Yii::t('app','车辆开始使用时间'),
            'staff_type' => Yii::t('app','员工类型'), //外协类型1外协员工管理创建2外协工单创建
            'stop_duty_reason' => Yii::t('app','停职原因'),//停职原因
            'stop_payment_type' => Yii::t('app','工资阻止发放类型'),
            'week_working_day' => Yii::t('app', '每周工作天数'),
            'job_title_grade' => Yii::t('app','职级'),
            'job_title_level' => Yii::t('app','职等'),
            'instructor_id' => Yii::t('app','辅导员'),
            'nick_name' =>  Yii::t('app','昵称'),
            'hire_type' => Yii::t('app', 'hire_type'),
            'contract_expiry_date' => Yii::t('app', 'contract_expiry_date'),
            'hire_times' => Yii::t('app', 'hire_times'),
            'working_country' => Yii::t('app', 'working_country'),
            'manger' => Yii::t('app', 'manger'),
            'education' => Yii::t('app', 'education'),
            'nationality' => Yii::t('app', 'nationality'),
            'middle_name' => Yii::t('app', 'middle_name'),
            'first_name' => Yii::t('app', 'first_name'),
            'last_name' => Yii::t('app', 'last_name'),
            'suffix_name' => Yii::t('app', 'suffix_name'),
            'is_history_leave' => Yii::t('app', 'is_history_leave'),
        ];
    }

    public function validateBank()
    {
        // 外协，加盟，子账号不验证
        if (!in_array($this->formal, [1, 4]) || $this->is_sub_staff || empty($this->bank_no)) {
            return true;
        }

        if ($this->isAttributeChanged('bank_no') || $this->isAttributeChanged('is_sub_staff')) {
            if (StaffInfo::find()->where([
                'bank_no' => $this->bank_no,
                'state' => [1, 3],
                'is_sub_staff' => 0,
            ])->exists()) {
                $this->addError('bank_no', 'common_exists');
                return false;
            }
        }

        return true;
    }

    public function validateIdentity()
    {
        // 外协，加盟，子账号不验证
        if (!in_array($this->formal, [1, 4]) || $this->is_sub_staff) {
            return true;
        }

        if (empty($this->identity)) {
            return $this->addError('identity', 'common_empty_error');
        }

        if ($this->isAttributeChanged('identity') || $this->isAttributeChanged('is_sub_staff')) {
            if (self::find()->where([
                'identity' => $this->identity,
                'state' => [1, 3],
                'is_sub_staff' => 0,
            ])->exists()) {
                return $this->addError('identity', 'common_exists');
            }
        }

        return true;
    }

    public function validateDisabilityCertificate()
    {
        if ($this->isAttributeChanged('disability_certificate') && $this->health_status == 2) {
            if (self::find()->where([
                'disability_certificate' => $this->disability_certificate,
                'state' => [1, 3],
                'is_sub_staff' => 0,
            ])->exists()) {
                return $this->addError('disability_certificate', 'common_exists');
            }
        }
        return true;
    }

    public function validateMobile()
    {
        // 外协，加盟，子账号不验证
        if (!in_array($this->formal, [1, 4]) || $this->is_sub_staff) {
            return true;
        }
        if ($this->isAttributeChanged('mobile') || $this->isAttributeChanged('is_sub_staff')) {
            if (self::find()->where([
                'mobile' => $this->mobile,
                'state' => [1, 3],
                'is_sub_staff' => 0,
            ])->exists()) {
                //return $this->addError('mobile', 'common_exists');
                return $this->addError('mobile_existed');
            }
        }

        return true;
    }
    //企业号码验证
    public function validateMobileCompany() {
        // 外协，加盟，子账号不验证
        if (!in_array($this->formal, [1, 4]) || $this->is_sub_staff) {
            return true;
        }

        if ($this->isAttributeChanged('mobile_company') || $this->isAttributeChanged('is_sub_staff')) {
            if (self::find()->where([
                'mobile_company' => $this->mobile_company,
                'state' => [1, 3],
                'is_sub_staff' => 0,
            ])->exists()) {
                return $this->addError('mobile_company_existed');
            }
        }
        return true;
    }

    public function validateLeaveReson()
    {
        //待离职状态是1 离职原因不能为null
        if($this->wait_leave_state == 1) {
            return true;
        } else {
            if ($this->state != 2) {
                $this->leave_reason = null;
                return true;
            }
        }

        //从库中读取离职原因，做差异化
        //if (!in_array($this->leave_reason, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 99])) {
        $name_list = [];
        array_map(function($value) use (&$name_list){
            $name_list = array_merge($name_list, array_values($value));
        }, Yii::$app->sysconfig->leave_type_reasson);
        if(!in_array($this->leave_reason, $name_list)) {
            return $this->addError('leave_reason', 'exception');
        }

        return true;
    }

    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        /* 旧业务逻辑
        if (!empty($this->email) && $this->email != ($changedAttributes['email'] ?? '') && $this->is_sub_staff == 0 && $this->state != 2) {
            $email = HrEmails::find()
                ->where(['staff_info_id' => $this->staff_info_id])
                ->andWhere(['<>', 'state', 2])
                ->one();
            if (!$email) {
                $email = new HrEmails();
            }
            if ($this->email != $email->email) {
                $email->staff_info_id = $this->staff_info_id;
                $email->state = 0;
                $email->email = $this->email;
                if (!$email->save()) {
                    Yii::error('email hr save error' . $this->staff_info_id);
                }
            }
        }
        */
        //如果当前账号离职 离职 变更邮箱状态未待注销  状态 1=已开启 2=已注销关闭 0=待开启 3 待注销
        if(!empty($this->email) && $this->email != ($changedAttributes['email'] ?? '') && $this->is_sub_staff == 0) {
            $email = HrEmails::find()->where(['staff_info_id' => $this->staff_info_id])->one();
            //判断邮箱是否存在  Yii::$app->getUser()->identity['id'] ?? 0,
            if($this->state == 1) {//在职
                if(!$email) {
                    $email = new HrEmails();
                    $email->staff_info_id = $this->staff_info_id;
                    $email->state = 0;
                    $email->email = $this->email;
                }
                if($email->email != $this->email) {
                    $email->email = $this->email;
                }
                //邮箱已存在
                if($email->state == 2) { //已注销
                    $email->state = 0;
                    $email->email = $this->email;
                }
                if($email->state == 3) { //待注销
                    $email->state = 1;
                }
                if (!$email->save()) {
                    Yii::$app->logger->write_log('email hr save error:'.json_encode($email->getErrors(), JSON_UNESCAPED_UNICODE).';工号;'.$this->staff_info_id);
                }
            }

            if($this->state == 2) {//离职
                if($email && $email->state == 1) {
                    $email->state = 3;
                    if (!$email->save()) {
                        Yii::$app->logger->write_log('email hr save error:'.json_encode($email->getErrors(), JSON_UNESCAPED_UNICODE).';工号;'.$this->staff_info_id);
                    }
                }
            }
        }

        // 停职/在职 主账号处理, MS 会自动处理：当主账号变成停职时，其子账号也自动停职
        if (isset($changedAttributes['state']) && in_array($this->state, [3,2])) {
            $subs = StaffItems::find()->select('staff_info_id')->where(['item' => 'MASTER_STAFF', 'value' => strval($this->staff_info_id)])->column(Yii::$app->get('r_backyard'));
            foreach ($subs as $subId) {
                $sub = self::find()->where(['staff_info_id' => $subId])->one();
                if(empty($sub)){
                    continue;
                }
                // 子账号
                if ($sub->state == $this->state || $sub->state == 2) {
                    continue;
                }
                $sub->state = $this->state;
                if ($this->state == 3) {
                    $sub->stop_duties_date = $this->stop_duties_date;
                }

                if ($this->state == 2) {
                    $sub->leave_date = $this->leave_date;
                }

                if ($sub->updateAttributes(['state','stop_duties_date','leave_date'])) {
                    $staffLog = new HrStaffLogs();
                    $staffLog->staff_info_id = $sub->staff_info_id;
                    $staffLog->log_date = (new \DateTime('now', new \DateTimeZone(TIMEZONE)))->format('Y-m-d');
                    $staffLog->state = $this->state;
                    $staffLog->save();
                }
            }
        }

        // 员工状态记录
        $staffLog = new HrStaffLogs();
        $staffLog->staff_info_id = $this->staff_info_id;
        $staffLog->log_date = (new \DateTime('now', new \DateTimeZone(TIMEZONE)))->format('Y-m-d');
        if (isset($changedAttributes['state']) && $this->state != $changedAttributes['state']) {
            $staffLog->state = $this->state;
            if (in_array($this->state, [1, 2])) {
                $this->stop_duties_date = null;
            }
        }

        if (isset($changedAttributes['payment_state']) || isset($changedAttributes['payment_markup'])) {
            $staffLog->payment_state = $this->payment_state;
            $staffLog->payment_markup = $this->payment_markup;
        }

        if (isset($changedAttributes['state']) || isset($changedAttributes['payment_state'])) {
            if (!$staffLog->save()) {
                Yii::$app->logger->write_log('staff state log save error，出现问题原因:'.json_encode($staffLog->getErrors(), JSON_UNESCAPED_UNICODE).';工号;'.$this->staff_info_id);
                $this->addError('state', 'error');
                return false;
            }
        }
    }
}
