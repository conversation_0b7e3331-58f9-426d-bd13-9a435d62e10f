<?php

namespace app\models\manage;

use Yii;

/**
 * This is the model class for table "export_manage".
 *
 * @property int $id 主键
 * @property int $staff_id 操作者id
 * @property string $module_code 模块code
 * @property int $state 下载状态；1=下载中，2=下载失败，3=下载成功
 * @property string $file_name 文件名字
 * @property string $file_path 文件路径
 * @property int $is_deleted 是否删除；1=已删除，0=未删除
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property int $click_num 点击下载次数
 */
class ExportManage extends \yii\db\ActiveRecord
{
    const STATUS_WAITING = 1;
    const STATUS_FAIL = 2;
    const STATUS_SUCCESS = 3;
    
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'export_manage';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['staff_id'], 'required'],
            [['staff_id', 'state', 'is_deleted', 'click_num'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['module_code', 'file_name'], 'string', 'max' => 255],
            [['file_path'], 'string', 'max' => 300],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'staff_id' => 'Staff ID',
            'module_code' => 'Module Code',
            'state' => 'State',
            'file_name' => 'File Name',
            'file_path' => 'File Path',
            'is_deleted' => 'Is Deleted',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'click_num' => 'Click Num',
        ];
    }
}
