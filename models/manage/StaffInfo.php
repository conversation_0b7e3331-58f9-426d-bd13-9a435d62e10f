<?php

namespace app\models\manage;

use app\models\backyard\HcmStaffPermissionInfo;
use app\models\backyard\HrJobDepartmentRelation;
use app\models\backyard\HrProbation;
use app\models\backyard\HrProbationAudit;
use app\models\backyard\HrProbationAuditContractWorker;
use app\models\backyard\HrProbationContractWorker;
use app\models\backyard\HrStaffAnnexInfo;
use app\models\backyard\HrStaffInfoExtend;
use app\models\backyard\VehicleInfo;
use app\models\fle\KaProfile;
use app\models\fle\StaffInfo as FleStaffInfo;
use app\models\fle\StoreDeliveryBarangayStaffInfo;
use app\models\fle\StoreReceivableBillDetail;
use app\models\fle\StoreRemittanceBill;
use app\models\fle\SysDistrict;
use app\models\fle\TicketDelivery;
use app\models\fle\TicketPickup;
use app\models\backyard\SettingEnv;
use app\models\oa\ReserveFundApply;
use app\models\StaffUtils;
use app\modules\v1\business\OutsourcingOrder;
use app\modules\v1\business\StaffInfoCheck;
use app\modules\v1\business\Staff;
use app\modules\v1\config\AreaManager;
use app\services\base\OutSourcingService;
use app\services\base\SettingEnvService;
use app\services\base\StaffService;
use Yii;
use yii\db\Exception;

/**
 * This is the model class for table "hr_staff_info".
 *
 * @property int $id
 * @property int $staff_info_id 员工ID ms staff_info.id
 * @property string $emp_id HR emp.id
 * @property string $name 员工姓名
 * @property string $name_en 英文名
 * @property int $sex 性别 0 未知 1男 2女
 * @property string $identity 身份证/护照
 * @property string $mobile 手机
 * @property string $email 邮箱
 * @property string $personal_email 个人邮箱
 * @property string $job_title 职位
 * @property int $node_department_id 所属 部门 id
 * @property string $position_category 角色
 * @property string $sys_store_id 网点Id
 * @property string $sys_department_id 部门Id
 * @property int $formal 员工属性（下拉列表：1 编制、0 非编制、2:加盟商（合作商）3: 其他（合作商）4:实习生）
 * @property int $hire_type 雇佣类型 (1 正式员工  2 月薪制特殊合同工  3 日薪制特殊合同工 4 时薪制特殊合同工 5 实习生)
 * @property int $hire_times 雇佣期间
 * @property string contract_expiry_date 合同到期时间
 * @property string $company_name_ef 外协或合作商公司名称
 * @property int $state 员工状态 1 在职 2 离职
 * @property int $wait_leave_state 是否待离职 1 是 0 否
 * @property string $hire_date 入职时间
 * @property string $leave_date 离职时间
 * @property string $stop_duties_date 停职时间
 * @property string $created_at 创建时间
 * @property string $updated_at
 * @property string $branch 备注网点
 * @property string $uuid
 * @property int $working_country
 * @property int $manger
 * @property int $is_sub_staff
 * @property int $education
 * @property int $nationality
 * @property string $middle_name
 * @property string $first_name
 * @property string $last_name
 * @property string $suffix_name
 * @property string $company_item_id
 * @property int $contract_company_id
 */
class StaffInfo extends BaseStaffInfo
{
    public $superJobTransfer = 0;
    public $superJobTransferNotJump = 1;
    public $newCarType;//车辆类型
    public $projectNum;//项目期数
    public $vehicleTypeCategory;//save车类型 简直了
    public $conversionPermanentDate;//转正式员工日期
    public $newCarNo;
    public $newPayType;
    public $supervisorMobile;
    public $newMasterStaff;
    public $positionCategory;
    public $profileObjectKey;
    public $manageAreaName;
    public $directManager;
    public $indirectManager;
    public $bankNoName;
    public $identityFrontKey;
    public $identityReverseKey;
    public $forceState = false;
    public $driver_license;
    public $outsourcing_type;
    public $equipment_cost;
    public $equipment_deduction_type;
    public $img_driving_license;
    public $img_identity;
    public $img_driver_license;
    public $img_bank_info;
    public $img_temp_contract;
    public $img_household_registry;
    public $img_vehicle;
    public $patment_markup_other;
    public $manageRegion;
    public $managePiece;
    public $disability_certificate_front;
    public $disability_certificate_reverse;
    public $graduate_school;
    public $major;
    public $graduate_time;
    public $education_old;
    public $nationality_old;
    public $birthday;
    public $register_country;
    public $register_province;
    public $register_city;
    public $register_district;
    public $register_postcodes;
    public $register_house_num;
    public $register_village_num;
    public $register_village;
    public $register_alley;
    public $register_street;
    public $residence_country;
    public $residence_province;
    public $residence_city;
    public $residence_district;
    public $residence_postcodes;
    public $residence_house_num;
    public $residence_village_num;
    public $residence_village;
    public $residence_alley;
    public $residence_street;
    public $dad_first_name;
    public $dad_last_name;
    public $mum_first_name;
    public $mum_last_name;
    public $relatives_relationship;
    public $relatives_first_name;
    public $relatives_last_name;
    public $relatives_call_name;
    public $relatives_mobile;
    public $working_country_old;
    public $new_shift_id;
    public $register_detail_address;
    public $residence_detail_address;
    public $race;
    public $religion;
	public $staff_province_code;//工作所在洲 只有马来 网点 为-1 的时候  有值
    public $is_single;
    public $bank_branch_name;//银行分行名称
    public $household_registration;//户籍照号
    public $ptkp_state;//PTKP状态
    public $tax_card;//税卡号
    public $backup_bank_no_name;  // 备用银行卡持卡人信息
    public $backup_bank_no;       // 备用银行卡号
    public $backup_bank_type;     // 备用银行卡类型
    public $residence_rt;                  // 居住地邻组
    public $residence_rw;                  // 居住地居委会
    public $register_rt;                   // 户口所在地邻组
    public $register_rw;                   // 户口所在地居委会
    public $default_rest_day_date;     //轮休默认休息日
    public $residential_address;      //居住地址
    public $social_security_leave_date;      //社保离职日期
    public $company_item_id;      //外协公司枚举
    public $is_complete_address;      //外协公司，是否完善地址信息

    const SCENARIO_OFFICAL_PARTNER = 'offical_partner';

    // 职位等级编辑权限
    const JOB_GRADE_EDIT_PERMISSION_0 = 0; // 无
    const JOB_GRADE_EDIT_PERMISSION_1 = 1; // 普通
    const JOB_GRADE_EDIT_PERMISSION_2 = 2; // 特殊


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'hr_staff_info';
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_OFFICAL_PARTNER] = ['name', 'mobile', 'sys_store_id', 'formal', 'company_name_ef', 'state', 'hire_date', 'leave_date', 'uuid'];
        return $scenarios;
    }

    /**
     * Returns a value indicating whether the named attribute has been changed.
     * @param string $name the name of the attribute.
     * @param bool $identical whether the comparison of new and old value is made for
     * identical values using `===`, defaults to `true`. Otherwise `==` is used for comparison.
     * This parameter is available since version 2.0.4.
     * @return bool whether the attribute has been changed
     */
    public function isAttributeChanged($name, $identical = true)
    {
        return parent::isAttributeChanged($name, false);
    }

    // 编制员工
    public function isFormal()
    {
        return in_array($this->formal, [1, 4]);
    }

    public function isInformal()
    {
        return $this->formal == 0;
    }

    // 合作商判断
    public function isOfficialPartner()
    {
        return in_array($this->formal, [2, 3]);
    }

    public static function genPno()
    {
        $auto = \Yii::$app->cache->get('staff_pno_temp') ?: 15;
        $auto++;
        \Yii::$app->cache->set('staff_pno_temp', $auto);
        return $auto;
    }

    public function getPositionCategory()
    {
        return HrStaffInfoPosition::find()->where(['staff_info_id' => $this->staff_info_id])->select('position_category')->column(Yii::$app->get('r_backyard'));
    }

    // 获取主账号
    public function getMasterStaff()
    {
        $item = StaffItems::find()
            ->select('value')
            ->where(['staff_info_id' => $this->staff_info_id, 'item' => StaffItems::IIEM_MASTER_STAFF])
            ->scalar();
        if (!$item) {
            return null;
        }
        return self::find()->where(['staff_info_id' => $item])->one();
    }

    public static function getMasterStaffs($staffId = null)
    {
        if ($staffId && !is_array($staffId)) {
            $staffId = [$staffId];
        }

        $items = StaffItems::find()
            ->select(['staff_info_id', 'value'])
            ->where(['item' => StaffItems::IIEM_MASTER_STAFF])
            ->andFilterWhere(['IN', 'staff_info_id', $staffId])
            ->asArray()
            ->all();
        foreach ($items as $key => $item) {
            $masters[$item['staff_info_id']] = $item['value'];
        }
        return $masters ?? '';
    }

    // 获取子账号列表
    public function getSubStaffs()
    {
        $items = StaffItems::find()
            ->select(['staff_info_id'])
            ->where([
                'item' => StaffItems::IIEM_MASTER_STAFF,
                'value' => (string) $this->staff_info_id,
            ])
            ->column();
        if (empty($items)) {
            return [];
        }
        return self::find()->where(['IN', 'staff_info_id', $items])->all();
    }

    // 获取子账号列表
    public static function getSubStaffss($staffId = null)
    {
        if ($staffId && !is_array($staffId)) {
            $staffId = [$staffId];
        }

        $staffId = array_map(function ($v) {
            return (string) $v;
        }, $staffId);
        $items = StaffItems::find()
            ->select(['staff_info_id', 'value'])
            ->where([
                'item' => StaffItems::IIEM_MASTER_STAFF,
            ])
            ->andFilterWhere(['IN', 'value', $staffId])
            ->asArray()
            ->all();

        foreach ($items as $key => $item) {
            $subs[$item['value']][] = $item['staff_info_id'];
        }
        return $subs ?? [];
    }

    /**
     * 是否为月薪制合同工v3员工
     * @param $hireDate
     * @return bool
     */
    public static function isContractV3($hireDate): bool
    {
        $setting_env_model   = SettingEnv::find()->where(['code' => 'MONTH_PROBATION_V3_START_DATE'])->one();
        $contractV3StartTime = empty($setting_env_model) ? '2025-02-16' : $setting_env_model->set_val;
        if (strtotime($hireDate) >= strtotime($contractV3StartTime)) {
            return true;
        }
        return false;
    }

    /**
     * @return true
     */
    public function insertMonthlyProbationRefresh()
    {
        if (YII_COUNTRY != 'TH'){
            return true;
        }
        $probation_evaluate_env = SettingEnv::find()->where(['code' => 'probation_evaluate_env'])->one();
        $probation_evaluate_env = !empty($probation_evaluate_env) ? json_decode($probation_evaluate_env->set_val,
            true) : [];
        while (true) {
            $sql = "SELECT s.staff_info_id,s.hire_date,p.id as 'p_id' FROM `hr_staff_info` AS s LEFT JOIN `hr_probation` AS p ON s.staff_info_id=p.staff_info_id WHERE s.hire_date >= '2020-06-13' and s.is_sub_staff=0 AND (s.hire_type = 1 or (s.hire_type IN (3,4) and s.hire_times >= 365) or (s.hire_type = 2 and s.hire_times >= 12)) AND s.formal=1 AND p.first_evaluate_start IS NULL limit 500";
            $staff_list = Yii::$app->r_backyard->createCommand($sql)->queryAll();
            if (empty($staff_list)){
                break;
            }
            foreach ($staff_list as $item) {
                if (empty($item['staff_info_id']) || empty($item['hire_date'])){
                    continue;
                }
                static::insertMonthlyProbation($item['staff_info_id'],$item['hire_date'],$probation_evaluate_env,true);
            }
        }
        return true;
    }

    public static function fixedHrProbationPhHistory()
    {
        $probation_evaluate_env = SettingEnv::find()->where(['code' => 'probation_evaluate_env'])->one();
        $probation_evaluate_env = json_decode($probation_evaluate_env->set_val, true);
        //补全正式员工表中的第一阶段时间和第二阶段时间
        $sql        = "select hp.id,hp.staff_info_id,date(hsi.hire_date) as hire_date,hsi.job_title_grade_v2,hp.first_evaluate_start,hp.first_evaluate_end,hp.second_evaluate_start,hp.second_evaluate_end from hr_probation hp join hr_staff_info hsi on hp.staff_info_id=hsi.staff_info_id where hp.probation_channel_type=1";
        $staff_list = Yii::$app->r_backyard->createCommand($sql)->queryAll();
        echo '补全正式员工表中的第一阶段时间和第二阶段时间: total ' . count($staff_list) . PHP_EOL;
        foreach ($staff_list as $item) {
            $update = [];
            $empty = empty($item['first_evaluate_start']) || empty($item['first_evaluate_end']) || empty($item['second_evaluate_start']) || empty($item['second_evaluate_end']);
            if (!$empty){
                continue;
            }
            if ($item['job_title_grade_v2'] <= $probation_evaluate_env['job_grade_exceed']) {
                $update['first_evaluate_start']  = static::getDateByDays($item['hire_date'],
                    $probation_evaluate_env['evaluate_day'][1]-1, 1);
                $update['first_evaluate_end']    = static::getDateByDays($item['hire_date'],
                    $probation_evaluate_env['first_check_days']-1, 1);
                $update['second_evaluate_start'] = static::getDateByDays($item['hire_date'],
                    $probation_evaluate_env['evaluate_day'][2]-1, 1);
                $update['second_evaluate_end']   = static::getDateByDays($item['hire_date'],
                    $probation_evaluate_env['second_check_days']-1, 1);
            } else {
                //x 级以上
                $update['first_evaluate_start']  = static::getDateByDays($item['hire_date'],
                    $probation_evaluate_env['evaluate_day_exceed'][1]-1, 1);
                $update['first_evaluate_end']    = static::getDateByDays($item['hire_date'],
                    $probation_evaluate_env['first_check_days_exceed']-1, 1);
                $update['second_evaluate_start'] = static::getDateByDays($item['hire_date'],
                    $probation_evaluate_env['evaluate_day_exceed'][2]-1, 1);
                $update['second_evaluate_end']   = static::getDateByDays($item['hire_date'],
                    $probation_evaluate_env['second_check_days_exceed']-1, 1);
            }
            HrProbation::updateAll($update, ['id' => $item['id']]);
        }
        echo '补全正式员工表中的第一阶段时间和第二阶段时间 done ' . PHP_EOL;
        echo '入职后没在 hr_probation 数据补全 start :' . PHP_EOL;
        $sql        = "select 
                        hsi.staff_info_id,date(hsi.hire_date) as hire_date 
                      from
                      hr_staff_info hsi
                      left join hr_probation hp on hp.staff_info_id = hsi.staff_info_id
                      where
                      hsi.formal = 1
                      and hsi.hire_date >= '2020-06-13'
                      and hsi.is_sub_staff = 0
                      and (
                        hsi.hire_type=1
                        or hsi.hire_type=2 and date(hsi.hire_date) <= '2025-02-16'
                        or hsi.hire_type in (3, 4)
                        and hsi.hire_times >= 365
                      )
                      and hp.id is null";
        $staff_list = Yii::$app->r_backyard->createCommand($sql)->queryAll();
        echo '入职后没在 hr_probation 数据补全 总人数' . count($staff_list) . PHP_EOL;
        foreach ($staff_list as $item) {
              $r =  self::insertMonthlyProbation($item['staff_info_id'],$item['hire_date'],$probation_evaluate_env);
          var_dump($item['staff_info_id'] .'-'. $r);
        }
    }
    /**
     * 试用期管理新增
     * @param $staffInfoId
     * @param $hireDate
     * @param array $probation_evaluate_env
     * @param bool $one_script
     * @return string
     */
    public static function insertMonthlyProbation($staffInfoId, $hireDate, array $probation_evaluate_env = [],$one_script = false): string
    {
        $staff                  = self::find()->where(['staff_info_id' => $staffInfoId])->one();
        if (empty($probation_evaluate_env)){
            $probation_evaluate_env = SettingEnv::find()->where(['code' => 'probation_evaluate_env'])->one();
            $probation_evaluate_env = !empty($probation_evaluate_env) ? json_decode($probation_evaluate_env->set_val,
                true) : [];
        }
        if (YII_COUNTRY == 'TH') {
            $model = HrProbation::find()->where(['staff_info_id' => $staffInfoId])->one();

            if (!$model) {
                $model                         = new HrProbation();
                $model->staff_info_id          = $staffInfoId;
                $model->hire_type              = $staff->hire_type;
                $model->formal_at              = StaffInfo::getConversionPermanentDate($hireDate, null,
                    $staff->job_title_grade_v2 ?? 0, $probation_evaluate_env);
                $model->probation_channel_type = static::isNonFrontLineProbation($staff) ? HrProbation::PROBATION_CHANNEL_TYPE_NON_FRONTLINE : HrProbation::PROBATION_CHANNEL_TYPE_FORMAL;
                $model->created_at             = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
                static::probationCommon($hireDate, $model, $staff->job_title_grade_v2, $probation_evaluate_env);
            } else {
                if (empty($one_script)) {
                    $existsProbation = HrProbationAudit::find()->where(['probation_id' => $model->id])->exists();
                    if (!$existsProbation) {
                        $model->formal_at = StaffInfo::getConversionPermanentDate($hireDate, null,
                            $staff->job_title_grade_v2);
                        static::probationCommon($hireDate, $model, $staff->job_title_grade_v2, $probation_evaluate_env);
                        Yii::$app->logger->write_log('insertMonthlyProbation 入职日期改变 staff_id:' . $model->staff_info_id . ' hireDate:' . $hireDate);
                    }
                } else {
                    $model->probation_channel_type = HrProbation::PROBATION_CHANNEL_TYPE_FORMAL;
                    static::probationCommon($hireDate, $model, $staff->job_title_grade_v2, $probation_evaluate_env);
                }
            }
            return $model->save();
        } elseif (YII_COUNTRY == 'PH') {
            $model         = HrProbation::find()->where(['staff_info_id' => $staffInfoId])->one();
            $modelV3       = HrProbationContractWorker::find()->where(['staff_info_id' => $staffInfoId])->one();
            $isMonthSalary = $staff->hire_type == BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY;
            //不存在都是新增的
            if (!$model && !$modelV3) {
                $probationChannelType = HrProbation::PROBATION_CHANNEL_TYPE_FORMAL;
                if ($staff->hire_type == BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY) {
                    $probationChannelType = static::isContractV3($hireDate) ? HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3 : HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT;
                }
                $isContractWorkerModel = $probationChannelType == HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3;
                $model                 = $isContractWorkerModel ? new HrProbationContractWorker() : new HrProbation();
                $model->staff_info_id  = $staffInfoId;
                $model->hire_type      = $staff->hire_type;
                $model->formal_at      = StaffInfo::getConversionPermanentDate($hireDate,
                    $probationChannelType, $staff->job_title_grade_v2,
                    $probation_evaluate_env);
                //兼容历史 hire type 3 4 数据 不等于月薪 都是默认 正式
                $model->probation_channel_type = $probationChannelType;
                $model->created_at             = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
                static::probationCommon($hireDate, $model, $staff->job_title_grade_v2, $probation_evaluate_env);
                return $model->save();
            } else {
                //判断一下是哪张表的数据
                $probationId = $oldType = $newType = 0;
                //都存在必定是 合同工五个月转正式的员工
                if ($model && $modelV3) {
                    return '试用期合同工五个月转正式员工的 所有信息都不用修改';
                }
                //合同工五个月转 （合同工三个月或者合同工五个月）
                if (!$model && $modelV3) {
                    $auditFind   = HrProbationAuditContractWorker::find();
                    $probationId = $modelV3->id;
                    $oldType     = HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3;
                    $newType = static::isContractV3($hireDate) ? HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3 : HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT;
                }
                //合同工三个月 -》 合同工三个月或者合同工五个月（可以操作，实际操作概率小）
                if ($model && !$modelV3) {
                    $auditFind   = HrProbationAudit::find();
                    $probationId = $model->id;
                    $oldType     = $model->probation_channel_type;
                    if ($oldType == HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT) {
                        $newType = static::isContractV3($hireDate) ? HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3 : HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT;
                    } else {
                        //其他情况保持类型不变
                        $newType = $oldType;
                    }
                }
                if (!isset($auditFind)) {
                    Yii::$app->logger->write_log('$auditFind  不存在' . $staffInfoId);
                    return '';
                }

                //是否发起了评估
                $existsProbation = $auditFind->where(['probation_id' => $probationId])->exists();
                //没发起评估前可修改数据
                if (!$existsProbation) {
                    //正式员工
                    if ($oldType == $newType && $oldType == HrProbation::PROBATION_CHANNEL_TYPE_FORMAL) {
                        $model->formal_at = StaffInfo::getConversionPermanentDate($hireDate,
                            HrProbation::PROBATION_CHANNEL_TYPE_FORMAL, $staff->job_title_grade_v2,
                            $probation_evaluate_env);
                        static::probationCommon($hireDate, $model, $staff->job_title_grade_v2, $probation_evaluate_env);
                        return $model->save();
                    }
                    //合同工三个月和合同工五月类型不变
                    if ($oldType == $newType && in_array($oldType, [
                            HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3,
                            HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT,
                        ])) {
                        $model            = $oldType == HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? $modelV3 : $model;
                        $model->formal_at = StaffInfo::getConversionPermanentDate($hireDate);
                        static::probationCommon($hireDate, $model);
                        return $model->save();
                    }
                    //试用期合同工三个月和试用期合同工五个月互转
                    if (in_array($oldType, [
                            HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3,
                            HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT,
                        ])
                        && in_array($newType, [
                            HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3,
                            HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT,
                        ])
                        && $oldType != $newType) {
                        if ($model) {
                            $model->delete();
                        }
                        if ($modelV3) {
                            $modelV3->delete();
                        }
                        $model                         = $newType == HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? new HrProbationContractWorker() : new HrProbation();
                        $model->staff_info_id          = $staffInfoId;
                        $model->hire_type              = BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY;
                        $model->formal_at              = StaffInfo::getConversionPermanentDate($hireDate);
                        $model->probation_channel_type = $newType;
                        $model->created_at             = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
                        static::probationCommon($hireDate, $model);
                        Yii::$app->logger->write_log('没发起评估可以修改身份 ' . $staffInfoId . ' ' . $oldType . '-' . $newType . '-' . $hireDate . ' delete old probation id' . $probationId,
                            'info');
                        return $model->save();
                    }
                }
            }
        }else{
            return '国家不对,目前仅支持TH,PH';
        }
        return '';
    }

    /**
     * @param $staff
     * @return bool
     */
    public static function isNonFrontLineProbation($staff): bool
    {
        if (
            empty($staff) || 
            empty($staff->node_department_id) || 
            empty($staff->sys_store_id) || 
            empty($staff->job_title) || 
            empty($staff->state) || 
            empty($staff->hire_type) ||
            empty($staff->working_country) ||
            $staff->state == BaseStaffInfo::STATE_RESIGN ||
            $staff->hire_type != BaseStaffInfo::HIRE_TYPE_FORMAL || 
            !in_array($staff->working_country,[1,8])
        ){
            return false;
        }
        $jobDepartmentRelation = HrJobDepartmentRelation::find()->where(['department_id' => $staff->node_department_id])->andWhere(['job_id' => $staff->job_title])->andWhere(['position_type' => [2,3]])->exists();
        if (empty($jobDepartmentRelation)){
            return false;
        }
        $probation_staff = SettingEnv::find()->where(['code' => 'probation_staff'])->one();
        $probation_staff = !empty($probation_staff) ? explode(',',$probation_staff->set_val) : [];
        if (in_array($staff->staff_info_id,$probation_staff)){
            return false;
        }
        return true;
    }

    /**
     * @param $hireDate
     * @param $model
     * @param $job_title_grade_v2
     * @param $probation_evaluate_env
     * @return void
     */
    public static function probationCommon($hireDate, &$model,$job_title_grade_v2 = null,$probation_evaluate_env = [])
    {
        $model->updated_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
        if (YII_COUNTRY == 'PH'){
            if ($model->probation_channel_type == HrProbation::PROBATION_CHANNEL_TYPE_FORMAL) {
                if ($job_title_grade_v2 <= $probation_evaluate_env['job_grade_exceed']) {
                    $model->first_evaluate_start  = static::getDateByDays($hireDate, $probation_evaluate_env['evaluate_day'][1]-1, 1);
                    $model->first_evaluate_end    = static::getDateByDays($hireDate, $probation_evaluate_env['first_check_days']-1, 1);
                    $model->second_evaluate_start = static::getDateByDays($hireDate, $probation_evaluate_env['evaluate_day'][2]-1 , 1);
                    $model->second_evaluate_end   = static::getDateByDays($hireDate, $probation_evaluate_env['second_check_days']-1, 1);
                } else {
                    //x 级以上
                    $model->first_evaluate_start  = static::getDateByDays($hireDate, $probation_evaluate_env['evaluate_day_exceed'][1]-1, 1);
                    $model->first_evaluate_end    = static::getDateByDays($hireDate, $probation_evaluate_env['first_check_days_exceed']-1, 1);
                    $model->second_evaluate_start = static::getDateByDays($hireDate, $probation_evaluate_env['evaluate_day_exceed'][2]-1, 1);
                    $model->second_evaluate_end   = static::getDateByDays($hireDate, $probation_evaluate_env['second_check_days_exceed']-1, 1);
                }
            }
            if ($model->probation_channel_type == HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT) {
                $evaluate_duration            = HrProbation::PH_CONTRACT_STAFF_EVALUATE_DURATION - 1;
                $model->first_evaluate_start  = gmdate('Y-m-d', strtotime($hireDate . " + $evaluate_duration  days"));
                $model->first_evaluate_end    = gmdate('Y-m-d', strtotime($model->first_evaluate_start . ' +5 days'));
                $model->second_evaluate_start = null;
                $model->second_evaluate_end   = null;
            }
            if ($model->probation_channel_type == HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
                $evaluate_duration_v3         = HrProbation::PH_CONTRACT_STAFF_EVALUATE_DURATION_V3 - 1;
                $model->first_evaluate_start  = gmdate('Y-m-d', strtotime($hireDate . " + $evaluate_duration_v3  days"));
                $model->first_evaluate_end    = gmdate('Y-m-d', strtotime($model->first_evaluate_start . ' +5 days'));
                $v3_second                    = HrProbation::PH_CONTRACT_STAFF_EVALUATE_DURATION_V3_SECOND - 1;
                $model->second_evaluate_start = gmdate('Y-m-d', strtotime($hireDate . " + $v3_second  days"));
                $model->second_evaluate_end   = gmdate('Y-m-d', strtotime($model->second_evaluate_start . ' +5 days'));
            }
        }else{
            if ($job_title_grade_v2 <= 17){
                $model->first_evaluate_start = static::getDateByDays($hireDate,($probation_evaluate_env['evaluate_day'][1] ?? 40), 1);
                $model->first_evaluate_end   = static::getDateByDays($hireDate,($probation_evaluate_env['first_check_days'] ?? 45), 1);
                $model->second_evaluate_start = static::getDateByDays($hireDate,($probation_evaluate_env['evaluate_day'][2] ?? 75), 1);
                $model->second_evaluate_end   = static::getDateByDays($hireDate,($probation_evaluate_env['second_check_days'] ?? 85), 1);
            }else{
                $model->first_evaluate_start = static::getDateByDays($hireDate,($probation_evaluate_env['evaluate_day_exceed'][1] ?? 40), 1);
                $model->first_evaluate_end   = static::getDateByDays($hireDate,($probation_evaluate_env['first_check_days_exceed'] ?? 48), 1);
                $model->second_evaluate_start = static::getDateByDays($hireDate,($probation_evaluate_env['evaluate_day_exceed'][2] ?? 75), 1);
                $model->second_evaluate_end   = static::getDateByDays($hireDate,($probation_evaluate_env['second_check_days_exceed'] ?? 88), 1);
            }
        }
    }
    /**
     * 菲律宾 月薪制合同工入职日期修改转正日期变动 或新增
     * @param $staffInfoId
     * @param $formalAt
     * @return void
     */
    protected static function updateMonthlyProbationFormalAt($staffInfoId, $formalAt)
    {
        $find = HrProbation::find()->where(['staff_info_id' => $staffInfoId])->one();
        if ($find && $find->status != HrProbation::STATUS_CORRECTED && $formalAt != $find->formal_at) {
            $find->formal_at = $formalAt;
            $find->save();
        }
    }
    public static function insertStaffExtendInfo($staffInfoId, $params)
    {
        $model = HrStaffInfoExtend::find()->where(['staff_info_id' => $staffInfoId])->one();
        if (empty($model)) {
            $model                = new HrStaffInfoExtend();
            $model->staff_info_id = $staffInfoId;
        }
        $model->project_num = $params['project_num'] ?? null;
        return $model->save();
    }
    /**
     * 获取转正式员工日期
     * @param $hireDate
     * @param $probationChannelType
     * @param $job_title_grade_v2
     * @param $probation_evaluate_env
     * @return false|string
     */
    public static function getConversionPermanentDate($hireDate, $probationChannelType = null, $job_title_grade_v2 = null,$probation_evaluate_env = [])
    {
        if (!$hireDate || !in_array(YII_COUNTRY,['TH','PH'])) {
            return '';
        }
        if (YII_COUNTRY == 'PH'){
            if (empty($probationChannelType)) {
                $addDay = static::isContractV3($hireDate) ? 150 : 90;
            } else {
                //正式员工
                if ($probationChannelType == HrProbation::PROBATION_CHANNEL_TYPE_FORMAL) {
                    if (empty($job_title_grade_v2)){
                        $job_title_grade_v2 = 0;
                    }
                    $formal_days = $job_title_grade_v2 <= $probation_evaluate_env['job_grade_exceed'] ?  ($probation_evaluate_env['formal_days'] ?? 181) : ($probation_evaluate_env['formal_days_exceed'] ?? 181);
                    return static::getDateByDays($hireDate,($formal_days-1), 1);
                } else {
                    $addDay = $probationChannelType == HrProbation::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? 150 : 90;
                }

            }
            $date             = date("Y-m-d", strtotime("$hireDate + $addDay days"));
            $datePermanentArr = explode('-', $date);
            if (empty($datePermanentArr[0]) || empty($datePermanentArr[1]) || empty($datePermanentArr[2])) {
                return '';
            }
            if ($datePermanentArr[2] == '01' || $datePermanentArr[2] == '16') {
                return $date;
            } elseif (($datePermanentArr[2] > 1) && ($datePermanentArr[2] < 16)) {
                return $datePermanentArr[0] . '-' . $datePermanentArr[1] . '-' . '16';
            } else {
                return date('Y-m-d',
                    strtotime(date('Y-m-d', strtotime("$hireDate + $addDay days")) . ' first day of next month'));
            }
        }else{
            if (empty($job_title_grade_v2)){
                $job_title_grade_v2 = 0;
            }
            $formal_days = $job_title_grade_v2 <= 17 ?  ($probation_evaluate_env['formal_days'] ?? 120) : ($probation_evaluate_env['formal_days_exceed'] ?? 120);
            return static::getDateByDays($hireDate,$formal_days - 1, 1);
        }
    }

    /**
     * @param $date
     * @param $days
     * @param $flag
     * @return false|string
     */
    public static function getDateByDays($date, $days, $flag = 0)
    {
        $default = '-';
        if (!empty($flag)) {
            $default = '+';
        }
        return date("Y-m-d", strtotime($default . $days . " days", strtotime($date)));
    }


    /**
     * @param $insert
     * @return bool
     * @throws \Exception
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }

        if ($this->isNewRecord) {
            $formal = $this->isFormal() ? 1 : 0;
            if(!empty($this->newMasterStaff)) {
                $formal = 0;//如果是子账号 工号生成规则和外协保持一致
            }

            $isLnt = StaffService::getInstance()->isLnt($this->contract_company_id);
            if ($isLnt) {
                $this->staff_info_id = StaffUtils::genAgentStaffInfoId([
                    'type'      => $this->sys_store_id == -1 ? 2 : 1,
                    'formal'    => $formal,
                    'hire_type' => 13,
                ]);
            } elseif (in_array(YII_COUNTRY, ['TH', 'MY', 'PH']) && in_array($this->hire_type,
                    self::$agentTypeTogether) && $this->is_sub_staff == 0) {
                $this->staff_info_id = StaffUtils::genAgentStaffInfoId([
                    'type'      => $this->sys_store_id == -1 ? 2 : 1,
                    'formal'    => $formal,
                    'hire_type' => 13,
                ]);
            } else {
                $this->staff_info_id = StaffUtils::genStaffInfoId([
                    'type'   => $this->sys_store_id == -1 ? 2 : 1,
                    'formal' => $formal,
                ]);
            }
        }

        if ($this->isAttributeChanged('state')) {
            $this->is_auto_system_change = 0;
        }

        // 判断工号
        if (empty($this->staff_info_id)) {
            $this->addError('staff_info_id', 'Staff Id Empty');
            return false;
        }

        //if (!$this->setPositionCategory()) {
        //    return false;
        //}

        //if(!in_array($this->newCarType,['Van','Bike'])) {
        //    $this->vehicle_use_date = '';
        //    $this->vehicle_source = 0;
        //}

        //winhr 同步过来的数据：车辆类型是Van 车辆来源是 租用公司车辆 验证车辆开始时间（去掉编辑判断，编辑页面只展示无法修改了）
        /*
        if($this->formal == 1 && $this->isNewRecord &&  in_array($this->newCarType,['Van','Bike']) && $this->vehicle_source == 2) {
            if($this->state == 1) {
                if(strtotime($this->hire_date) > strtotime($this->vehicle_use_date)) {
                    $this->addError('vehicle_source_tip1');
                    return false;
                }
            }
            if($this->state == 2) {
                if(strtotime($this->leave_date) < strtotime($this->vehicle_use_date)) {
                    $this->addError('vehicle_source_tip2');
                    return false;
                }
            }
        }
        */

        // 设置区域经理
        $this->setManageArea();
        $items = [
            'PROFILE_OBJECT_KEY' => $this->profileObjectKey, // 头像
            'MANGER' => $this->directManager, // 直线&虚线主管
            'INDIRECT_MANGER' => $this->indirectManager, //直线&虚线主管
            'BANK_NO_NAME' => $this->bankNoName, // 持卡人信息
            'MASTER_STAFF' => $this->newMasterStaff, //主账号
            'PAY_TYPE' => $this->newPayType, // 外协结算类型
            'SUPERVISOR_MOBILE' => $this->supervisorMobile, // 主管电话
            'DRIVER_LICENSE' => $this->driver_license,//驾驶证号
            'OUTSOURCING_TYPE' => $this->outsourcing_type,//外协类型  个人individual   公司company
            'PAYMENT_MARKUP_OTHER' => $this->patment_markup_other,//工资阻止发放原因其他原因
            'EQUIPMENT_COST' => $this->equipment_cost,//设备费用
            'EQUIPMENT_DEDUCTION_TYPE' => $this->equipment_deduction_type, //设备扣款方式
            'DISABILITY_CERTIFICATE_FRONT' => $this->disability_certificate_front, //残疾证 正面
            'DISABILITY_CERTIFICATE_REVERSE' => $this->disability_certificate_reverse, //残疾证 反面
            'GRADUATE_SCHOOL' => $this->graduate_school, // 毕业学校
            'MAJOR' => $this->major, //专业
            'GRADUATE_TIME' => $this->graduate_time, //毕业时间
            'EDUCATION' => $this->education_old, //最高学历
            'NATIONALITY' => $this->nationality_old, //国籍1泰国2中国99其他
            'BIRTHDAY' => $this->birthday, //生日
            'REGISTER_COUNTRY' => $this->register_country, //户口所在国家
            'REGISTER_PROVINCE' => $this->register_province, //户口所在省
            'REGISTER_CITY' => $this->register_city, //户口所在市
            'REGISTER_DISTRICT' => $this->register_district, //户口所在乡
            'REGISTER_POSTCODES' => $this->register_postcodes, //户口所在邮编
            'REGISTER_HOUSE_NUM' => $this->register_house_num, //户口所在门牌号
            'REGISTER_VILLAGE_NUM' => $this->register_village_num, //户口所在村号
            'REGISTER_VILLAGE' => $this->register_village, //户口所在村
            'REGISTER_ALLEY' => $this->register_alley, //户口所在巷
            'REGISTER_STREET' => $this->register_street, //户口所在街道
            'RESIDENCE_COUNTRY' => $this->residence_country, //居住地所在国家
            'RESIDENCE_PROVINCE' => $this->residence_province, //居住地所在省
            'RESIDENCE_CITY' => $this->residence_city, //居住地所在市
            'RESIDENCE_DISTRICT' => $this->residence_district, //居住地所在乡
            'RESIDENCE_POSTCODES' => $this->residence_postcodes, //居住地所在邮编
            'RESIDENCE_HOUSE_NUM' => $this->residence_house_num, //居住地所在门牌号
            'RESIDENCE_VILLAGE_NUM' => $this->residence_village_num, //居住地所在村号
            'RESIDENCE_VILLAGE' => $this->residence_village, //居住地所在村
            'RESIDENCE_ALLEY' => $this->residence_alley, //居住地所在巷
            'RESIDENCE_STREET' => $this->residence_street, //居住地所在的街道
            'DAD_FIRST_NAME' => $this->dad_first_name, //父亲名
            'DAD_LAST_NAME' => $this->dad_last_name, //父亲姓
            'MUM_FIRST_NAME' => $this->mum_first_name, //母亲名
            'MUM_LAST_NAME' => $this->mum_last_name, //母亲姓
            'RELATIVES_RELATIONSHIP' => $this->relatives_relationship, //亲属关系(紧急联系人)
            'RELATIVES_FIRST_NAME' => $this->relatives_first_name, //亲属名(紧急联系人)
            'RELATIVES_LAST_NAME' => $this->relatives_last_name, //亲属姓(紧急联系人)
            'RELATIVES_CALL_NAME' => $this->relatives_call_name, //亲属称呼(紧急联系人)
            'RELATIVES_MOBILE' => $this->relatives_mobile, //亲属电话(紧急联系人)
            'RESIDENTIAL_ADDRESS' => $this->residential_address, //居住地址详情
            'WORKING_COUNTRY' => $this->working_country_old, //工作所在国家
            'REGISTER_DETAIL_ADDRESS' => $this->register_detail_address, //工作所在国家
            'RESIDENCE_DETAIL_ADDRESS' => $this->residence_detail_address, //工作所在国家
            'RACE' => $this->race, //种族
            'RELIGION' => $this->religion, //宗教
            'STAFF_PROVINCE_CODE' => $this->staff_province_code, //工作所在洲 只有马来 网点 为-1 的时候  有值
            'BANK_BRANCH_NAME' => $this->bank_branch_name, //银行分行名称
            'HOUSEHOLD_REGISTRATION' => $this->household_registration, //户籍照号
            'PTKP_STATE' => $this->ptkp_state, //PTKP状态
            'TAX_CARD' => $this->tax_card, //税卡号
        ];
        // 车辆类型
        if ($this->isOfficialPartner()) {
            $items['CAR_TYPE'] = $this->newCarType;
        } else {
            if (!empty($this->newCarType)) {
                $items['CAR_TYPE'] = $this->newCarType;
            }
        }
        // 车牌号
        if(!empty($this->newCarNo)){
            $items['CAR_NO'] = $this->newCarNo;
        }

        //马来增加车类型
        if(YII_COUNTRY == 'MY' && !empty($this->vehicleTypeCategory)) {
            $items['VEHICLE_TYPE_CATEGORY'] = VehicleInfo::$vehicleTypeCategoryMY[$this->vehicleTypeCategory]??''; //车类型
        }
        if (YII_COUNTRY == 'TH' && !empty($this->social_security_leave_date)){
            $items['SOCIAL_SECURITY_LEAVE_DATE'] = $this->social_security_leave_date; //社保离职日期
        }

        if (isCountry('TH')) {
            if ($this->vehicle_source == BaseStaffInfo::VEHICLE_SOURCE_RENTAL_CODE) {
                $project_num = $this->projectNum;
            } else {
                $project_num = null;
            }
            StaffInfo::insertStaffExtendInfo($this->staff_info_id, ['project_num' => $project_num]);
        }

        $this->is_sub_staff = !empty($this->newMasterStaff) ? 1 : 0;


        if (!empty($this->identityFrontKey)) {
            $items['IDENTITY_FRONT_KEY'] = $this->identityFrontKey; // 身份证正面
            $items['IDENTITY_REVERSE_KEY'] = $this->identityReverseKey; // 身份证反面
        }


        if (YII_COUNTRY == 'PH') {  // 菲律宾需要保存备用银行卡信息
            $items['BACKUP_BANK_NO_NAME'] = $this->backup_bank_no_name;// 备用银行卡持卡人信息
            $items['BACKUP_BANK_NO']     = $this->backup_bank_no; // 备用银行卡号
            $items['BACKUP_BANK_TYPE']   = $this->backup_bank_type;// 备用银行卡类型
        }

        //印尼新增地址字段
        if (YII_COUNTRY == 'ID') {
            $items['RESIDENCE_RT'] = $this->residence_rt; // 居住地邻组
            $items['RESIDENCE_RW'] = $this->residence_rw; // 居住地居委会
            $items['REGISTER_RT']  = $this->register_rt;  // 户口所在地邻组
            $items['REGISTER_RW']  = $this->register_rw;  // 户口所在地居委会
        }

        $res = StaffItems::setItems($this->staff_info_id, $items);
        if ($res !== true) {
            $this->addError(strtolower($res), 'common_exception');
            return false;
        }
        
        if (!empty($this->img_driving_license) ||
            !empty($this->img_identity) ||
            !empty($this->img_driver_license) ||
            !empty($this->img_bank_info) ||
            !empty($this->img_household_registry) ||
            !empty($this->img_temp_contract) ||
            !empty($this->img_vehicle)) {

            $data = [
                ['key' => 'IMG_DRIVING_LICENSE', 'value' => $this->img_driving_license],
                ['key' => 'IMG_IDENTITY', 'value' => $this->img_identity],
                ['key' => 'IMG_DRIVER_LICENSE', 'value' => $this->img_driver_license],
                ['key' => 'IMG_BANK_INFO', 'value' => $this->img_bank_info],
                ['key' => 'IMG_TEMP_CONTRACT', 'value' => $this->img_temp_contract],
                ['key' => 'IMG_HOUSEHOLD_REGISTRY', 'value' => $this->img_household_registry],
                ['key' => 'IMG_VEHICLE', 'value' => $this->img_vehicle],
            ];

            $list = array_filter($data, function ($v, $k) {
                return !empty($v['value']);
            }, ARRAY_FILTER_USE_BOTH);

            foreach ($list as $v) {
                $res = StaffAttachment::setItem($this->staff_info_id, $v['key'], $v['value']);
                if ($res !== true) {
                    $this->addError(strtolower($res), 'common_exception');
                    return false;
                }
            }
        }

        /*
        //职位是Regional Manager(79)只可以选择大区
        //职位是District Manager(269)只可以选择片区
        if (!empty($this->manageRegion) && (
                $this->job_title == 79 && !empty($this->managePiece) ||
                $this->job_title == 269 && empty($this->managePiece)
            )
        ) {
            $this->addError(strtolower($res), 'region_and_piece_tip1');
            return false;
        }
        */
        //正式员工 大区片区管理
        if($this->isFormal()) {
            //张帆 去掉验证
            //if(!empty($this->manageRegion) && !empty($this->managePiece)) {
            //    $this->addError(strtolower($res), 'region_and_piece_tip1');
            //    return false;
            //}
            HrStaffManageRegions::deleteAll(['staff_info_id' => $this->staff_info_id]);

            if(!empty($this->manageRegion)) {
                //大区
                foreach ($this->manageRegion as $key => $value) {
                    $one = new HrStaffManageRegions();
                    $one->staff_info_id = $this->staff_info_id;
                    $one->region_id = $value;
                    $one->piece_id  = 0;
                    if(!$one->save()) {
                        $this->addError(strtolower($res), 'common_exception');
                        return false;
                    }
                }
            }
            if(!empty($this->managePiece)) {
                //片区
                $piece = SysManagePiece::find()
                    ->select(['id','manage_region_id','name'])
                    ->where(['deleted' => 0])
                    ->andWhere(['IN', 'id', $this->managePiece])
                    ->asArray()->all();
                foreach ($piece as $key => $value) {
                    $one = new HrStaffManageRegions();
                    $one->staff_info_id = $this->staff_info_id;
                    $one->region_id = $value['manage_region_id'];
                    $one->piece_id  = $value['id'];
                    if(!$one->save()) {
                        $this->addError(strtolower($res), 'common_exception');
                        return false;
                    }
                }
            }
        }


        /*
        $one = new HrStaffManageRegions();
        $one->staff_info_id = $this->staff_info_id;
        $one->region_id = $this->manageRegion == null ? 0 :$this->manageRegion;
        $one->piece_id  = $this->managePiece == null ? 0 :$this->managePiece;

        //$one->creater   = Yii::$app->getUser()->identity['id'] ?? 0;
        if(!$one->save()) {
            $this->addError(strtolower($res), 'common_exception');
            return false;
        }
        */

        //部门 \ 职位 变动时，无论角色是否变更，均先清空按工号配置的权限，再以最新角色权限合集为准；
        if($this->isAttributeChanged('sys_department_id') || $this->isAttributeChanged('node_department_id') || $this->isAttributeChanged('job_title')) {
            $role_staff_menus_ids = RoleStaffMenus::find()->select(['menu_id'])->where(['staff_info_id' => $this->staff_info_id])->column();
            if(!empty($role_staff_menus_ids)) {
                RoleStaffMenus::deleteAll(['staff_info_id' => $this->staff_info_id]);
                Yii::$app->logger->write_log('部门职位变更，删除Fbi员工按工号配置菜单 staff_id:'. $this->staff_info_id . ' menus_id：'. implode(',', $role_staff_menus_ids),'info');
            }
            $hcmStaffPermission = HcmStaffPermissionInfo::find()->select(['permission_id'])->where(['staff_info_id' => $this->staff_info_id])->column();
            if(!empty($hcmStaffPermission)) {
                HcmStaffPermissionInfo::deleteAll(['staff_info_id' => $this->staff_info_id]);
                Yii::$app->logger->write_log('部门职位变更，删除Hcm员工按工号配置菜单 staff_id:'. $this->staff_info_id . ' menus_id：'. implode(',', $hcmStaffPermission),'info');
                if($this->is_single) {
                    $this->sendEmail($hcmStaffPermission);
                }
            }
        }

        return true;
    }

    /**
     * 员工信息，部门职位变更发送邮件
     * @param $hcmStaffPermission
     */
    public function sendEmail($hcmStaffPermission)
    {
        //发邮件
        $emailInfo['name'] = $this->name;
        $emailInfo['staff_info_id'] = $this->staff_info_id;
        $emailInfo['node_department_id'] = $this->oldAttributes['node_department_id'];
        $emailInfo['job_title'] = $this->oldAttributes['job_title'];
        $emailInfo['permission_ids'] = implode(',', $hcmStaffPermission);

        Yii::$app->jrpc->staffChangeSendEmail($emailInfo);
    }

    public function getItems($type = null)
    {
        $items = StaffItems::find()
            ->select(['value'])
            ->where(['staff_info_id' => $this->staff_info_id])
            ->asArray()
            ->indexBy('item')
            ->column();
        return $type ? $items[$type] ?? '' : $items;
    }

    /**
     * 扩展表信息
     * @param $column
     * @return array|false|mixed
     */
    public function getExtendInfo($column = null)
    {
        $extendInfo = HrStaffInfoExtend::find()->where(['staff_info_id' => $this->staff_info_id])->asArray()->one();
        if (empty($extendInfo)) {
            return false;
        }
        if (empty($column)) {
            return $extendInfo;
        }
        return $extendInfo[$column];
    }


    /**
     * @http://192.168.0.222:8888/zentao/story-view-1477.html

    1.1 未完成的揽件任务，如有则报错：该员工有未完成的揽件任务,不能变更所属网点，请联系网点经理或主管

    1.2 未完成的派件任务，如有则报错：该员工有未完成的派件任务不能变更所属网点，请联系网点经理或主管

    1.3 未回款的快递员公款，如有则报错：该员工有未结清的公款，不能变更所属网点，请联系网点经理或主管

    1.4 若员工绑定了客户，则提示：该员工被签约客户指定揽件，不能变更所属网点，请联系管理员

    1.5 若员工绑定了片区，则提示：该员工是片区负责人，不能变更所属网点，请通知网点经理或主管重新设置片区负责人

    1.6 当前员工有未向总部汇款项，不能变更所属网点 https://l8bx01gcjr.feishu.cn/docs/doccn5krVYyPEDKcH8UNDGOGk4d#
     */
    public function validate($attributeNames = null, $clearErrors = true)
    {

        if (!parent::validate($attributeNames, $clearErrors)) {
            return false;
        }

        // 禁止员工类型转换
        if (!$this->isNewRecord && $this->isAttributeChanged('formal')) {
            return $this->addError('formal', 'deny_change');
        }

        // 离职员工，网点和在职状态不可以更改
        if (!$this->isNewRecord && $this->state == 2) {
            //超管不做限制
            if (!method_exists(Yii::$app,'getUser') || !in_array(Yii::$app->getUser()->identity['id'] ?? 0, [10000, 29921, 21932,28228, 41780])) {
                if ($this->isAttributeChanged('node_department_id')) {
                    return $this->addError('','resigned_employees_cannot_change');
                }
                if ($this->isAttributeChanged('job_title')) {
                    return $this->addError('','resigned_employees_cannot_change');
                }
                if ($this->isAttributeChanged('sys_store_id')) {
                    return $this->addError('','resigned_employees_cannot_change');
                }
            }
        }
        
        // 入职时间
        if ($this->isAttributeChanged('hire_date') && !$this->isInformal() && !$this->isOfficialPartner()) {
            // 超级管理员特殊处理
            $edit_hire_date_role = [];
            $edit_hire_date_role_model = SettingEnv::find()->where(['code' => 'edit_hire_date_role'])->one();
            if(!empty($edit_hire_date_role_model) && $edit_hire_date_role_model->set_val ){
                $edit_hire_date_role = explode(',',$edit_hire_date_role_model->set_val);
            }
            //$edit_hire_date_role = explode(',',env('edit_hire_date_role', '10000,28737'));
            if (!method_exists(Yii::$app,'getUser') || !in_array(Yii::$app->getUser()->identity['id'] ?? 0, $edit_hire_date_role)) {
                if ($this->isNewRecord) {
                    $old = new \DateTime(date('Y-m-d'), new \DateTimeZone('+0000'));
                } else {
                    $old = new \DateTime($this->getOldAttribute('created_at'));
                }
                $new = new \DateTime($this->hire_date);
                $diff = $old->diff($new);

                $edit_hire_date_role_10 = [];
                $edit_hire_date_role_10_model = SettingEnv::find()->where(['code' => 'edit_hire_date_role_10'])->one();
                if(!empty($edit_hire_date_role_10_model) && $edit_hire_date_role_10_model->set_val ){
                    $edit_hire_date_role_10 = explode(',',$edit_hire_date_role_10_model->set_val);
                }
                //$edit_hire_date_role_10 = explode(',',env('edit_hire_date_role_10', '28228,41780'));
                if(method_exists(Yii::$app,'getUser') && in_array(Yii::$app->getUser()->identity['id'], $edit_hire_date_role_10)) {
                    if ($diff->invert == 1 && $diff->days > 10) {
                        return $this->addError('hire_date_out_range');
                    }
                } else {
                    if ($diff->invert == 1 && $diff->days > 2) {
                        return $this->addError('hire_date_out_range');
                    }
                }
            }
        }

        // 非编制
        if ($this->isInformal()) {
            if (empty($this->newPayType)) {
                return $this->addError('pay_type', 'common_empty_error');
            } else if (!in_array($this->newPayType, ['BY_DAY', 'BY_MONTH', 'CASH_PAY'])) {
                return $this->addError('pay_type', 'common_exception');
            }

            if($this->outsourcing_type == 'company') {
                // 外协，外协公司不能为空
                if (!isCountry('TH') && empty($this->company_name_ef)) {
                    return $this->addError('company_name_ef', 'common_empty_error');
                }

                // 外协，外协公司不能为空
                if (isCountry('TH') && empty($this->company_item_id)) {
                    return $this->addError('company_name_ef', 'common_empty_error');
                }
            }

            if($this->outsourcing_type == 'individual') {
                //如果外协类型位个人的话  银行卡类型  银行卡号 持卡人姓名 都是必填

                if(!in_array($this->bank_type,array_keys(Yii::$app->sysconfig->all_bank_type())) && $this->hire_type != BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING  ) {
                    return $this->addError('bank_card_type','error');
                }

                if(empty($this->bankNoName) &&  $this->hire_type != BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING ) {
                    return $this->addError('bank_no_name', 'common_empty_error');
                }
                if(empty($this->bank_no) &&  $this->hire_type != BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING ) {
                    return $this->addError('bank_no', 'common_empty_error');
                }
                if(empty($this->bank_type) &&  $this->hire_type != BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING ) {
                    return $this->addError('bank_card_type', 'common_empty_error');
                }
                // 个人外协字段验证 my 重写
                $validate_field = OutSourcingService::getInstance()->validateIndividualField([
                    'relatives_relationship' => $this->relatives_relationship,
                    'relatives_call_name'    => $this->relatives_call_name,
                    'relatives_mobile'       => $this->relatives_mobile,
                    'residential_address'    => $this->residential_address,
                ]);
                if ($validate_field !== true) {
                    [$att, $err] = $validate_field;
                    return $this->addError($att, $err);
                }

            }
        } else {
            //if(!in_array($this->bank_type,array_keys(Yii::$app->sysconfig->bank_type))) {
            //    return $this->addError('bank_card_type','error');
            //}
            if(empty($this->bankNoName)) {
                $this->bankNoName = $this->name;
            }
        }

        // 合作商判断
        if ($this->isOfficialPartner()) {
            if ($this->sys_store_id == -1) {
                $this->addError('sys_store_id', 'common_exception');
                return false;
            }

            $store = SysStoreTemp::temp()[$this->sys_store_id] ?? '';
            if (!$store || $store['category'] != 6) {
                $this->addError('store', 'common_exception');
                return false;
            }
        }

        // 验证主账号
        if (!empty($this->newMasterStaff)) {
            if (StaffItems::find()->where([
                'staff_info_id' => $this->newMasterStaff,
                'item' => StaffItems::IIEM_MASTER_STAFF,
            ])->exists()) {
                return $this->addError('master_account', 'The Master Staff Error');
            }
            if (!self::find()->where(['staff_info_id' => $this->newMasterStaff, 'state' => 1])->exists()) {
               // return $this->addError('master_account', 'The Master Staff State Error');
            }

            if ($this->newMasterStaff == $this->staff_info_id) {
                return $this->addError('master_account', 'Master Staff State Error');
            }
        }

        // 账号离职，验证是否所有子账号都离职
        if ($this->isAttributeChanged('state')) {
            if ($this->state == 2) {
                foreach ($this->subStaffs as $staff) {
                    if ($staff->state == 1) {
                        Yii::$app->logger->write_log(['staff_info_id' => $staff->staff_info_id,  'error' => 'set_master_leave_tip'], 'info');
                        return $this->addError('master_account', 'set_master_leave_tip');
                    }
                }
            }
        }


        // 出纳网点经理判断
        if (($res = $this->checkPosition()) && $res !== true) {
            return $this->addError('role', $res);
        }

        //0 待开启 1已开启 2已注销 3待注销
        if($this->state == 1) {
            $email = HrEmails::find()->where(['staff_info_id' => $this->staff_info_id])->one();
            if(!$email && !empty($this->email)) {
                if(HrEmails::find()->where(['email' => $this->email])->andWhere(['!=','state',2])->exists()) {
                    return $this->addError('', 'mail_exists');
                }
            }
        }

        // 编制和实习生需要部门和职位，角色 判断 非编制
        if (($this->isFormal() || $this->isInformal()) && $this->is_sub_staff == 0) {
            $departement_id = $this->node_department_id == 0 ? $this->sys_department_id : $this->node_department_id;
            // 部门-职位判断
            //$departs = Yii::$app->sysconfig->relationDepartmentJobTitle();
            $departs = Yii::$app->sysconfig->relationDepartmentJobTitleV2();
            if (!isset($departs[$departement_id])) {
                return $this->addError('sys_department_id', 'Department Error');
            } else if (!in_array($this->job_title, $departs[$departement_id])) {
                return $this->addError('', 'Job Title Configuration Error');
            }

            // 职位-角色判断
            // HRIS角色可以为空 MS那边设置一个默认角色
            $header_office_position_keys = array_keys(Yii::$app->sysconfig->getHeaderOfficePosition());

            $store_position_keys = array_keys(Yii::$app->sysconfig->getStorePosition());
            $jobTitles = Yii::$app->sysconfig->relationJobTitlePosition();
            if (empty($this->positionCategory)) {
                if (isset($jobTitles[$departement_id . '_' . $this->job_title])) {
                    $allowPos = $jobTitles[$departement_id . '_' . $this->job_title];
                    if ($this->sys_store_id == -1) {
                        $count = array_intersect($allowPos, $header_office_position_keys);
                    } else {
                        $count = array_intersect($allowPos, $store_position_keys);
                    }

                    if (count($count) > 0) {
                        return $this->addError('role', 'cannot be blank.');
                    }
                }
            } else {
                if(isset($jobTitles[$departement_id . '_' . $this->job_title])) {
                    $super_admin_position_staffs = [];
                    $super_admin_position_staff = SettingEnv::find()->where(['code' => 'super_admin_position_staff'])->one();
                    if(!empty($super_admin_position_staff) && $super_admin_position_staff->set_val){
                        $super_admin_position_staffs = explode(',',$super_admin_position_staff->set_val);
                    }
                    //return $this->addError('', 'Configuration Role Error');
                    foreach ($this->positionCategory as $key => $val) {
                        //指定人有超管权限，不验证
                        if(in_array($this->staff_info_id,$super_admin_position_staffs) && $val == 99){
                            continue;
                        }

                        if (!in_array($val, $jobTitles[$departement_id . '_' . $this->job_title])) {
                            //Yii::$app->logger->write_log(' staff validate  : ' . $this->staff_info_id, 'info');
                            Yii::$app->logger->write_log(['staff_info_id' => $this->staff_info_id, 'position' => $this->positionCategory, 'error' => 'Configuration_Role_Error'], 'info');
                            return $this->addError('', 'Configuration Role Error');//部门职位角色没有关联关系
                        }

                        if($this->sys_store_id == '-1') {
                            if(!in_array($val, $header_office_position_keys)) {
                                Yii::$app->logger->write_log(['staff_info_id' => $this->staff_info_id, 'position' => $this->positionCategory, 'error' => 'config_error_1'], 'info');
                                return $this->addError('', 'config_error_1');//总部员工不能配置网点角色
                            }
                        } else {
                            if(!in_array($val, $store_position_keys)) {
                                Yii::$app->logger->write_log(['staff_info_id' => $this->staff_info_id, 'position' => $this->positionCategory, 'error' => 'config_error_2'], 'info');
                                return $this->addError('', 'config_error_2');//网点员工不能配置总部角色
                            }
                        }
                    }
                }
            }
        } else if ($this->isOfficialPartner()) {
            foreach ($this->positionCategory as $pos) {
                if (!array_key_exists($pos, \Yii::$app->sysconfig->getOfficalPartnetPosition())) {
                    return $this->addError('role', 'common_exception');
                }
            }
        }

        // 直线&虚线主管判断
        if ($this->isFormal()) {
            if (empty($this->directManager)) {
                return $this->addError('imm_supervisor', 'common_empty_error');
            }
            if (!empty($this->indirectManager) && !self::find()->where(['formal' => 1, 'staff_info_id' => $this->indirectManager])->exists()) {
                return $this->addError('sub_boss', 'Not Found');
            }
        }

        // 验证网点
        if (!$this->superJobTransfer && ($res = $this->validateStore()) !== true) {
            return false;
        }

        //临时使用的方案，批量转岗不验证部分逻辑
        if($this->superJobTransfer){
            $res = $this->superJobTransferValidateStore();
            if(!empty($res)){
                return $this->addError('store',$res);
            }
        }
        //unset($this->superJobTransfer);
        //unset($this->superJobTransferNotJump);

        return !$this->hasErrors();
    }

    public function superJobTransferValidateStore()
     {

         $error = [];
         if ($this->isAttributeChanged('state') &&
             $this->sys_store_id != '-1' &&
             $this->state == 1) {
             $store = Yii::$app->sysconfig->getStore($this->sys_store_id);
             if ($store['state'] != 1) {
                 $error[] = 'state error';
             }
         }


         // 网点判断
         if (!$this->isNewRecord && $this->isAttributeChanged('sys_store_id')) {
             // 1 未完成的揽件任务，如有则报错：该员工有未完成的揽件任务,不能变更所属网点，请联系网点经理
             if ($this->superJobTransferNotJump && TicketPickup::find()
                 ->where(['staff_info_id' => $this->staff_info_id])
                 ->andWhere(['state' => 1])
                 ->exists()
             ) {
                 $error[] = 'store_change_tip1';
             }

             // 2 未完成的派件任务，如有则报错：该员工有未完成的派件任务不能变更所属网点，请联系网点经理
             if ($this->superJobTransferNotJump && TicketDelivery::find()
                 ->where(['staff_info_id' => $this->staff_info_id])
                 ->andWhere(['state' => 0])
                 ->exists()
             ) {
                 $error[] = 'store_change_tip2';
             }

             // 3.未回款的快递员公款，如有则报错：该员工有未结清的公款，不能变更所属网点，请联系网点经理
             if (StoreReceivableBillDetail::find()
                 ->where(['store_id' => $this->oldAttributes['sys_store_id']])
                 ->andWhere(['staff_info_id' => $this->staff_info_id])
                 ->andWhere(['state' => [0, 3]])
                 ->exists()) {
                 $error[] = 'store_change_tip3';
             }

             // 若员工绑定了客户，则提示：该员工被签约客户指定揽件，不能变更所属网点，请联系管理员
             if ($this->superJobTransferNotJump && KaProfile::find()->where(['courier_id' => $this->staff_info_id])->exists()) {
                 $error[] = 'store_change_tip4';
             }
             if(in_array(YII_COUNTRY, ['PH', 'MY'])) {
                 //菲律宾马来 所属网点 该员工已绑定派送码，不能变更所属网点，请通知网点主管重新设置派送码设置及快递员绑定
                 if($this->superJobTransferNotJump && StoreDeliveryBarangayStaffInfo::find()->where(['staff_info_id' => $this->staff_info_id, 'deleted' => 0])->exists()) {
                     $error[] = 'store_change_tip8';
                 }
             } else {
                 // 该员工是片区负责人，不能变更所属网点，请通知网点经理重新设置片区负责人
                 if ($this->superJobTransferNotJump && SysDistrict::find()->where(['courier_id' => $this->staff_info_id, 'deleted' => 0])->exists()) {
                     $error[] = 'store_change_tip5';
                 }
             }

             // 当前员工有未向总部汇款项，不能变更所属网点 https://l8bx01gcjr.feishu.cn/docs/doccn5krVYyPEDKcH8UNDGOGk4d#
             //select * from store_remittance_bill where store_id = '' AND collector_id = '' AND (parcel_state = 0 or cod_state = 0); cc 杨云飞
             if ($this->superJobTransferNotJump && StoreRemittanceBill::find()
                 ->where(['store_id' => $this->oldAttributes['sys_store_id'], 'collector_id' => $this->staff_info_id ])
                 ->andWhere(['OR',['parcel_state' => 0],['cod_state' => 0]])
                 ->exists()) {
                 $error[] = 'store_change_tip9';
             }
         }
         return $error;
     }


    public function validateStore()
    {

        if ($this->isAttributeChanged('state') &&
            $this->sys_store_id != '-1' &&
            $this->state == 1) {
            $store = Yii::$app->sysconfig->getStore($this->sys_store_id);
            if ($store && $store['state'] != 1) {
                return $this->addError('store', 'state error');
            }
        }


        // 网点判断
        if (!$this->isNewRecord && $this->isAttributeChanged('sys_store_id')) {
            $validate_code = [
                'hris_validate_ticket_pickup', //验证是否有未完成的揽件任务 0不验证1验证
                'hris_validate_ticket_delivery', //验证是否有未完成的派件任务 0不验证1验证
                'hris_validate_store_receivable_bill_detail', //验证未回款的快递员公款 0不验证1验证
                'hris_validate_ka_profile', //验证员工绑定了ka客户0不验证 1验证
                'hris_validate_store_delivery_barangay_staff_info', //菲律宾/马来 验证员工已绑定派送码0不验证 1验证
                'hris_validate_sys_district', //非菲律宾/马来 该员工是片区负责人 0不验证 1验证
                'hris_validate_store_remittance_bill', //验证是否有未向总部汇款项 0不验证 1验证
                'hris_validate_reserve_fund_apply', //验证备用金是否归还 0不验证1验证
            ];
            $setting_env = SettingEnv::find()->where(['code' => $validate_code])->asArray()->indexBy('code')->all();
            $hris_validate_ticket_pickup = $setting_env['hris_validate_ticket_pickup']['set_val'] ?? 1;
            $hris_validate_ticket_delivery = $setting_env['hris_validate_ticket_delivery']['set_val'] ?? 1;
            $hris_validate_store_receivable_bill_detail = $setting_env['hris_validate_store_receivable_bill_detail']['set_val'] ?? 1;
            $hris_validate_ka_profile = $setting_env['hris_validate_ka_profile']['set_val'] ?? 1;
            $hris_validate_store_delivery_barangay_staff_info = $setting_env['hris_validate_store_delivery_barangay_staff_info']['set_val'] ?? 1;
            $hris_validate_sys_district = $setting_env['hris_validate_sys_district']['set_val'] ?? 1;
            $hris_validate_store_remittance_bill = $setting_env['hris_validate_store_remittance_bill']['set_val'] ?? 1;
            $hris_validate_reserve_fund_apply = $setting_env['hris_validate_reserve_fund_apply']['set_val'] ?? 1;


            // 1 未完成的揽件任务，如有则报错：该员工有未完成的揽件任务,不能变更所属网点，请联系网点经理
            if ($hris_validate_ticket_pickup == 1 && TicketPickup::find()
                ->where(['staff_info_id' => $this->staff_info_id])
                ->andWhere(['state' => 1])
                ->exists()
            ) {
                return $this->addError('store', 'store_change_tip1');
            }

            // 2 未完成的派件任务，如有则报错：该员工有未完成的派件任务不能变更所属网点，请联系网点经理
            if ($hris_validate_ticket_delivery == 1 && TicketDelivery::find()
                ->where(['staff_info_id' => $this->staff_info_id])
                ->andWhere(['state' => 0])
                ->exists()
            ) {
                return $this->addError('store', 'store_change_tip2');
            }

            // 3.未回款的快递员公款，如有则报错：该员工有未结清的公款，不能变更所属网点，请联系网点经理
            if ($hris_validate_store_receivable_bill_detail == 1 && StoreReceivableBillDetail::find()
                ->where(['store_id' => $this->oldAttributes['sys_store_id']])
                ->andWhere(['staff_info_id' => $this->staff_info_id])
                ->andWhere(['state' => [0, 3]])
                ->exists()) {
                return $this->addError('store', 'store_change_tip3');
            }

            // 若员工绑定了客户，则提示：该员工被签约客户指定揽件，不能变更所属网点，请联系管理员
            if ($hris_validate_ka_profile == 1 && KaProfile::find()->where(['courier_id' => $this->staff_info_id])->exists()) {
                return $this->addError('store', 'store_change_tip4');
            }
            if(in_array(YII_COUNTRY, ['PH', 'MY'])) {
                //菲律宾马来 所属网点 该员工已绑定派送码，不能变更所属网点，请通知网点主管重新设置派送码设置及快递员绑定
                if($hris_validate_store_delivery_barangay_staff_info == 1 && StoreDeliveryBarangayStaffInfo::find()->where(['staff_info_id' => $this->staff_info_id, 'deleted' => 0])->exists()) {
                    return $this->addError('store', 'store_change_tip8');
                }
            } else {
                // 该员工是片区负责人，不能变更所属网点，请通知网点经理重新设置片区负责人
                if ($hris_validate_sys_district == 1 && SysDistrict::find()->where(['courier_id' => $this->staff_info_id, 'deleted' => 0])->exists()) {
                    return $this->addError('store', 'store_change_tip5');
                }
            }



            // 当前员工有未向总部汇款项，不能变更所属网点 https://l8bx01gcjr.feishu.cn/docs/doccn5krVYyPEDKcH8UNDGOGk4d#
            //select * from store_remittance_bill where store_id = '' AND collector_id = '' AND (parcel_state = 0 or cod_state = 0); cc 杨云飞
            if ($hris_validate_store_remittance_bill == 1 && StoreRemittanceBill::find()
                                   ->where(['store_id' => $this->oldAttributes['sys_store_id'], 'collector_id' => $this->staff_info_id ])
                                   ->andWhere(['OR',['parcel_state' => 0],['cod_state' => 0]])
                                   ->exists()) {
                return $this->addError('store', 'store_change_tip9');
            }

            //验证是否有未归还备用金
            if($hris_validate_reserve_fund_apply == 1 && ReserveFundApply::find()->where(['create_id' => $this->staff_info_id])->andWhere(['return_status' => [1,2,4]])->andWhere(['create_store_id' => $this->oldAttributes['sys_store_id']])->exists()) {
                return $this->addError('store', 'store_change_tip10');
            }


        }
        return true;
    }

    // 一个网点只能有一个 3 => 'STORE_MANAGER', // 网点经理
    // 4 => 'STORE_CASHIER', //网点出纳
    //新需求
    //a.SHOP 网点，判断“网点出纳”最多：2名
    //b.DC/SP网点，判断“网点出纳”最多：4名
    //c.其他网点，判断不变，“网点出纳”最多：1名
    public function checkPosition()
    {
        if ($this->isAttributeChanged('state') && in_array($this->state, [2, 3])) {
            return true;
        }

        if($this->is_sub_staff == 1) { //如果是子账号 不验证
            return true;
        }

        $pos = array_intersect($this->positionCategory ?? [], [3, 4]);
        if (empty($pos)) {
            return true;
        }

        //获取网点信息
        $store = SysStoreTemp::temp()[$this->sys_store_id] ?? '';
        $store_category = $store['category'] ?? '';

        $positionExists = FleStaffInfo::find()
            ->select(['COUNT(1) AS count', 'staff_info_position.position_category AS position_category', 'staff_info_position.staff_info_id'])
            ->innerJoin('staff_info_position', 'staff_info_position.staff_info_id = staff_info.id')
            ->where(['staff_info.organization_type' => 1])
            ->andWhere(['staff_info.organization_id' => $this->sys_store_id])
            ->andWhere(['staff_info_position.position_category' => $pos])
            ->andWhere(['staff_info.state' => 1])
            ->andFilterWhere(['!=', 'staff_info.id', $this->staff_info_id])
            ->groupBy('staff_info_position.position_category')
            ->asArray()
            ->all();

        $notice = StaffInfoCheck::checkStoreCashier($positionExists, $store_category);
        if (!empty($notice)) {
            return $notice;
        }

        // hris校验
        $positionExists = StaffInfo::find()
            ->select(['COUNT(1) AS count', 'hr_staff_info_position.position_category AS position_category'])
            ->innerJoin('hr_staff_info_position', 'hr_staff_info_position.staff_info_id = hr_staff_info.staff_info_id')
            ->where(['hr_staff_info.sys_store_id' => $this->sys_store_id])
            ->andWhere(['hr_staff_info_position.position_category' => $pos])
            ->andWhere(['hr_staff_info.state' => 1])
            ->andFilterWhere(['!=', 'hr_staff_info.staff_info_id', $this->staff_info_id])
            ->groupBy('hr_staff_info_position.position_category')
            ->asArray()
            ->all();

        $notice = StaffInfoCheck::checkStoreCashier($positionExists, $store_category);
        if (!empty($notice)) {
            return $notice;
        }

        return true;
    }

    public function setManageArea()
    {
        $insertData = [];
        // 区域经理
        HrAreaManagerStore::deleteAll(['staff_info_id' => $this->staff_info_id]);
        $areaRoles = (array)explode(",", SettingEnvService::getInstance()->getSetVal('jurisdiction_area_roles'));
        if (count(array_intersect($areaRoles, $this->positionCategory)) > 0 && !empty($this->manageAreaName)) {
            foreach ($this->manageAreaName as $area) {
                $area = trim($area);
                // 所属区域
                switch (YII_COUNTRY) {
                    case 'MY':
                        $storeIds = Yii::$app->sysconfig->getStoresBySortingNo(AreaManager::$areasMy, $area);
                        break;
                    case 'LA':
                        $storeIds = Yii::$app->sysconfig->getStoresBySortingNo(AreaManager::$areasLa, $area);
                        break;
                    default:
                        $storeIds = Yii::$app->sysconfig->getStoresByArea($area);
                }
                foreach ($storeIds as $key => $storeId) {
                    $insertData[] = [
                        $this->staff_info_id,
                        strval($storeId),
                        $area,
                    ];
                }
            }
            if ($insertData) {
                $res = Yii::$app->db->createCommand()->batchInsert(HrAreaManagerStore::tableName(),
                    ['staff_info_id', 'sys_store_id', 'manage_area_name'], $insertData)->execute();
                if (!$res) {
                    Yii::$app->logger->write_log('HrAreaManagerStore保存失败，可能出现的原因' . json_encode($insertData,
                            JSON_UNESCAPED_UNICODE) . ';工号' . $this->staff_info_id);
                    return $this->addError('sys_store_id', 'Area Manager Operation Store Error');
                }
            }
        }
        return true;
    }

}
