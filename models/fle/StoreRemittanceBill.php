<?php

namespace app\models\fle;

use Yii;

/**
 * This is the model class for table "store_remittance_bill".
 *
 * @property string $id 主键
 * @property string $store_id 网点ID
 * @property string $business_date 业务日期
 * @property int $state 回款状态 0:未结清 1:已结清
 * @property int $parcel_amount 网点运费
 * @property int $parcel_state 运费结算状态
 * @property string $parcel_remittance_record_id 运费结算ID
 * @property int $cod_amount cod金额
 * @property int $cod_state COD结算状态
 * @property string $cod_remittance_record_id COD结算ID
 * @property string $remittance_record_id 汇款记录ID
 * @property string $created_at
 * @property string $updated_at
 */
class StoreRemittanceBill extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'store_remittance_bill';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('fle');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['business_date', 'created_at', 'updated_at'], 'safe'],
            [['state', 'parcel_amount', 'parcel_state', 'cod_amount', 'cod_state'], 'integer'],
            [['id', 'parcel_remittance_record_id', 'cod_remittance_record_id', 'remittance_record_id'], 'string', 'max' => 32],
            [['store_id'], 'string', 'max' => 10],
            [['store_id', 'business_date'], 'unique', 'targetAttribute' => ['store_id', 'business_date']],
            [['id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', '主键'),
            'store_id' => Yii::t('app', '网点ID'),
            'business_date' => Yii::t('app', '业务日期'),
            'state' => Yii::t('app', '回款状态 0:未结清 1:已结清'),
            'parcel_amount' => Yii::t('app', '网点运费'),
            'parcel_state' => Yii::t('app', '运费结算状态'),
            'parcel_remittance_record_id' => Yii::t('app', '运费结算ID'),
            'cod_amount' => Yii::t('app', 'cod金额'),
            'cod_state' => Yii::t('app', 'COD结算状态'),
            'cod_remittance_record_id' => Yii::t('app', 'COD结算ID'),
            'remittance_record_id' => Yii::t('app', '汇款记录ID'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
        ];
    }
}
