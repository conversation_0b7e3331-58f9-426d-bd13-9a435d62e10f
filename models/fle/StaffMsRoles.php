<?php

namespace app\models\fle;

use Yii;

/**
 * This is the model class for table "staff_ms_role".
 *
 * @property int $id ID
 * @property string $name 名称
 * @property string $description_zh 描述——中文
 * @property string $description_th 描述——泰文
 * @property string $description_en 描述——英文
 * @property string $created_at
 * @property string $updated_at
 */
class StaffMsRoles extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'staff_ms_role';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('fle');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['id'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['name'], 'string', 'max' => 50],
            [['description_zh', 'description_th', 'description_en'], 'string', 'max' => 100],
            [['id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'description_zh' => 'Description Zh',
            'description_th' => 'Description Th',
            'description_en' => 'Description En',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
