<?php

namespace app\models\backyard;

use Yii;

/**
 * This is the model class for table "leave_manage_log".
 *
 * @property int $id
 * @property int $staff_info_id 工号
 * @property string $serial_no 离职编码
 * @property string $leave_date 离职日期
 * @property int $leave_source 离职来源
 * @property int $leave_type 离职类型
 * @property int $leave_reason 离职原因
 * @property int $approval_status 审批状态
 * @property int $by_staff_resign_id by申请离职自增id
 * @property int $by_approval_status by审批状态
 * @property int $operate_id 操作人id
 * @property string $operate_name 操作人姓名
 * @property string $created_at 创建时间
 * @property string $updated_at 最后更新时间
 */
class LeaveManageLog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'leave_manage_log';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('backyard_main');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['staff_info_id', 'leave_source', 'leave_type', 'leave_reason', 'approval_status', 'by_staff_resign_id', 'by_approval_status', 'operate_id'], 'integer'],
            [['leave_date', 'created_at', 'updated_at'], 'safe'],
            [['serial_no', 'operate_name'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'staff_info_id' => 'Staff Info ID',
            'serial_no' => 'Serial No',
            'leave_date' => 'Leave Date',
            'leave_source' => 'Leave Source',
            'leave_type' => 'Leave Type',
            'leave_reason' => 'Leave Reason',
            'approval_status' => 'Approval Status',
            'by_staff_resign_id' => 'By Staff Resign ID',
            'by_approval_status' => 'By Approval Status',
            'operate_id' => 'Operate ID',
            'operate_name' => 'Operate Name',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
