<?php

namespace app\models\backyard;

use Yii;

/**
 * This is the model class for table "hr_out_sourcing_blacklist".
 *
 * @property int $id
 * @property string $identity 证件号（身份证/护照）
 * @property string $name 姓名
 * @property string $mobile_area_code 电话区号
 * @property string $mobile 电话
 * @property int $submitter_staff_id 提交人id
 * @property int $type 类型（1=直接录入，）
 * @property string $remark 备注
 * @property int $remove_staff_id 移除人id
 * @property string $remove_date 移除时间
 * @property string $remove_remark 移除说明
 * @property int $status 状态（1=生效中，2=已移除）
 * @property string $updated_at 更新时间
 * @property string $created_at 创建时间
 */
class HrOutSourcingBlacklist extends \yii\db\ActiveRecord
{
    const STATUS_IN_EFFECT = 1;//生效中
    const STATUS_REMOVED = 2;//已移除


    const TYPE_DEFAULT = 1;
    const TYPE_FACE_DETECTION = 2;//外协人脸作弊
    const TYPE_NOT_REFUND_TASK = 3;//未回款
    const REASON_CODE_NOT_REFUND_TASK = '10001';//未回款原因
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'hr_out_sourcing_blacklist';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('backyard');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['submitter_staff_id', 'type', 'remove_staff_id', 'status'], 'integer'],
            [['remark', 'remove_remark'], 'string'],
            [['remove_date', 'updated_at', 'created_at'], 'safe'],
            [['identity', 'name'], 'string', 'max' => 50],
            [['mobile_area_code'], 'string', 'max' => 5],
            [['mobile'], 'string', 'max' => 20],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'identity' => 'Identity',
            'name' => 'Name',
            'mobile_area_code' => 'Mobile Area Code',
            'mobile' => 'Mobile',
            'submitter_staff_id' => 'Submitter Staff ID',
            'type' => 'Type',
            'remark' => 'Remark',
            'remove_staff_id' => 'Remove Staff ID',
            'remove_date' => 'Remove Date',
            'remove_remark' => 'Remove Remark',
            'status' => 'Status',
            'updated_at' => 'Updated At',
            'created_at' => 'Created At',
        ];
    }
}
