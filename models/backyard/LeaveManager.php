<?php

namespace app\models\backyard;

use Yii;

/**
 * This is the model class for table "leave_manager".
 *
 * @property int $id 主键ID
 * @property int $staff_info_id 员工ID
 * @property string $created_at 首次创建时间
 * @property int $assets_remand_state 1 未处理 2 处理中 3 处理完成
 * @property int $assets_operate_version 当前资产管理操作版本
 * @property int $assets_operator_id 当前资产操作人id
 * @property string $assets_operate_time 当前资产操作时间
 * @property string $assets_operate_remark 资产操作备注
 * @property string $assets_attachments 资产编辑附件
 * @property int $is_has_superior 主管是否操作过0未操作 1操作过
 * @property int $money_remand_state 1 未处理 2 处理中 3 处理完成
 * @property int $money_operate_version 当前钱款管理操作版本
 * @property int $money_operator_id 当前钱款操作人id
 * @property string $money_operate_time 当前钱款操作时间
 * @property string $approval_time 同步审批状态时间
 * @property int $approval_status 1 待审批 2 已同意 3 已驳回 4 已撤销 5 已超时
 * @property string $leave_date
 * @property string $money_attachments 钱款编辑附件
 * @property int $superior_id 处理主管工号ID
 * @property string $superior_operate_time 主管处理时间
 * @property int $superior_operate_version 主管处理版本
 * @property string apply_resign_time 发起申请离职的时间
 */
class LeaveManager extends \yii\db\ActiveRecord
{
    const IS_NEW_ASSETS_REMAND_STATE_NO = 0;
    const IS_NEW_ASSETS_REMAND_STATE_YES = 1;

    const MONEY_STATE_UNPROCESSED = 1;


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'leave_manager';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('backyard_main');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['staff_info_id', 'assets_remand_state', 'assets_operate_version', 'assets_operator_id', 'is_has_superior', 'money_remand_state', 'money_operate_version', 'money_operator_id', 'approval_status', 'superior_id', 'superior_operate_version'], 'integer'],
            [['created_at', 'assets_operate_time', 'money_operate_time', 'approval_time', 'leave_date', 'superior_operate_time'], 'safe'],
            [['assets_operate_remark', 'assets_attachments', 'money_attachments'], 'string', 'max' => 500],
            [['staff_info_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'staff_info_id' => 'Staff Info ID',
            'created_at' => 'Created At',
            'assets_remand_state' => 'Assets Remand State',
            'assets_operate_version' => 'Assets Operate Version',
            'assets_operator_id' => 'Assets Operator ID',
            'assets_operate_time' => 'Assets Operate Time',
            'assets_operate_remark' => 'Assets Operate Remark',
            'assets_attachments' => 'Assets Attachments',
            'is_has_superior' => 'Is Has Superior',
            'money_remand_state' => 'Money Remand State',
            'money_operate_version' => 'Money Operate Version',
            'money_operator_id' => 'Money Operator ID',
            'money_operate_time' => 'Money Operate Time',
            'approval_time' => 'Approval Time',
            'approval_status' => 'Approval Status',
            'leave_date' => 'Leave Date',
            'money_attachments' => 'Money Attachments',
            'superior_id' => 'Superior ID',
            'superior_operate_time' => 'Superior Operate Time',
            'superior_operate_version' => 'Superior Operate Version',
        ];
    }
}
