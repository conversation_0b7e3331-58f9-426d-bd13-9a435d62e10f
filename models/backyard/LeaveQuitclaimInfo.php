<?php
/**
 * Author: Bruce
 * Date  : 2023-04-17 15:22
 * Description:
 */

namespace app\models\backyard;


use Yii;

/**
 * *
 * @property int $id
 * @property int leave_manager_log_id 离职记录主键id
 * @property int $staff_info_id 工号
 * @property int $is_new 是否最新
 * @property int $version 版本号
 * Class LeaveQuitclaimInfo
 * @package app\models\backyard
 */
class LeaveQuitclaimInfo extends \yii\db\ActiveRecord
{
    public static function tableName()
    {
        return 'leave_quitclaim_info';
    }

    /**
     * @return object|\yii\db\Connection|null
     * @throws \yii\base\InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('backyard');
    }

    //是否最新的一次离职
    const IS_NEW_YES = 1;
    const IS_NEW_NO = 0;

    const VERSION_1 = 1;//版本1
    const VERSION_2 = 2;//版本2
}