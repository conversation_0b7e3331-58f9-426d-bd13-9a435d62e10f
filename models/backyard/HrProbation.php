<?php

namespace app\models\backyard;

use Yii;
use yii\db\ActiveRecord;


class HrProbation extends ActiveRecord
{

    /**
     * ph 合同工 过 N 天转正条件日期
     */
    const PH_CONTRACT_REGULAR_DAYS = 90;
    /**
     * ph 合同工V3 过 N 天转正条件日期
     */
    const PH_CONTRACT_V3_REGULAR_DAYS = 150;
    /**
     * ph合同工入职第75天发送转正评估(入职日期当天算1天)
     * @var int
     */
    const PH_CONTRACT_STAFF_EVALUATE_DURATION = 75;
    /**
     * ph合同工v3入职第60天发送第二阶段转正评估(入职日期当天算1天)
     * ph合同工v3入职第120天发送第二阶段转正评估(入职日期当天算1天)
     * @var int
     */
    const PH_CONTRACT_STAFF_EVALUATE_DURATION_V3 = 60;
    const PH_CONTRACT_STAFF_EVALUATE_DURATION_V3_SECOND = 120;

    const STATUS_CORRECTED = 4; //已转正
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'hr_probation';
    }

    public static function getDb()
    {
        return Yii::$app->get('backyard_main');
    }

    public function rules()
    {
        return parent::rules(); // TODO: Change the autogenerated stub
    }

    /**
     * 转正评估要走那个流程类别，默认1，1=正式员工,2=非一线，3=合同工，4=合同工三期 5 合同工三期转正式
     */
    const PROBATION_CHANNEL_TYPE_FORMAL = 1; // 正式员工
    const PROBATION_CHANNEL_TYPE_NON_FRONTLINE = 2; // 非一线
    const PROBATION_CHANNEL_TYPE_CONTRACT = 3;
    const PROBATION_CHANNEL_TYPE_CONTRACT_V3 = 4;
    const PROBATION_CHANNEL_TYPE_CONTRACT_TO_FORMAL = 5;
}