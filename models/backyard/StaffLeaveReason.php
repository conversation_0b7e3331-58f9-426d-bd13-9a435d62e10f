<?php

namespace app\models\backyard;


use Yii;
class StaffLeaveReason extends \yii\db\ActiveRecord
{

    const STAFF_LEAVE_TYPE_1 = 1;//自愿离职
    const GROUP_TYPE_DEFAULT = 0; // 默认 暂未分组
    const GROUP_TYPE_5 = 5; // 收入低/福利问题
    const GROUP_TYPE_6 = 6; // 个人因素
    const GROUP_TYPE_7 = 7; // 培训问题
    const GROUP_TYPE_8 = 8; // 网点问题
    const GROUP_TYPE_9 = 9; // 处罚问题
    const GROUP_TYPE_10 = 10; // 系统问题
    const GROUP_TYPE_11 = 11; // 其他
    public static $agent_leave_reason = [
        self::GROUP_TYPE_5,
        self::GROUP_TYPE_6,
        self::GROUP_TYPE_7,
        self::GROUP_TYPE_8,
        self::GROUP_TYPE_9,
        self::GROUP_TYPE_10,
        self::GROUP_TYPE_11,
    ];
    
    public static function tableName()
    {
        return 'staff_leave_reason';
    }
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('backyard');
    }

}
