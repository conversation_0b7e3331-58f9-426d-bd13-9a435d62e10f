<?php

namespace app\models\backyard;


use Yii;
class StaffLeaveReason extends \yii\db\ActiveRecord
{
    const STAFF_LEAVE_TYPE_1 = 1;//自愿离职


    //离职原因
    const  LEAVE_REASON_SKIP_WORK = 21;
    const  LEAVE_REASON_FAKE_INFO= 25;//向公司提供虚假信息
    const  LEAVE_REASON_80 =  80;//腐败/滥用职权或故意对雇主实施刑事犯罪
    const  LEAVE_REASON_84 =  84;//未经正当理由连续超过三天未履行工作职责
    const  LEAVE_REASON_97 =  97;//公司解约个人代理
    const  LEAVE_REASON_33 =  33;//公司解约个人代理(旧)
    const  LEAVE_REASON_81 =  81;//粗心大意造成公司重大损失
    const  LEAVE_REASON_94 =  94;//退休
    const  LEAVE_REASON_88 =  88;//纪律处分
    const  LEAVE_REASON_93 =  93;//公司原因
    const  LEAVE_REASON_89 =  89;//员工试用期未通过（无需提前通知）



    const GROUP_TYPE_DEFAULT = 0; // 默认 暂未分组
    const GROUP_TYPE_5 = 5; // 收入低/福利问题
    const GROUP_TYPE_6 = 6; // 个人因素
    const GROUP_TYPE_7 = 7; // 培训问题
    const GROUP_TYPE_8 = 8; // 网点问题
    const GROUP_TYPE_9 = 9; // 处罚问题
    const GROUP_TYPE_10 = 10; // 系统问题
    const GROUP_TYPE_11 = 11; // 其他
    public static $agent_leave_reason = [
        self::GROUP_TYPE_5,
        self::GROUP_TYPE_6,
        self::GROUP_TYPE_7,
        self::GROUP_TYPE_8,
        self::GROUP_TYPE_9,
        self::GROUP_TYPE_10,
        self::GROUP_TYPE_11,
    ];
    
    public static function tableName()
    {
        return 'staff_leave_reason';
    }
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('backyard');
    }

    //离职原因版本
    const LEAVER_REASON_VERSION_NEW = 2;//新版


}
