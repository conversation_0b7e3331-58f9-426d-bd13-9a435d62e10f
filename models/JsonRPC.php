<?php

namespace app\models;

use app\libs\RestClient;
use app\libs\ValidationException;
use Exception;
use Graze\GuzzleHttp\JsonRpc\Client;
use Yii;

class JsonRPC extends yii\base\BaseObject
{
    public $uri;
    private $client;

    public function init()
    {
        $this->client = Client::factory($this->uri);
    }


    /**
     * @param $data
     * @return mixed
     * @throws Exception
     */
    public function genStaffInfoIdFromHcm($data)
    {
        $body = [
            'jsonrpc' => '2.0',
            'method'  => 'generateId',
            'params'  => [
                ['locale' => 'en'],
                $data,
            ],
            'id'      => time(),
        ];
        try {
            $re = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            return json_decode($re)->result;
        } catch (Exception $e) {
            Yii::$app->logger->write_log('genStaffInfoIdFromHcm 失败，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body,
                    JSON_UNESCAPED_UNICODE));
            throw $e;
        }
    }

    // 重置密码
    public function resetStaffPassword($data)
    {
        if (env('break_away_from_ms')) {
            //只有 越南 和印尼
            return $this->resetStaffPasswordHcm($data);
        }
        $params = [
            "staff_id"                => intval($data['staff_info_id']),
            "locale"                  => $data['lang'],
            "company_type"            => $data['company_type'] ?? 0,
            "equipment_type_category" => 16,
        ];
        $url = Yii::$app->params['nws_rest_endpoint'] . '/svc/staff/reset_password';
        $res = Yii::$app->http->httpPost($url, $params);
        Yii::$app->logger->write_log(['function' => 'resetStaffPassword', 'params' => $params, 'result' => $res,],'info');
        if (!isset($res['code']) || $res['code'] != 1) {
            throw new ValidationException($res['message'] ?? 'reset password error!');
        }
        return 1;
    }

    public function resetStaffPasswordHcm($data){
        $body = [
            'jsonrpc' => '2.0',
            'method'  => 'reset_password',
            'params'  => [
                ['locale' => 'en'],
                ['staff_info_id' => $data['staff_info_id']],
            ],
            'id'      => time(),
        ];
        $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
        $resObj = json_decode($res,true);
        Yii::$app->logger->write_log('重置密码：resetStaffPassword'.json_encode($data,JSON_UNESCAPED_UNICODE).';结果:'.json_encode($res,JSON_UNESCAPED_UNICODE),'info');
        return !empty($resObj['result']);
    }

    // 同步职位
    public function addJobTitle($data,$lang = 'zh-CN') {
        try {
            $params = array_merge([['locale' => $lang]], array_values($data));
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'addJobTitle',
                'params' => $params,
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['jrpc_remote'], $body);
            $res_arr = json_decode($res,true);
            $result = $res_arr['result'] ?? '';
            if(empty($result)) {
                $result = $res_arr['error']['message'] ?? '';
            }
            return $result;
        } catch (Exception $e) {
            Yii::$app->logger->write_log('同步 addJobTitle 失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';body:'.json_encode($body,JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    // 职位修改
    public function updateJobTitle($data, $lang = 'zh-CN') {
        try {
            $params = array_merge([['locale' => $lang]], array_values($data));
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'updateJobTitle',
                'params' => $params,
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['jrpc_remote'], $body);
            $res_arr = json_decode($res,true);
            $result = $res_arr['result'] ?? '';
            if(empty($result)) {
                $result = $res_arr['error']['message'] ?? '';
            }
            return $result;
        } catch (Exception $e) {
            Yii::$app->logger->write_log('同步 updateJobTitle 失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';body:'.json_encode($body,JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    //职位删除
    public function deleteJobTitle($data, $lang = 'zh-CN') {
        try {
            $params = array_merge([['locale' => $lang]], array_values($data));
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'deleteJobTitle',
                'params' => $params,
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['jrpc_remote'], $body);
            $res_arr = json_decode($res,true);
            $result = $res_arr['result'] ?? '';
            if(empty($result)) {
                $result = $res_arr['error']['message'] ?? '';
            }
            return $result;
        } catch (Exception $e) {
            Yii::$app->logger->write_log('同步 updateJobTitle 失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';body:'.json_encode($body,JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    public function getStoresOfArea()
    {
        return [];
    }

    public function smsSend($mobile, $msg, $src = '')
    {
        $prefix = 'hr_hris_';
        $client = Client::factory(\Yii::$app->params['sms_uri']);
        $params = [
            'mobile' => $mobile,
            'msg' => $msg,
            'type' => 0,
            'code' => 'th',
            'delay' => 0,
            'src' => $prefix . $src
        ];
        if (isCountry('PH')) {
            $params['nation']           = 'PH';
            $params['type']             = 0;// 文本消息， 固定类型
            $params['service_provider'] = 9;// 服务商 Viber 固定值
        }
        $body = [
            ['locale' => 'th-TH'],
            $params,
        ];
        try {
            $r = $client->send($client->request(time(), 'send', $body))->getRpcResult();
            Yii::$app->logger->write_log('短信发送成功;短信内容 : '.json_encode($body,JSON_UNESCAPED_UNICODE).';结果:'.json_encode($r,JSON_UNESCAPED_UNICODE),'info');
        } catch (Exception $e) {
            Yii::$app->logger->write_log('短信发送失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';短信内容:'.json_encode($body,JSON_UNESCAPED_UNICODE));
            return false;
        }

        return true;
    }

    public function backYardMessage($staffId, $msg, $title='แจ้งอีเมล์บริษัทเปิดแล้ว')
    {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'message_to_backyard',
            'params' => [
                ['locale' => 'th-TH'],
                [
                    'staff_info_id' => $staffId,
                    'title' => $title,
                    'content' => $msg,
                    'category' => '10',
                ],
            ],
            'id' => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['backyard_message_uri'], $body);
            Yii::$app->logger->write_log('backYardMessage：'.';结果:'.json_encode($res).';工号:'.$staffId,'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('backYardMessage发送失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';工号:'.$staffId);
            return false;
        }
    }

    public function staffLevelApproval($staff_info_id) {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'get_resign_detail',
            'params' => [
                ['locale' => 'th-TH'],
                [
                    'submitter_id' => $staff_info_id
                ],
            ],
            'id' => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hc_by_url'], $body);
            Yii::$app->logger->write_log('staffLevelApproval：'.';结果:'.json_encode($res).';工号:'.$staff_info_id,'info');
            $res_arr = json_decode($res,true);
            return $res_arr['result']['data'] ?? [];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('staffLevelApproval，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';工号:'.$staff_info_id);
            return false;
        }
    }

    //同步直线上级变更
    public function staffChangeManager($staff_info_id, $before_manager, $after_manager) {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'sync_changed_superior',
            'params' => [
                ['locale' => 'th-TH'],
                [
                    'staff_info_id' => $staff_info_id,
                    'before_manager' => $before_manager,
                    'after_manager' => $after_manager
                ],
            ],
            'id' => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hc_by_url'], $body);
            Yii::$app->logger->write_log('staffChangeManager：'.';结果:'.json_encode($res).';body:'.json_encode($body),'info');
            $res_arr = json_decode($res,true);
            return $res_arr['result']['data'] ?? [];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('staffLevelApproval，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';body:'.json_encode($body));
            return false;
        }
    }

    //同步给fleet工单信息
    public function fleetFlashDelivery($param,$lang = 'zh-CN') {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'flashDelivery',
            'params' => [
                ['locale' => $lang],
                $param,
            ],
            'id' => time(),
        ];

        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['fleet_svc'], $body);
            Yii::$app->logger->write_log('flashDelivery：同步参数:'.json_encode($body).';结果:'.json_encode($res) ,'info');
            $res_arr = json_decode($res,true);
            $msg_code = $res_arr['result']['code'] ?? -1;
            if($msg_code == 1) {
                return true;
            } else {
                Yii::$app->logger->write_log('flashDelivery，参数内容:'.json_encode($body).';同步失败;返回结果:'.json_encode($res));
                return false;
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('flashDelivery，参数内容:'.json_encode($body).';可能出现的原因' . $e->getMessage().';行号:'.$e->getLine());
            return false;
        }
    }

    public function staff_hold($params, $lang = 'zh-CN') {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'synchronize_hold',
            'params' => [
                ['locale' => $lang],
                [
                    'staff_info_id' => $params['staff_info_id'],//员工id
                    'type' =>  $params['type'],//hold类型(1.工资hold,2.提成hold,同时就传’1,2’)
                    'hold_reason' => $params['hold_reason'],//hold原因(BY申请离职)
                    'hold_remark' => $params['hold_remark'],//hold备注
                    'hold_time' => $params['hold_time'],//hold时间操作时间
                    'hold_source' => $params['hold_source'],//来源->离职管理
                ],
            ],
            'id' => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            Yii::$app->logger->write_log('staff_hold：'.';结果:'.json_encode($res).';工号:'.$params['staff_info_id'],'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('staff_hold，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';工号:'.$params['staff_info_id']);
            return false;
        }
    }

    public function staff_release_hold($params, $lang = 'zh-CN')
    {
        $body = [
            'jsonrpc' => '2.0',
            'method'  => 'sync_release_hold',
            'params'  => [
                ['locale' => $lang],
                [
                    'staff_info_id' => $params['staff_info_id'],//员工id
                    'hold_reason'   => $params['hold_reason'],  //hold原因
                    'hold_source'   => $params['hold_source'],  //来源->离职管理
                    'handle_hold'   => $params['handle_hold'],  //是否处理hold
                    'operator_id'   => $params['operator_id'],  //操作人
                ],
            ],
            'id'      => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            Yii::$app->logger->write_log(sprintf('staff_release_hold params:%s, 返回结果：%s', json_encode($params), json_encode($res)),'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('staff_release_hold，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';工号:'.$params['staff_info_id']);
            return false;
        }
    }

    //员工变在职 by申请来源hold 变更成已处理
    public function syncBackyardSourceHoldHandle($params, $lang = 'zh-CN'): bool
    {
        $body = [
            'jsonrpc' => '2.0',
            'method'  => 'sync_by_source_hold_handle',
            'params'  => [
                ['locale' => $lang],
                [
                    'staff_info_id' => $params['staff_info_id'],//员工id
                ],
            ],
            'id'      => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            Yii::$app->logger->write_log([
                'function' => 'sync_by_source_hold_handle',
                'result'   => $res,
                'params'   => $params,
            ], 'info');
            return true;
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'sync_by_source_hold_handle',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
                'params'   => $params,
            ]);
            return false;
        }
    }

    //变更网点组织负责人
    public function sync_update_organization_store_manager($params, $lang = 'zh-CN') {
        try {
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'update_organization_manager_store',
                'params' => [
                    ['locale' => $lang],
                    [
                        'store_id' => $params['store_id'],
                        'manager_id' => $params['manager_id'],
                        'operator_id' => $params['operator_id'],
                        'manager_position_state' => $params['manager_position_state'],
                    ],
                ],
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
            Yii::$app->logger->write_log('sync_update_organization_store_manager ; 结果:'.json_encode($res).';参数:'.json_encode($params),'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('sync_update_organization_store_manager，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';参数:'.json_encode($params));
            return false;
        }
    }

    //变更片区组织负责人
    public function sync_update_organization_piece_manager($params, $lang = 'zh-CN') {
        try {
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'update_organization_manager_piece',
                'params' => [
                    ['locale' => $lang],
                    [
                        'piece_id' => $params['piece_id'],
                        'manager_id' => $params['manager_id'],
                        'operator_id' => $params['operator_id'],
                        'manager_position_state' => $params['manager_position_state'],
                    ],
                ],
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
            Yii::$app->logger->write_log('sync_update_organization_piece_manager ;结果:'.json_encode($res).';参数:'.json_encode($params),'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('sync_update_organization_piece_manager，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';参数:'.json_encode($params));
            return false;
        }
    }

    //cfo部门及子部门人员变更部门
    public function sync_update_organization_cfo_department($params, $lang = 'zh-CN')
    {
        try {
            $body = [
                'jsonrpc' => '2.0',
                'method'  => 'financial_staff_changes',
                'params'  => [
                    ['locale' => $lang],
                    [
                        'operator_id'   => $params['operator_id'],
                        'department_id' => $params['department_id'],
                        'staff_info_id' => $params['staff_info_id'],
                    ],
                ],
                'id'      => time(),
            ];
            $res  = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
            Yii::$app->logger->write_log('financial_staff_changes ;结果:' . json_encode($res) . ';参数:' . json_encode($params), 'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('financial_staff_changes，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';参数:' . json_encode($params));
            return false;
        }
    }

    //变更大区组织负责人
    public function sync_update_organization_region_manager($params, $lang = 'zh-CN') {
        try {
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'update_organization_manager_region',
                'params' => [
                    ['locale' => $lang],
                    [
                        'region_id' => $params['region_id'],
                        'manager_id' => $params['manager_id'],
                        'operator_id' => $params['operator_id'],
                        'manager_position_state' => $params['manager_position_state'],
                    ],
                ],
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
            Yii::$app->logger->write_log('sync_update_organization_piece_manager ;结果:'.json_encode($res).';参数:'.json_encode($params),'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('sync_update_organization_piece_manager，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';参数:'.json_encode($params));
            return false;
        }
    }

    //变更部门负责人
    public function update_organization_manager_department_manager($params, $lang = 'zh-CN') {
        try {
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'update_organization_manager_department',
                'params' => [
                    ['locale' => $lang],
                    [
                        'department_id' => $params['department_id'],
                        'manager_id' => $params['manager_id'],
                        'operator_id' => $params['operator_id'],
                        'manager_position_state' => $params['manager_position_state'],
                    ],
                ],
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
            Yii::$app->logger->write_log('update_organization_manager_department_manager ;结果:'.json_encode($res).';参数:'.json_encode($params),'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('update_organization_manager_department_manager，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';参数:'.json_encode($params));
            return false;
        }
    }

    /**
     * 网点主管变动自动同步为网点负责人
     * @param $params
     * @param string $lang
     * @return bool
     */
    public function sync_change_store_supervisor_manager($params, string $lang = 'zh-CN'): bool
    {
        try {
            $body    = [
                'jsonrpc' => '2.0',
                'method'  => 'sync_store_supervisor_manager',
                'params'  => [
                    ['locale' => $lang],
                    [
                        'id'         => $params['id'],
                        'manager_id' => $params['manager_id'],
                        'user'       => $params['user'],
                        'manager_position_state' => $params['manager_position_state'] ?? null
                    ],
                ],
                'id'      => time(),
            ];
            $res     = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
            $res_arr = json_decode($res, true);
            $result  = $res_arr['result'] ?? [];
            if ($result['code'] != 1) {
                Yii::$app->logger->write_log([
                    'function' => 'sync_change_store_supervisor_manager',
                    'params'   => $params,
                    'result'   => $res_arr,
                ], 'notice');
                return false;
            }
            Yii::$app->logger->write_log([
                'function' => 'sync_change_store_supervisor_manager',
                'params'   => $params,
                'result'   => $res_arr,
            ], 'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log([
                'function'      => 'sync_change_store_supervisor_manager',
                'params'        => $params,
                'error_message' => $e->getMessage(),
                'line'          => $e->getLine(),
                'file'          => $e->getFile(),
            ]);
            return false;
        }
    }

    //hrbp 角色工号变更oa 人才盘点菜单 type=0删除人才盘点对应的菜单 1新增人才盘点对应菜单
    public function sync_update_hrbp_talent_review_permissions($staff_info_id, $type, $lang = 'zh-CN') {
        try {
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'sync_update_talent_review_permission',
                'params' => [
                    ['locale' => $lang],
                    [
                        'staff_info_id' => $staff_info_id,
                        'type' => $type
                    ],
                ],
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
            Yii::$app->logger->write_log('sync_update_talent_review_permission ;结果:'.json_encode($res).';参数:'.json_encode($body),'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('sync_update_talent_review_permission，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';参数:'.json_encode($body));
            return false;
        }
    }

    //班次更新 发送backyard消息
    public function update_staff_shift_send_message($staff_info_id,$message) {
        try {
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'add_kit_message',
                'params' => [
                    ['locale' => 'th-TH'],
                    [
                        'staff_users' => array($staff_info_id),
                        'message_title' => $message['title'],
                        'message_content' => $message['content'],
                        'staff_info_ids_str' => $staff_info_id,
                        'category' => '46',
                        'id' => time() . $staff_info_id . rand(1000000, 9999999)
                    ],
                ],
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            Yii::$app->logger->write_log('update_staff_shift_send_message ：'.';结果:'.json_encode($res).';工号:'.json_encode($staff_info_id) . '----message:' . json_encode($message), 'info');
            return $res;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('update_staff_shift_send_message 发送失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';工号:'.json_encode($staff_info_id) . '----message:' . json_encode($message));
            return false;
        }
    }



    //通知 hcm 变更管辖范围
    public function approvalJurisdictionChanges($staff_info_id = '', $operator_id = '') {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'approval_jurisdiction_changes',
            'params' => [
                ['locale' => 'th-TH'],
                [
                    'staff_info_id' => $staff_info_id,
                    'operator_id' => $operator_id,
                    'type' => 2,
                ],
            ],
            'id' => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            Yii::$app->logger->write_log('approvalJurisdictionChanges：'.';结果:'.json_encode($res).';body:'.json_encode($body),'info');
            $res_arr = json_decode($res,true);
            return $res_arr['result']['data'] ?? [];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('approvalJurisdictionChanges，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';body:'.json_encode($body));
            return false;
        }
    }


    //员工职位部门变更发送邮件
    public function staffChangeSendEmail($params)
    {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'staff_change_send_email',
            'params' => [
                ['locale' => 'zh'],
                $params,
            ],
            'id' => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            Yii::$app->logger->write_log('staffChangeSendEmail：' . ';结果:' . json_encode($res) . ';body:' . json_encode($body),
                'info');
            $res_arr = json_decode($res, true);
            $msg_code = $res_arr['result']['code'] ?? -1;
            if ($msg_code != 1) {
                Yii::$app->logger->write_log('staffChangeSendEmail，参数内容:' . json_encode($body) . ';返回结果:' . json_encode($res),
                    'notice');
            }
            return $res_arr['result']['data'] ?? [];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('staffChangeSendEmail，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body));
            return false;
        }
    }

    public function changeWorkingDayRestDay($params)
    {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'del_staff_off_ph_day',
            'params' => [
                ['locale' => 'th-TH'],
                [
                    'staff_info_id' => $params['staff_info_id'],
                    'date_at' => $params['date_at'],
                    'remark' => $params['remark'],
                    'is_create' => $params['is_create']??false,
                    'default_rest_day_date' => $params['default_rest_day_date']??[],
                ],
            ],
            'id' => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            Yii::$app->logger->write_log('changeWorkingDayRestDay：' . ';结果:' . json_encode($res) . ';body:' . json_encode($body),
                'info');
            return $res;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('changeWorkingDayRestDay，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body));
            return false;
        }
    }

    /**
     * 更新排班建议
     * @param $params
     * @return false
     */
    public function updateSchedulingSuggestNumber($params)
    {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'updateSchedulingSuggestNumber',
            'params' => [
                ['locale' => 'th-TH'],
                [
                    'store_ids' => $params['store_ids'],
                ],
            ],
            'id' => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            Yii::$app->logger->write_log('updateSchedulingSuggestNumber：' . ';结果:' . json_encode($res) . ';body:' . json_encode($body),
                'info');
            return $res;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('updateSchedulingSuggestNumber，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body));
            return false;
        }
    }

    /**
     * 直线上级变更时 回调kpi接口 需要做权限的变更 发送变更通知等
     * @param $params
     * @param $source
     * @param string $lang
     * @return void
     * <AUTHOR>
     */
    public function changeLeader($params, $source, $lang = 'zh-CN')
    {
        $body = [
            'jsonrpc' => '2.0',
            'method'  => 'change_leader',
            'params'  => [
                ['locale' => $lang],
                [
                    'change_leader' => $params['change_leader'],
                ],
            ],
            'id'      => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
            Yii::$app->logger->write_log('source:' . $source . '; changeLeader：'.'; 结果:'.json_encode($res).'; body:'.json_encode($body),
                'info');
            return $res;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('source:' . $source . '; changeLeader，可能出现的原因'.$e->getMessage().';行号:'.$e->getLine().';body:'.json_encode($body));
            return false;
        }
    }


    // 向osm 发送push
    public function sendPushToOsm($params, $lang = 'zh-CN') {
        try {
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'push.pushToDevices',
                'params' => [
                    [
                        'locale' => $lang
                    ],
                    [
                        'list' => $params
                    ],
                ],
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['bi_rpc_endpoint_v2'], $body);
            $res_arr = json_decode($res,true);
            $result = $res_arr['result'] ?? [];
            if(!empty($result) && $result['code'] == 1) {
                Yii::$app->logger->write_log('sendPushToOsm ;成功结果:'.json_encode($res).';参数:'.json_encode($body),'info');
                return true;
            }
            Yii::$app->logger->write_log('sendPushToOsm ;失败结果:'.json_encode($res).';参数:'.json_encode($body),'notice');
            return false;

        } catch (\Exception $e) {
            Yii::$app->logger->write_log('sendPushToOsm，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';参数:'.json_encode($body));
            return false;
        }
    }

    //同步ms 支援信息
    public function syncMsStaffSupportApply($params, $lang = 'zh-CN') {
        $url = Yii::$app->params['nws_rest_endpoint'] . '/svc/staff/generate/editStaffSupportInfo';
        $res = Yii::$app->http->httpPost($url, $params);
        Yii::$app->logger->write_log([
            'function' => 'syncMsStaffSupportApply',
            'params'   => $params,
            'result'   => $res,
        ], 'info');
        if (isset($res['code']) && $res['code'] == 1) {
            return true;
        }
        throw new Exception($res['message'] ?? 'generate editStaffSupportInfo error!');
    }

    /**
     * Backyard消息发送 公共方法
     * @param $params
     * @return bool
     */
    public function sendBackyardMessage($params): bool
    {
        try {
            $staff_info_id   = $params['staff_info_id'];
            $message_title   = $params['message_title'];
            $message_content = $params['message_content'];
            $category        = $params['category'];

            $body   = [
                'jsonrpc' => '2.0',
                'method'  => 'add_kit_message',
                'params'  => [
                    ['locale' => 'th-TH'],
                    [
                        'staff_users'        => [$staff_info_id],
                        'message_title'      => $message_title,
                        'message_content'    => $message_content,
                        'staff_info_ids_str' => $staff_info_id,
                        'category'           => $category,
                        'id'                 => time().$staff_info_id.rand(1000000, 9999999),
                    ],
                ],
                'id'      => time(),
            ];
            $result = Yii::$app->http->json_rpc_post(Yii::$app->params['backyard_message_uri'], $body);
            $result_arr = json_decode($result, true);
            Yii::$app->logger->write_log([
                'function' => 'sendBackyardMessage',
                'params'   => $params,
                'result'   => $result_arr,
            ], 'info');

            if (isset($result_arr['result']['code']) && $result_arr['result']['code'] == 1) {
                return true;
            } else {
                Yii::$app->logger->write_log([
                    'function' => 'sendBackyardMessage',
                    'params'   => $params,
                    'result'   => $result_arr,
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'sendBackyardMessage',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
                'params'   => $params,
            ]);
            return false;
        }
    }

    /**
     * 发送Backyard push 公共方法
     * @param $params
     * @return bool
     */
    public function sendBackyardPushToStaff($params): bool
    {
        try {
            $staff_info_id = $params['staff_info_id'];
            $push_title    = $params['push_title'];
            $push_content  = $params['push_content'];
            $src           = $params['src'] ?? 'backyard';

            $body   = [
                'jsonrpc' => '2.0',
                'method'  => 'push_to_staff',
                'params'  => [
                    ['locale' => 'th-TH'],
                    [
                        "staff_info_id"   => $staff_info_id,  //推送人员工ID
                        "src"             => $src,            //1:'kit'; 2:'backyard','c';
                        "message_title"   => $push_title,     //标题
                        "message_content" => $push_content,   //内容
                        'message_scheme'  => "flashbackyard://fe/tab?index=message",
                    ]
                ],
                'id'      => time(),
            ];
            $result = Yii::$app->http->json_rpc_post(Yii::$app->params['backyard_message_uri'], $body);
            $result_arr = json_decode($result, true);
            Yii::$app->logger->write_log([
                'function' => 'sendBackyardPushToStaff',
                'params'   => $params,
                'result'   => $result_arr,
                'jsonrpc_params' => $body
            ], 'info');

            if ($result_arr['result'] === true) {
                return true;
            } else {
                Yii::$app->logger->write_log([
                    'function' => 'sendBackyardPushToStaff',
                    'params'   => $params,
                    'result'   => $result,
                ],'info');
                return false;
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'sendBackyardPushToStaff',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
                'params'   => $params,
            ]);
            return false;
        }
    }

    /**
     * 发送外协协议-马来使用
     * @param $params
     * @return bool
     */
    public function sendArgeementSignPushToStaff($params): bool
    {
        try {
            $body   = [
                'jsonrpc' => '2.0',
                'method'  => 'push_staff_argeement_sign',
                'params'  => [
                    ['locale' => 'th-TH'],
                    [
                        "staff_info_id" => $params['staff_info_id'],
                        "operator_id"   => $params['operator_id'],
                    ],
                ],
                'id'      => time(),
            ];
            $result = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);

            Yii::$app->logger->write_log([
                'function' => 'sendArgeementSignPushToStaff',
                'params'   => $params,
                'result'   => $result,
            ], 'info');

            $resultArr = json_decode($result, true);
            if (!isset($resultArr['result']['code']) || ($resultArr['result']['code'] != 1)) {
                Yii::$app->logger->write_log([
                    'function' => 'sendArgeementSignPushToStaff',
                    'params'   => $params,
                    'result'   => $resultArr,
                ]);
                return false;
            }
            return true;
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'sendArgeementSignPushToStaff',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
                'params'   => $params,
            ]);
            return false;
        }
    }

    //员工职位部门变更发送邮件
    public function staffSuspensionSendEmail($params)
    {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'suspension_send_email',
            'params' => [
                ['locale' => 'zh'],
                $params,
            ],
            'id' => time(),
        ];
        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            Yii::$app->logger->write_log('staffSuspensionSendEmail：' . ';结果:' . json_encode($res) . ';body:' . json_encode($body),
                'info');
            $res_arr = json_decode($res, true);
            $msg_code = $res_arr['result']['code'] ?? -1;
            if ($msg_code != 1) {
                Yii::$app->logger->write_log('staffSuspensionSendEmail，参数内容:' . json_encode($body) . ';返回结果:' . json_encode($res),
                    'notice');
            }
            return $res_arr['result']['data'] ?? [];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('staffSuspensionSendEmail，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body));
            return false;
        }
    }

    //获取员工未归还钱款
    public function getStaffOutstandingMoney($params) {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'staff_outstanding_money',
            'params' => [
                ['locale' => 'zh'],
                $params,
            ],
            'id' => time(),
        ];

        try {
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            $res_arr = json_decode($res, true);
            Yii::$app->logger->write_log([
                'function' => 'getStaffOutstandingMoney',
                'params' => $params,
                'result' => $res_arr
            ], 'info');
            $msg_code = $res_arr['result']['code'] ?? -1;
            if ($msg_code != 1) {
                Yii::$app->logger->write_log([
                    'function' => 'getStaffOutstandingMoney',
                    'params' => $params,
                    'result' => $res
                ], 'notice');
            }
            return $res_arr['result']['data'] ?? [];
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'getStaffOutstandingMoney',
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return [];
        }
    }

    //根据dict_code获取字典列表(用于编辑或获取下拉列表)
    public function getDictionaryByDictCode($params, $lang)
    {
        try {
            $cache_key = 'HRIS_getDictionaryByDictCode_' . $params['dict_code'] . '_'. $lang;
            $cache = Yii::$app->cache->redis->get($cache_key);
            if(!empty($cache)) {
                return json_decode($cache, true);
            }

            $params = array_merge([['locale' => $lang]], [$params]);
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'getDictionaryByDictCode',
                'params' => $params,
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);

            Yii::$app->logger->write_log([
                'function' => 'getDictionaryByDictCode',
                'params'   => $params,
                'result'   => $res,
            ], 'info');
            $result_arr = json_decode($res, true);

            if (YII_ENV != 'pro') {
                $cacheTimeSec = 60;
            } else {
                $cacheTimeSec = 3600;
            }
            Yii::$app->cache->redis->setex($cache_key, $cacheTimeSec, json_encode($result_arr['result']['data'] ?? [], JSON_UNESCAPED_UNICODE));

            return $result_arr['result']['data'] ?? [];
        } catch (Exception $e) {
            Yii::$app->logger->write_log('同步 getDictionaryByDictCode 失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';body:'.json_encode($body,JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    //根据dict_code获取全部字典列表(用于解析历史数据)
    public function getTotalDictionaryItemsByDictCode($params, $lang)
    {
        try {
            $cache_key = 'HRIS_getTotalDictionaryItemsByDictCode' . $params['dict_code'] . '_'. $lang;
            $cache = Yii::$app->cache->redis->get($cache_key);
            if(!empty($cache)) {
                return json_decode($cache, true);
            }

            $params = array_merge([['locale' => $lang]], [$params]);
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'getTotalDictionaryItemsByDictCode',
                'params' => $params,
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);

            Yii::$app->logger->write_log([
                'function' => 'getTotalDictionaryItemsByDictCode',
                'params'   => $params,
                'result'   => $res,
            ], 'info');
            $result_arr = json_decode($res, true);

            if (YII_ENV != 'pro') {
                $cacheTimeSec = 60;
            } else {
                $cacheTimeSec = 3600;
            }
            Yii::$app->cache->redis->setex($cache_key, $cacheTimeSec, json_encode($result_arr['result']['data'] ?? [], JSON_UNESCAPED_UNICODE));

            return $result_arr['result']['data'] ?? [];
        } catch (Exception $e) {
            Yii::$app->logger->write_log('同步 getTotalDictionaryItemsByDictCode 失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';body:'.json_encode($body,JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    /**
     * 停职恢复在职调用backyard接口给员工 上级 hrbp 发送消息push
     * @param $params
     * @param string $locale
     * @return bool
     */
    public function suspendedReinstatementSendMsg($params, string $locale = 'th-TH'): bool
    {
        try {
            $body   = [
                'jsonrpc' => '2.0',
                'method'  => 'suspendedReinstatementSendMsg',
                'params'  => [
                    ['locale' => $locale],
                    $params
                ],
                'id'      => time(),
            ];
            $result = Yii::$app->http->json_rpc_post(Yii::$app->params['hc_by_url'], $body);
            $result_arr = json_decode($result, true);
            Yii::$app->logger->write_log([
                'function' => 'suspendedReinstatementSendMsg',
                'params'   => $params,
                'result'   => $result_arr,
                'jsonrpc_params' => $body
            ], 'info');

            if ($result_arr['result'] === true) {
                return true;
            } else {
                Yii::$app->logger->write_log([
                    'function' => 'suspendedReinstatementSendMsg',
                    'params'   => $params,
                    'result'   => $result,
                ],'notice');
                return false;
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'suspendedReinstatementSendMsg',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
                'params'   => $params,
            ]);
            return false;
        }
    }

    /**
     * 获取外协公司枚举
     * @param array $params
     * @param string $lang
     * @return array|bool|mixed
     */
    public function getOsCompanyList($params = [], string $lang = 'th-TH')
    {
        try {
            $cache_key = 'outsource_company';
            $cache = Yii::$app->cache->redis->get($cache_key);
            if(!empty($cache)) {
                return json_decode($cache, true);
            }

            $params = array_merge([['locale' => $lang]], [$params]);
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'outsource_company',
                'params' => $params,
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);

            Yii::$app->logger->write_log([
                'function' => 'outsource_company',
                'params'   => $params,
                'result'   => $res,
            ], 'info');
            $result_arr = json_decode($res, true);

            if (YII_ENV != 'pro') {
                $cacheTimeSec = 60;
            } else {
                $cacheTimeSec = 3600;
            }
            Yii::$app->cache->redis->setex($cache_key, $cacheTimeSec, json_encode($result_arr['result']['data'] ?? [], JSON_UNESCAPED_UNICODE));

            return $result_arr['result']['data'] ?? [];
        } catch (Exception $e) {
            Yii::$app->logger->write_log('同步 outsource_company 失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';body:'.json_encode($body,JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    //获取职位性质，成本类型枚举
    public function get_position_cost_list($lang = 'zh-CN') {
        try {
            $body = [
                'jsonrpc' => '2.0',
                'method' => 'get_position_cost_list',
                'params' => [
                    ['locale' => $lang],
                    [],
                ],
                'id' => time(),
            ];
            $res = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
            $result_arr = json_decode($res, true);
            Yii::$app->logger->write_log('get_position_cost_list ;结果:'.json_encode($result_arr).';','info');
            return $result_arr['result']['data'] ?? [];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('get_position_cost_list，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';参数:'.json_encode($params));
            return [];
        }
    }
    /**
     * 员工离职 复制hc
     * @param $staff_info_id
     * @return array|false|mixed
     */
    public function copyLeaveStaffHc($staff_info_id) {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'face_blacklist_hit_copy_hc',
            'params' => [
                ['locale' => 'th-TH'],
                [
                    'staff_info_id' => $staff_info_id,
                ],
            ],
            'id' => time(),
        ];
        try {
            $res     = Yii::$app->http->json_rpc_post(Yii::$app->params['hc_by_url'], $body);
            $res_arr = json_decode($res, true);
            if (empty($res_arr['result']['data'])) {
                Yii::$app->logger->write_log(['copyLeaveStaffHc 返回异常' => $body], 'error');
            }
            Yii::$app->logger->write_log(['copyLeaveStaffHc' => $body, 'result' => $res_arr], 'info');
            return $res_arr['result']['data'];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('copyLeaveStaffHc，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body));
            return false;
        }
    }

    /**
     * 离职原因
     * @param $leave_reason
     * @param string $lang
     * @return array|false|mixed
     */
    public function leaveReasonText($leave_reason,$lang='en') {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'get_leave_source_reason',
            'params' => [
                ['locale' => $lang],
                [
                    'leave_reason' => $leave_reason,
                ],
            ],
            'id' => time(),
        ];
        try {
            $res     = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            $res_arr = json_decode($res, true);

            if (empty($res_arr['result']['leave_reason_text'])) {
                Yii::$app->logger->write_log(['leaveReasonText 返回异常' => $body], 'error');
            }
            Yii::$app->logger->write_log(['leaveReasonText' => $body, 'result' => $res_arr], 'info');
            return $res_arr['result']['leave_reason_text'];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('leaveReasonText，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body));
            return false;
        }
    }

    /**
     *
     * @param $data
     * @param $lang
     * @return array|false|mixed
     */
    public function vehicleInspectionToSubMessage($data, $lang='en') {
        $body = [
            'jsonrpc' => '2.0',
            'method' => 'send_vehicle_message_to_sub',
            'params' => [
                ['locale' => $lang],
                [
                    'staff_info_id'     => $data['staff_info_id'],
                    'sub_staff_info_id' =>  $data['sub_staff_info_id'],
                ],
            ],
            'id' => time(),
        ];
        try {
            $res     = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            $res_arr = json_decode($res, true);

            if (empty($res_arr['result']['code'])) {
                Yii::$app->logger->write_log(['vehicleInspectionToSubMessage 返回异常' => $body], 'error');
            }
            Yii::$app->logger->write_log(['vehicleInspectionToSubMessage' => $body, 'result' => $res_arr], 'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('vehicleInspectionToSubMessage，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body));
            return false;
        }
    }
}
