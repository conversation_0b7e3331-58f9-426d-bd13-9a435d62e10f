<template>
  <div class="batch_upd">
    <Card :title="cardTitle">
      <Row>
        for example：({{$t('batch_upd_tip')}})
        <table style="text-align:center">
          <tr>
            <td>staff_info_id</td><td>immediate_supervisor</td><td>identity</td><td>bank_no</td>
            <td>state</td>
            <td>leave_date</td>
            <td>stop_duties_date</td>
            <td>personal_email</td>
          </tr>
          <tr>
            <td>1</td><td>17232</td><td>**********</td><td>**********</td>
            <td>2</td>
            <td>2012-12-12</td>
            <td>2012-12-12</td>
            <td><EMAIL></td>
          </tr>
        </table>
      </Row>
      <Row>
        <Upload action="" :before-upload="handleBeforeUpload" accept=".xls, .xlsx,.csv">
          <Button icon="ios-cloud-upload-outline" :loading="uploadLoading" @click="handleUploadFile">Upload Excel</Button>
        </Upload>
      </Row>
      <Row>
        <div class="ivu-upload-list-file" v-if="file !== null">
          <Icon type="ios-stats"></Icon>
          {{ file.name }}
          <Icon v-show="showRemoveFile" type="ios-close" class="ivu-upload-list-remove" @click.native="handleRemove()"></Icon>
        </div>
      </Row>
      <Row>
        <transition name="fade">
          <Progress v-if="showProgress" :percent="progressPercent" :stroke-width="2">
            <div v-if="progressPercent == 100">
              <Icon type="ios-checkmark-circle"></Icon>
              <span>success</span>
            </div>
          </Progress>
        </transition>
      </Row>
      <Row class="margin-top-10">
        <Table :columns="tableTitle" :data="tableData" :loading="tableLoading"></Table>
      </Row>
    </Card>
  </div>
</template>
<script>
import excel from '@/libs/excel'
import api from '@/libs/api.request'
export default {
  data() {
    return {
      uploadLoading: false,
      progressPercent: 0,
      showProgress: false,
      showRemoveFile: false,
      file: null,
      tableData: [],
      tableTitle: [],
      tableLoading: false
    }
  },
  computed: {
    cardTitle() {
      return `Import Excel update excel ：staff_info_id (staff ID)，immediate_supervisor (${this.$t('imm_supervisor')})，
      identity (${this.$t('idtentity')})，bank_no (${this.$t('bank_card_number')})，state (${this.$t('state')}：2 ${this.$t('state_2')}，3 ${this.$t('state_3')})，
      leave_date (${this.$t('leave_date')})，stop_duties_date (${this.$t('suspension_date')})，personal_email (${this.$t('personal_email')})`
    }
  },
  methods: {
    initUpload() {
      this.file = null
      this.showProgress = false
      this.loadingProgress = 0
      this.tableData = []
      this.tableTitle = []
    },
    handleUploadFile() {
      this.initUpload()
    },
    handleRemove() {
      this.initUpload()
      this.$Message.info('Uploaded files deleted!')
    },
    handleBeforeUpload(file) {
      const fileExt = file.name.split('.').pop().toLocaleLowerCase()
      if (fileExt === 'xlsx' || fileExt === 'xls' || fileExt === 'csv') {
        this.readFile(file)
        this.file = file
      } else {
        this.$Notice.warning({
          title: 'error',
          desc: 'files:' + file.name + 'Instead of an EXCEL file, select an EXCEL file suffixed with. xlsx or. xls。'
        })
      }
      return false
    },
    trimObjKey(obj) {
        for (let key in obj) {
            if (/(\s|\-)+/g.test(key)) {
                let keys = key.replace(/(\s|\-)+/g, "")
                obj[keys] = obj[key]
                delete obj[key]
            }
        }
        return obj
    },
    // 读取文件
    readFile(file) {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file)
      reader.onloadstart = e => {
        this.uploadLoading = true
        this.tableLoading = true
        this.showProgress = true
      }
      reader.onprogress = e => {
        this.progressPercent = Math.round(e.loaded / e.total * 100)
      }
      reader.onerror = e => {
        this.$Message.error('error')
      }
      reader.onload = e => {
        this.$Message.info('success')
        const data = e.target.result
        let { header, results } = excel.read(data, 'array')
        const tableTitle = [
          { title: 'staff_info_id', key: 'staff_info_id' },
          { title: 'immediate_supervisor', key: 'immediate_supervisor' },
          { title: 'identity', key: 'identity' },
          { title: 'bank_no', key: 'bank_no' },
          { title: 'state', key: 'state' },
          { title: 'leave_date', key: 'leave_date' },
          { title: 'stop_duties_date', key: 'stop_duties_date' },
          { title: 'personal_email', key: 'personal_email' },
          { title: 'result', key: 'result' }
        ]
        // console.log(excel.read(data, 'array'))
        this.tableTitle = tableTitle
        let filter = results.map(item => {
          item = this.trimObjKey(item)
          return { 
            staff_info_id: item.staff_info_id, 
            immediate_supervisor: item.immediate_supervisor, 
            identity: item.identity,
            bank_no:item.bank_no ,
            state:item.state ,
            leave_date:item.leave_date,
            stop_duties_date:item.stop_duties_date,
            personal_email: item.personal_email
          }
        })
        console.log(filter)
        api.request({ url: 'staffs/batch-upd', method: 'post', data: filter }).then(res => {
          this.tableData = res.data.body
          this.uploadLoading = false
          this.tableLoading = false
          this.showRemoveFile = true
        })
      }
    }
  },
  created() {

  },
  mounted() {

  }
}

</script>
<style>
  .batch_upd td{
    border: 1px solid #ccc;
  }
  .batch_upd .ivu-card-head p {
    overflow: visible;
    white-space: normal;
  }
</style>
