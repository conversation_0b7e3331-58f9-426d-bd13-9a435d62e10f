<?php

namespace app\services\th;

use app\models\backyard\HrBlackGreyList;
use app\models\backyard\HrOutSourcingBlacklist;
use app\models\backyard\OsStaffInfoExtend;
use app\models\backyard\SettingEnv;
use app\models\manage\BaseStaffInfo;
use app\services\base\OutSourcingService as BaseOutSourcingService;
use app\services\base\SettingEnvService;
use Yii;

class OutSourcingService extends BaseOutSourcingService
{
    public $identity_match   = '/^[A-Za-z0-9]{1,30}$/';
    public $mobile_match     = '/^\d{10}$/';
    public $bank_no_match    = '/^\d{1,30}$/';
    public $validate_job_ids = [13, 110, 1000];

    //验证提交字段
    public function validateOutSourcingField($staffs, $job_id, $hire_os_type = 11)
    {
        $com = $this->commonValidateOutsourcingField($staffs, $job_id, $hire_os_type);

        if ($com !== true) {
            return $com;
        }
        //快递员Bike Courier  Van Courier Tricycle Courier Car Courier Truck Driver驾驶证号
        if (in_array($job_id, $this->validate_job_ids) &&
            $hire_os_type == BaseStaffInfo::HIRE_OS_TYPE_COMMON &&
            !preg_match("/^([\x{0E00}-\x{0E7F}]|[0-9 -\/]|![a-zA-Z]){2,20}?$/u", $staffs['car_no'])) {
            return ['car_no', 'outsourcing_tip4'];
        }

        //premier公司 身份证 必须为 13位数字
        if($staffs['company_item_id'] == self::COMPANY_ITEM_PREMIER && !preg_match(OutSourcingService::PREG_MATCH_OS_COMPANY_PREMIER, $staffs['identity'])) {
            return ['att_os_staff_identity', 'os_staff_identity_length_premier'];
        }
        return true;
    }

    /**
     * @description:验证外协身份证号在正式员工数据中是否有异常
     * @param $identity
     * @param string $hire_type
     * @param array $black_origin_enum //    黑名单来源在 黑名单校验枚举  内 校验
     * @return array|string[]|true
     * @throws \Exception
     */
    public function validateOutSourcingIdentity($identity, $hire_type = '', $black_origin_enum = [])
    {
        if (empty($identity)) {
            return true;
        }

        //外协黑名单
        $hr_black = HrOutSourcingBlacklist::find()->where(['identity' => $identity])->andWhere(['status' => 1])->one();
        if (!empty($hr_black)) {
            return ['outsourcing_tip25'];
        }

        $staff_info = BaseStaffInfo::find()->where([
            'identity'     => $identity,
            'formal'       => [1, 4,],
            'is_sub_staff' => 0,
        ])->orderBy(['staff_info_id' => SORT_DESC])->asArray()->one();

        if (empty($staff_info)) {
            return true;
        }

        if ($staff_info['state'] == 2) {
            $setting_env          = SettingEnv::find()->where([
                'code' => [
                    'hris_os_leave_date_num',
                    'hris_os_leave_reason',
                ],
            ])->indexBy('code')->asArray()->all();
            $limit                = $setting_env['hris_os_leave_date_num']['set_val'] ?? 30;
            $hris_os_leave_reason = $setting_env['hris_os_leave_reason']['set_val'] ?? [];

            $hr_black = HrBlackGreyList::find()
                ->select(['source_code'])
                ->distinct()
                ->where(['identity' => $identity])
                ->andWhere(['behavior_type'=>HrBlackGreyList::BEHAVIOR_TYPE_BLACK])
                ->andWhere(['status' => HrBlackGreyList::STATUS_ON])
                ->asArray()
                ->all();

            if (!empty($hr_black)) {
                //获取黑名单来源
                $hr_black_types = array_column($hr_black, 'source_code');

                //是否需要检验 指定离职原因
                if (empty($hris_os_leave_reason)) {
                    //非众包外协  验证黑名单
                    if ($hire_type != BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING) {
                        return ['outsourcing_tip25'];
                    }
                    //众包外协创建 当黑名单来源在入参中黑名单枚举值中：不允许创建工号
                    if (empty($black_origin_enum) || !empty(array_intersect($black_origin_enum, $hr_black_types))) {
                        return ['outsourcing_tip25'];
                    }
                } else {
                    $leave_reason = explode(',', $hris_os_leave_reason);
                    if (in_array($staff_info['leave_reason'], $leave_reason)) {
                        //非众包外协  验证黑名单
                        if ($hire_type != BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING) {
                            return ['outsourcing_tip25'];
                        }

                        //众包外协创建 当黑名单来源在入参中黑名单枚举值中：不允许创建工号
                        if (empty($black_origin_enum) || !empty(array_intersect($black_origin_enum, $hr_black_types))) {
                            return ['outsourcing_tip25'];
                        }
                    }
                }
            }

            $diffDate = (new \DateTime())->diff((new \DateTime($staff_info['leave_date'])))->format('%a');
            if ($diffDate <= $limit) {
                $error_message = Yii::$app->lang->get('outsourcing_tip27');
                $error_message = str_replace("{day_count}", $limit, $error_message);
                return [$error_message];
            }
        } else {
            //-- 110 Van courier / 13 Bike courier / 452 Boat courier/ 271 Hub Staff / 98 Shop Officer
            //不配代表所有职位都允许做外协，配置-1代表所有职位都不允许做外协）
            $val = SettingEnvService::getInstance()->getSetVal('staff_not_apply_outsourcing_job_title_id');
            if(!empty($val)) {
                if($val === '-1') {
                    return ['outsourcing_tip23'];
                }
                //[13, 98, 110, 271, 452]
                $setting_env_job_title_id = explode(',', $val);
                if (in_array($staff_info['job_title'], $setting_env_job_title_id)) {
                    return ['outsourcing_tip23'];
                }
            }
        }
        return true;
    }

    /**
     * 重新启用工号，将原来的户口信息，加进去
     * @param $before
     * @param $staff_arr
     * @return mixed
     */
    public static function addRegisterInfo($before, $staff_arr)
    {
        $staff_arr['register_country']     = $before['register_country'] ?? '';
        $staff_arr['register_province']    = $before['register_province'] ?? '';
        $staff_arr['register_city']        = $before['register_city'] ?? '';
        $staff_arr['register_district']    = $before['register_district'] ?? '';
        $staff_arr['register_postcodes']   = $before['register_postcodes'] ?? '';
        $staff_arr['register_house_num']   = $before['register_house_num'] ?? '';
        $staff_arr['register_village_num'] = $before['register_village_num'] ?? '';
        $staff_arr['register_village']     = $before['register_village'] ?? '';
        $staff_arr['register_alley']       = $before['register_alley'] ?? '';
        $staff_arr['register_street']      = $before['register_street'] ?? '';

        return $staff_arr;
    }

    /**
     * 外协员工拓展信息
     * @param $data
     * @return bool
     */
    public static function addOsStaffInfoExtend($data)
    {
        if(empty($data['staff_info_id'])) {
            return false;
        }
        $osInfo = OsStaffInfoExtend::find()->where(['staff_info_id' => $data['staff_info_id']])->asArray()->one();
        if($osInfo) {
            OsStaffInfoExtend::updateAll($data, ['id' => $osInfo['id']]);

        } else {
            $osStaffInfoExtendModel = new OsStaffInfoExtend();
            $osStaffInfoExtendModel->staff_info_id = $data['staff_info_id'];
            $osStaffInfoExtendModel->company_item_id = $data['company_item_id'];
            $osStaffInfoExtendModel->is_complete_address = $data['is_complete_address'];
            $osStaffInfoExtendModel->save();
        }

        Yii::$app->logger->write_log(['addOsStaffInfoExtend-params' => ['before' => $osInfo, 'after' => $data]],'info');

        return true;
    }

    /**
     * 获取外协公司枚举信息
     * @param $companyId
     * @return string
     */
    public static function getOsCompanyEnums($companyId)
    {
        if(empty(self::$os_company_enums)) {
            $outsourceCompanyList = Yii::$app->jrpc->getOsCompanyList();
            self::$os_company_enums = !empty($outsourceCompanyList) ? array_column($outsourceCompanyList, 'label', 'value') : [];
        }

        return isset(self::$os_company_enums[$companyId]) ? self::$os_company_enums[$companyId] : '';
    }


}