<?php

namespace app\services\th;

use app\models\backyard\StaffPayrollCompanyInfo;
use app\models\backyard\SysDepartment;
use app\models\manage\BaseStaffInfo;
use app\models\manage\StaffInfo;
use app\services\base\SettingEnvService;
use Yii;

class CompanyService extends \app\services\base\CompanyService
{

    /**
     * 泰国真实公司ID
     */
    const FLASH_EXPRESS_COMPANY_ID  = 1;
    const F_COMMERCE_COMPANY_ID     = 50001;
    const FULFILLMENT_COMPANY_ID    = 20001;
    const FLASH_PAY_ID              = 60001;
    const FLASH_MONEY_ID            = 30001;
    const FLASH_HOME_ID             = 1222;
    const BULK_BULK_ID              = 80014;
    const INCORPORATION_ID          = 858;


    public  $staffCompanyConfigMap = [
        self::FLASH_EXPRESS_COMPANY_ID => 'salary_special_flash_express',
        self::FULFILLMENT_COMPANY_ID   => 'special_ffm_to_flashexpress',
        self::FLASH_PAY_ID             => 'special_pay_to_flashexpress',
        self::F_COMMERCE_COMPANY_ID    => 'special_fcommerce_to_flashexpress',
        self::FLASH_MONEY_ID           => 'special_money_to_flashexpress',
        self::FLASH_HOME_ID            => 'special_flash_home_to_flashexpress',
        self::BULK_BULK_ID             => 'special_bulk_bulk_to_flashexpress',
        self::INCORPORATION_ID         => 'special_incorporation_to_flashexpress',
    ];


    public function getStaffContractCompany($staff_info_id, $node_department_id, $is_lnt_staff)
    {
        $specialStaff = $this->getSpecialCompanyStaff();
        if (!empty($specialStaff[$staff_info_id])) {
            return self::FLASH_EXPRESS_COMPANY_ID;
        }
        $staffInfo = StaffInfo::find()->select(['nationality'])->where(['staff_info_id' => $staff_info_id])->one();
        if ($staffInfo['nationality'] != BaseStaffInfo::COUNTRY_TH) {
            return self::FLASH_EXPRESS_COMPANY_ID;
        }

        $company_id = SysDepartment::find()->select('company_id')->where(['id' => $node_department_id])->column(Yii::$app->get('r_backyard'));
        $company_id = current($company_id);
        if (empty($company_id) || !in_array($company_id, array_keys($this->getContractCompanyMap()))) {
            return self::FLASH_EXPRESS_COMPANY_ID;
        }
        return $company_id;
    }

}

