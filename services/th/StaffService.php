<?php

namespace app\services\th;

use app\libs\RedisListKeyEnums;
use app\models\backyard\DelinquencyCategory;
use app\models\backyard\HrBlackGreyList;
use app\models\backyard\SettingEnv;
use app\models\backyard\StaffInfoSync;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HrOperateLogs;
use Exception;
use Yii;

class StaffService extends \app\services\base\StaffService
{
    //https://flashexpress.feishu.cn/docx/D0BQdDwkdo84h1xD0KCcmeaHnPh
    //一个网点只有一个在职的员工的职位
    public $store_only_one_job_title = [16,1290];

    protected $i = 0;

    public function testSingle()
    {
        var_dump($this->i++);
    }

    public function test()
    {
        echo 'th StaffService' . "\r\n";
    }

    /**
     * 获取TH同步工号信息
     * @param $staffInfoId
     * @return bool|void
     */
    public function getSyncStaffInfo($staffInfoId)
    {
        $res = StaffInfoSync::find()->select(['id'])
            ->where(['staff_info_id' => $staffInfoId])
            ->asArray()
            ->one();
        return !empty($res) ? true : false;
    }

    /**
     * redis 网点变更网点主管更新为网点负责人
     * @param $params
     * @return bool
     */
    public function pushChangeStoreSupervisorManager($params): bool
    {
        try {
            $push_result = Yii::$app->redis->lpush(RedisListKeyEnums::CHANGE_STORE_SUPERVISOR_MANAGER,
                json_encode($params));
            Yii::$app->logger->write_log(['change_store_supervisor_manager' => $params, 'result' => $push_result],
                'info');
            return true;
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'pushChangeStoreSupervisorManager',
                'message'  => $e->getMessage(),
                'line'     => $e->getLine(),
                'file'     => $e->getFile(),
                'params'   => $params,
            ]);
            return false;
        }
    }

    /**
     * @description 同步角色变更
     * @param $before_position_category
     * @param $after_position_category
     * @param $staff_info_id
     * @param $fbid
     * @return void
     */
    public function syncApprovalJurisdictionChanges(
        $before_position_category,
        $after_position_category,
        $staff_info_id,
        $fbid
    ) {
        $positionCategoryNeedToSync = [68, 77];
        foreach ($positionCategoryNeedToSync as $roleId) {
            if (!empty($before_position_category) && in_array($roleId, $before_position_category) && !in_array($roleId,
                    $after_position_category)) {
                Yii::$app->jrpc->approvalJurisdictionChanges($staff_info_id, $fbid);
                break;
            }
        }
    }

    //停职原因：未回公款、连续旷工，停职恢复在职
    //向恢复在职员工本人的“个人号码”发送手机短信（泰语）
    //向恢复在职员工直线上级、对应HRBP发送BY/KIT-Push通知（根据用户登录语言）
    public function StaffSuspendedReinstatementSendMsg($params): bool
    {
        Yii::$app->logger->write_log(['StaffSuspendedReinstatementSendMsg' => $params], 'info');
        $result = Yii::$app->redis->lpush(RedisListKeyEnums::SUSPENDED_REINSTATEMENT, json_encode($params));
        Yii::$app->logger->write_log(['StaffSuspendedReinstatementSendMsg' => $params, 'result' => $result], 'info');
        return $result;
    }

    /**
     * 恢复在职 移除黑灰名单逻辑
     * @param $staff_info_id
     * @param $identity
     * @param string $fbid
     * @return bool
     */
    public function removeBlackGreyList($before, $fbid,$newStaff)
    {
        Yii::$app->logger->write_log('removeBlackGreyList staff_data:'.json_encode($before), 'info');
        // 作废 连续旷工三天以上 和对应次数 的黑灰名单 D0301=连续旷工， D0101=曾在公司工作次数
        $leaveNum = $this->getStaffLeaveNum($before,$newStaff);
        if (in_array($newStaff['hire_type'],BaseStaffInfo::$agentTypeTogether)){
            $leaveNum = 1;
        }
        $staff_info_id = $before["staff_info_id"] ?? '';
        $leave_source = $before['leave_source'];
        $leave_date = date('Ymd',strtotime($before['leave_date']));
        
        $uid = $staff_info_id . $leaveNum . $leave_source . date('Ymd',strtotime($leave_date));
        $uuid_21     = $this->getUuid($uid, DelinquencyCategory::INTER_CODE_21);
        $uuid_5     = $this->getUuid($uid, DelinquencyCategory::INTER_CODE_5);
        Yii::$app->logger->write_log('removeBlackGreyList staff_info_id:'.$staff_info_id.',uuid_21:'.$uuid_21.',uuid_5:'.$uuid_5, 'info');
        $hr_black = HrBlackGreyList::find()
            ->where(['uuid' => $uuid_21])
            ->andWhere(['status' => HrBlackGreyList::STATUS_ON])
            ->andWhere(['category' => DelinquencyCategory::INTER_CODE_21])
            ->asArray()
            ->one();
        if ($hr_black) {
            $data = [
                "uuid"            => $uuid_21,
                "remove_remark"   => "Return to work",
                "remove_staff_id" => $fbid,
            ];
            $this->removeWhrBlackGreyList($data);
        }else{
            return true;
        }
        $hr_black = HrBlackGreyList::find()
            ->where(['uuid' => $uuid_5])
            ->andWhere(['status' => HrBlackGreyList::STATUS_ON])
            ->andWhere(['category' => DelinquencyCategory::INTER_CODE_5])
            ->asArray()
            ->one();
        if ($hr_black) {
            $data = [
                "uuid"            => $uuid_5,
                "remove_remark"   => "Return to work",
                "remove_staff_id" => $fbid,
            ];
            $this->removeWhrBlackGreyList($data);
        }
        return true;
    }

    /**
     * 删除黑灰名单
     * @param $staff
     * @return bool
     */
    public function delDelinquencyList($staff, $fbid,$before)
    {
        Yii::$app->logger->write_log('delDelinquencyList 1  staff_data:'.json_encode($before), 'info');
        if (empty($staff['leave_date'])){
            return true;
        }
        $leave_reason = $before['leave_reason'];
        // 这种情况 如果先修改的离职日期 后 又修改离职原因 走到这后会找不到 要删的那条黑灰名单
        $cates             = DelinquencyCategory::find()
            ->where([
                    'source_cate' => DelinquencyCategory::SOURCE_QUIT,
                    'deleted'     => DelinquencyCategory::VALID,
                    'level'       => 2,
                    'exter_code'       => $leave_reason,
                ]
            )
            ->asArray()
            ->one();
        $staff_info_id = $before["staff_info_id"] ?? '';
        $leaveNum          = $this->getStaffLeaveNum($before,$before,true);
        if (in_array($before['hire_type'],BaseStaffInfo::$agentTypeTogether)){
            $leaveNum = 1;
        }
        $leave_source = $before['leave_source'];
        $leave_date = date('Ymd',strtotime($before['leave_date']));
        $uid = $staff_info_id . $leaveNum . $leave_source . date('Ymd',strtotime($leave_date));
        // 次数的
        $_c_uuid = $this->getUuid($uid,'D0101');
        Yii::$app->logger->write_log('delDelinquencyList 2 end  uuid:'.$_c_uuid, 'info');
        $data          = HrBlackGreyList::find()->where([
            'uuid' => $_c_uuid,
        ])->asArray()->one();
        if ($data){
            HrBlackGreyList::deleteAll(['uuid' => $_c_uuid]);
        }
        
        if (empty($cates)){
            return true;
        }
        // 原因的
        $_uuid = $this->getUuid($uid,$cates['inter_code']);
        Yii::$app->logger->write_log('delDelinquencyList 1 end  uuid:'.$_uuid, 'info');
        $data          = HrBlackGreyList::find()->where([
            'uuid' => $_uuid,
        ])->asArray()->one();
        if ($data){
            HrBlackGreyList::deleteAll(['uuid' => $_uuid]);
        }
        return true;
    }

    /**
     * 添加黑灰名单逻辑
     * @param $staff
     * @return bool
     */
    public function addDelinquencyList($staff, $fbid,$is_add,$newStaff)
    {
        //检查离职原因
        $cates             = DelinquencyCategory::find()
            ->where([
                    'source_cate' => DelinquencyCategory::SOURCE_QUIT,
                    'deleted'     => DelinquencyCategory::VALID,
                    'level'       => 2,
                ]
            )->andWhere(['<>', 'exter_code', ' '])
            ->indexBy('exter_code')
            ->asArray()
            ->all();
        $leaveReason       = $staff["leave_reason"];
        $leave_source       = $staff["leave_source"];
        $staffJobTitle     = $staff["job_title"];
        $settings          = SettingEnv::find()->where([
            'code' => 'black_and_graylist_frontline_jobids',
        ])->asArray()->one();
        $firstLineJobTitle = explode(',', $settings['set_val']);
        $p_category        = DelinquencyCategory::find()
            ->where([
                    'id' => $cates[$leaveReason]["ancestry"] ?? 0,
                ]
            )
            ->asArray()
            ->one();
        $leaveNum          = $this->getStaffLeaveNum($staff,$newStaff, !$is_add);
        if ($is_add){
            // 如果是添加 说明离职日志表里还没写入 这里需要+1 ，如果是更新则不需要+1了
            $leaveNum          = $leaveNum + 1;
        }
        if (in_array($newStaff['hire_type'],BaseStaffInfo::$agentTypeTogether)){
            $leaveNum          = 1;
        }
        if (
            isset($cates[$leaveReason]) && 
            $staff['is_sub_staff'] == BaseStaffInfo::IS_SUB_STAFF_NO && 
            in_array($staff['formal'],[BaseStaffInfo::FORMAL_YES,BaseStaffInfo::FORMAL_TRAINEE])
        ) {
            $formula = $cates[$leaveReason]['formula'];
            $params  = ['job_title' => $staffJobTitle, 'front_line' => $firstLineJobTitle];
            $result  = $this->getEvaluated($formula, $params);
            Yii::$app->logger->write_log('addDelinquencyList 1 staff_info_id:'.$staff['staff_info_id'].',job_title:'.$staffJobTitle.',result:'.$result.',category:'.($cates[$leaveReason]["inter_code"] ?? ""), 'info');
            if ($result > 0) {
                $leave_source_text =  Yii::$app->lang->get('font_leave_source_'.$leave_source,'','en');
                $uid = $staff["staff_info_id"] . $leaveNum . $leave_source . date('Ymd',strtotime($staff["leave_date"]));
                $data = [
                    "identity"           => $staff["identity"] ?? "",
                    "staff_info_id"      => $staff["staff_info_id"] ?? "",
                    "name"               => $staff["name"] ?? "",
                    "mobile"             => $staff["mobile"] ?? "",
                    "remark"             => "Due to reasons for leaving the job".','.$leave_source_text,
                    "p_category"         => $p_category["inter_code"] ?? "",
                    "category"           => $cates[$leaveReason]["inter_code"] ?? "",
                    "source_code"        => DelinquencyCategory::SOURCE_QUIT,
                    "behavior_type"      => $result,
                    "submitter_staff_id" => $fbid,
                    "leave_num"          => $leaveNum,
                    "uuid"               => $this->getUuid($uid,
                        $cates[$leaveReason]['inter_code']),
                ];
                $this->addWhrBlackGreyList($data);
            }
        }else{
            Yii::$app->logger->write_log('addDelinquencyList 离职原因进入黑灰名单条件不满足 staff_info_id:'.$staff['staff_info_id'].',leave_reason:'.$leaveReason.',job_title:'.$staffJobTitle.',state:'.$staff['state'].',wait_leave_state:'.$staff['wait_leave_state'], 'info');
        }
        // 次数 需要去掉子账号、外协、个人代理、加盟商
        if (
            $staff['is_sub_staff'] != BaseStaffInfo::IS_SUB_STAFF_NO ||
            !in_array($staff['formal'],[BaseStaffInfo::FORMAL_YES,BaseStaffInfo::FORMAL_TRAINEE]) ||
            in_array($staff['hire_type'],BaseStaffInfo::$agentTypeTogether)
        ){
            Yii::$app->logger->write_log('addDelinquencyList end 离职次数进入黑灰名单条件不满足 staff_info_id:'.$staff['staff_info_id'].',leave_reason:'.$leaveReason.',job_title:'.$staffJobTitle.',state:'.$staff['state'].',wait_leave_state:'.$staff['wait_leave_state'], 'info');
            return true;
        }
        //检查离职次数
        $cate    = DelinquencyCategory::find()
            ->where([
                    'inter_code' => DelinquencyCategory::INTER_CODE_5,
                ]
            )
            ->asArray()
            ->one();
        $formula = $cate['formula'];

        $settings = SettingEnv::find()->where([
            'code' => [
                'first_line_black_times',
                'first_line_gray_times',
                'not_first_line_black_times',
                'not_first_line_gray_times',
            ],
        ])->asArray()->all();
        $settings = array_column($settings, 'set_val', 'code');
        $params   = array_merge([
            'job_title'  => $staffJobTitle,
            'front_line' => $firstLineJobTitle,
            'leave_num'  => $leaveNum,
        ], $settings);
        // 满足条件 黑名单优先级比灰名单高
        // !in_array($job_title,$front_line) ? (非一线):(一线)
        // 非一线 $leave_num >= $not_first_line_black_times ? 1 : ($leave_num >= $not_first_line_gray_times ? 2 : 0)
        // 一线 $leave_num == $first_line_black_times ? 1 : ($leave_num >= $first_line_gray_times ? 2 : 0)
        $result = $this->getEvaluated($formula, $params);
        Yii::$app->logger->write_log('addDelinquencyList 2 staff_info_id:'.$staff['staff_info_id'].',leave_num:'.$leaveNum.',job_title:'.$staffJobTitle.',result:'.$result, 'info');
        if ($result > 0) {
            $uid = $staff["staff_info_id"] . $leaveNum . $leave_source . date('Ymd',strtotime($staff["leave_date"]));
            $remark = 'Due to the number of resignations (' . $leaveNum . ' times)';
            $data   = [
                "identity"           => $staff["identity"] ?? "",
                "staff_info_id"      => $staff["staff_info_id"] ?? "",
                "name"               => $staff["name"] ?? "",
                "mobile"             => $staff["mobile"] ?? "",
                "remark"             => $remark,
                "p_category"         => DelinquencyCategory::INTER_CODE_5_P,
                "category"           => DelinquencyCategory::INTER_CODE_5,
                "source_code"        => DelinquencyCategory::SOURCE_QUIT,
                "behavior_type"      => $result,
                "submitter_staff_id" => $fbid,
                "leave_num"          => $leaveNum,
                "uuid"               => $this->getUuid($uid,
                    DelinquencyCategory::INTER_CODE_5),
            ];
            $this->addWhrBlackGreyList($data);
        }
        Yii::$app->logger->write_log('addDelinquencyList end staff_info_id:'.$staff['staff_info_id'].',leave_reason:'.$leaveReason.',job_title:'.$staffJobTitle.',state:'.$staff['state'].',wait_leave_state:'.$staff['wait_leave_state'], 'info');
        return true;
    }

    /**
     * 获取员工离职次数，按证件号
     * @param $staff
     * @return int
     */
    public function getStaffLeaveNum($staff,$newStaff = [],$is_del = false): int
    {
        if (in_array($newStaff['hire_type'],BaseStaffInfo::$agentTypeTogether)){
            return 0;
        }
        $identity  = $staff["identity"] ?? "";
        $leave_num = 0;
        if (empty($identity)) {
            return $leave_num;
        }
        $data      = BaseStaffInfo::find()->select([
            'count(leave_manage_log.`id`)  as num',
            'hr_staff_info.staff_info_id',
        ])
            ->leftJoin('leave_manage_log',
                '`leave_manage_log`.`staff_info_id` = `hr_staff_info`.`staff_info_id` and leave_manage_log.by_approval_status = 0 and (leave_manage_log.operate_id in (10000,\'-1\') or leave_manage_log.leave_source in (\'-1\',6,7,9,10))')
            ->where(['hr_staff_info.identity' => $identity,'hr_staff_info.is_sub_staff'=>0])
//                ->andWhere(['not in','hr_staff_info.hire_type',BaseStaffInfo::$agentTypeTogether])
            ->andWhere(['in','hr_staff_info.formal',[BaseStaffInfo::FORMAL_YES,BaseStaffInfo::FORMAL_TRAINEE]])
            ->andWhere(['not in','hr_staff_info.hire_type',BaseStaffInfo::$agentTypeTogether])
            ->asArray()
            ->groupBy('hr_staff_info.staff_info_id')
            ->all();
        $leave_num = $data ? array_sum(array_column($data, "num")) : 0;
        $staff_info_ids = $data ? array_column($data, "staff_info_id") : [];
        $remove_leave_num_sum = 0;
        foreach ($staff_info_ids as $staff_info_id){
            $staff_data      = BaseStaffInfo::find()->select([
                'leave_manage_log.`leave_reason`',
                'hr_staff_info.`state`',
                'leave_manage_log.created_at',
            ])->leftJoin('leave_manage_log',
                '`leave_manage_log`.`staff_info_id` = `hr_staff_info`.`staff_info_id` and leave_manage_log.by_approval_status = 0 and (leave_manage_log.operate_id in (10000,\'-1\') or leave_manage_log.leave_source in (\'-1\',6,7,9,10))')
                ->where(['hr_staff_info.staff_info_id' => $staff_info_id,'hr_staff_info.is_sub_staff'=>0])
                ->andWhere(['in','hr_staff_info.formal',[BaseStaffInfo::FORMAL_YES,BaseStaffInfo::FORMAL_TRAINEE]])
                ->andWhere(['not in','hr_staff_info.hire_type',BaseStaffInfo::$agentTypeTogether])
                ->orderBy(['leave_manage_log.created_at' => SORT_DESC])->asArray()->all();
            $remove_leave_num = 0;
            Yii::$app->logger->write_log('getStaffLeaveNum staff_info_id:'.$staff_info_id.';;;is_del:'.$is_del.';;;staff_data:'.json_encode($staff_data, JSON_UNESCAPED_UNICODE).';;;newStaff:'.json_encode($newStaff, JSON_UNESCAPED_UNICODE), 'info');
            foreach ($staff_data as $k => $v){
                if (empty($v['created_at'])){
                    continue;
                }
                // 该员工总共有几次三天旷工离职的 需要减几次
                if ($v['leave_reason'] == BaseStaffInfo::LEAVE_REASON_21){
                    $remove_leave_num++;
                }
                // 当前状态不是离职状态 说明需要看最后一次离职时 离职原因是否是三天旷工 如果是 需要少减一次
                if ($is_del){
                    if ($k == 0 && $newStaff['state'] == BaseStaffInfo::STATE_RESIGN && $v['leave_reason'] == BaseStaffInfo::LEAVE_REASON_21){
                        $remove_leave_num--;
                    }
                }else{
                    if ($k == 0 && $newStaff['state'] != BaseStaffInfo::STATE_RESIGN && $v['leave_reason'] == BaseStaffInfo::LEAVE_REASON_21){
                        $remove_leave_num--;
                    }elseif ($newStaff['staff_info_id'] != $staff_info_id && $k == 0 && $v['state'] == BaseStaffInfo::STATE_RESIGN && $v['leave_reason'] == BaseStaffInfo::LEAVE_REASON_21){
                        $remove_leave_num--;
                    }
                }
            }
            $remove_leave_num_sum += $remove_leave_num;
        }
        return $leave_num < $remove_leave_num_sum ? 0 : ($leave_num - $remove_leave_num_sum);
    }

    /**
     * 调用RPC接口添加黑灰名单
     * @param $data
     * @return void
     */
    public function addWhrBlackGreyList($data, $lang = 'zh-CN')
    {
        $body = [
            'jsonrpc' => '2.0',
            'method'  => 'add_black_grey_list',
            'params'  => [
                ['locale' => $lang],
                $data,
            ],
            'id'      => time(),
        ];
        $res  = Yii::$app->http->json_rpc_post(Yii::$app->params['winhr_rpc_endpoint'], $body);
        Yii::$app->logger->write_log('addWhrBlackGreyList require:' . json_encode($body) . 'response: ' . $res,
            'info');//响应
        $_res_arr = json_decode($res, true);
        if (!isset($_res_arr['result'])) {
            Yii::$app->logger->write_log('addWhrBlackGreyList require:' . json_encode($body) . ' response: ' . json_encode($res));//异常
            return;
        }
        $res = $_res_arr['result'];
        if (isset($res['code']) && $res['code'] == 1) {
            Yii::$app->logger->write_log('addWhrBlackGreyList require:' . json_encode($body) . ' response: ' . json_encode($res),
                'info');//异常
        } elseif(strrpos($res['msg'], "System already exists") === false) {
            Yii::$app->logger->write_log('addWhrBlackGreyList require:' . json_encode($body) . ' response: ' . json_encode($res));//异常
        }
    }

    /**
     * 调用RPC接口移除黑灰名单
     * @param $data
     * @return void
     */
    public function removeWhrBlackGreyList($data, $lang = 'zh-CN')
    {
        $body = [
            'jsonrpc' => '2.0',
            'method'  => 'remove_black_grey_list',
            'params'  => [
                ['locale' => $lang],
                $data,
            ],
            'id'      => time(),
        ];

        $res = Yii::$app->http->json_rpc_post(Yii::$app->params['winhr_rpc_endpoint'], $body);
        Yii::$app->logger->write_log('removeWhrBlackGreyList require:' . json_encode($body) . 'response: ' . $res,
            'info');//响应
        $_res_arr = json_decode($res, true);
        if (!isset($_res_arr['result'])) {
            Yii::$app->logger->write_log('removeWhrBlackGreyList require:' . json_encode($body) . ' response: ' . json_encode($res));//异常
            return;
        }
        $res = $_res_arr['result'];
        if (isset($res['code']) && $res['code'] == 1) {
            Yii::$app->logger->write_log('removeWhrBlackGreyList require:' . json_encode($body) . ' response: ' . json_encode($res),
                'info');//异常
        } else {
            Yii::$app->logger->write_log('removeWhrBlackGreyList require:' . json_encode($body) . ' response: ' . json_encode($res));//异常
        }
    }

    /**
     * 根据规则生成uuid
     * @param string $id
     * @param string $category
     * @return string
     */
    public static function getUuid($id, $category)
    {
        if (empty($id) || empty($category)) {
            return '';
        }
        return $category . str_pad($id, 20, 0, STR_PAD_LEFT);
    }
}