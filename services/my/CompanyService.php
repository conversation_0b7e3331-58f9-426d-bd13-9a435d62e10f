<?php

namespace app\services\my;

use app\models\backyard\StaffPayrollCompanyInfo;
use app\models\backyard\SysDepartment;
use app\services\base\SettingEnvService;
use Yii;

class CompanyService extends \app\services\base\CompanyService
{
    /**
     * 马来真实公司ID
     */
    const FLASH_EXPRESS_COMPANY_ID  = 315;
    const F_COMMERCE_COMPANY_ID     = 15006;
    const FULFILLMENT_COMPANY_ID    = 20001;
    const FLASH_PAY_ID              = 60001;
    const FLASH_MONEY_ID            = 15050;
    const LNT_ID                    = -1;


    public  $staffCompanyConfigMap = [
        self::FLASH_EXPRESS_COMPANY_ID => 'salary_special_flash_express',
        self::FULFILLMENT_COMPANY_ID   => 'salary_special_fulfillment',
        self::FLASH_PAY_ID             => 'salary_special_flash_pay',
        self::F_COMMERCE_COMPANY_ID    => 'salary_special_f_commerce',
        self::FLASH_MONEY_ID           => 'salary_special_flash_money',
        self::LNT_ID                   => 'salary_special_lnt',
    ];


    public function getStaffContractCompany($staff_info_id, $node_department_id,$is_lnt_staff)
    {
        $specialStaff = $this->getSpecialCompanyStaff();
        if (!empty($specialStaff[$staff_info_id])) {
            return $specialStaff[$staff_info_id];
        }

        if ($is_lnt_staff) {
            return self::LNT_ID;
        }

        $company_id = SysDepartment::find()->select('company_id')->where(['id' => $node_department_id])->column(Yii::$app->get('r_backyard'));
        $company_id  = current($company_id);
        if (empty($company_id) || !in_array($company_id, array_keys($this->getContractCompanyMap()))) {
            return self::FLASH_EXPRESS_COMPANY_ID;
        }
        return $company_id;
    }

}

