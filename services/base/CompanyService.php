<?php

namespace app\services\base;

use app\models\backyard\StaffPayrollCompanyInfo;
use Yii;

class CompanyService extends BaseService
{

    const MS_COMPANY_ENUM_FLASH = 0;
    const MS_COMPANY_ENUM_LNT = 1;

    public  $staffCompanyConfigMap = [];

    public function getContractCompanyMap(): array
    {
        $companyConfigInfo = StaffPayrollCompanyInfo::find()->select([
            'company_id',
            'company_short_name',
        ])->asArray()->all(Yii::$app->get('r_backyard'));
        return array_column($companyConfigInfo, 'company_short_name', 'company_id');
    }



    public function getSpecialCompanyStaff(): array
    {

        $specialStaffTypeMap = $this->staffCompanyConfigMap;
        $codes               = array_values($specialStaffTypeMap);
        $list                = SettingEnvService::getInstance()->getMultiSetVal($codes);
        $specialStaffCompany = [];
        foreach ($specialStaffTypeMap as $companyId => $code) {
            if (!empty($list[$code])) {
                foreach (explode(',', $list[$code]) as $staff_id) {
                    $specialStaffCompany[$staff_id] = $companyId;
                }
            }
        }
        return $specialStaffCompany;
    }
    public function getStaffContractCompany($staff_info_id, $node_department_id,$is_lnt_staff)
    {
       return 0;
    }
}

