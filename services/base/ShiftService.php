<?php

namespace app\services\base;

use app\libs\ValidationException;
use app\models\backyard\HrShift;
use app\models\backyard\HrStaffShiftHistory;
use app\models\manage\BaseStaffInfo;
use app\models\manage\StaffInfo;
use Yii;
use yii\base\InvalidConfigException;

class ShiftService extends BaseService
{
    /**
     * 获取当日网点的班次
     * @param $params
     * @return array
     * @throws ValidationException
     * @throws InvalidConfigException
     */
    // backyard-db : select distinct(hr_staff_shift.shift_id ) as shift_id from hr_staff_info left join hr_staff_shift on `hr_staff_shift`.`staff_info_id` = `hr_staff_info`.`staff_info_id` where sys_store_id = 'TH0000000' and state = 1 and is_sub_staff = 0;
    // backyard-db : select distinct(shift_id) as shift_id from hr_staff_shift_history where shift_day= '2022-10-10';
    public function getHubShift($params): array
    {
        $sys_store_id = $params['sys_store_id'];
        $date_at = $params['date_at'];

        if(empty($sys_store_id)){
            throw  new ValidationException('sys_store_id empty!!');
        }
        if (!preg_match("/\d{4}\-\d{2}\-\d{2}/", $date_at)) {
            throw  new ValidationException('data_at error => '.$date_at);
        }

        $result = [];
        $current_day = gmdate('Y-m-d',time()+TIME_ADD_HOUR*3600);
        //var_dump($current_day,$date_at);die;
        //未来的直接返回
        if($date_at > $current_day){
            return $result;
        }
        //当天的查班次表
        if($date_at == $current_day){
            $staffShiftInfo  = staffInfo::find()
                ->select(['distinct(hr_staff_shift.shift_id ) as shift_id'])
                ->leftJoin('hr_staff_shift', '`hr_staff_shift`.`staff_info_id` = `hr_staff_info`.`staff_info_id`')
                ->where(['sys_store_id'=>$sys_store_id,'state'=> BaseStaffInfo::STATE_ON_JOB,'is_sub_staff'=>0])->asArray()
                ->all(Yii::$app->get('r_backyard'));
        }else{
            //过去的 查历史表
            $staffShiftInfo = HrStaffShiftHistory::find()
                ->select(['distinct(shift_id) as shift_id'])
                ->where(['shift_day'=>$date_at])->asArray()
                ->all(Yii::$app->get('r_backyard'));
        }
        if(empty($staffShiftInfo)){
            return $result;
        }
        $shift_ids = array_column($staffShiftInfo,'shift_id');
        return HrShift::find()->select(['id','start','end'])->where(['IN', 'id', $shift_ids])->andWhere(['shift_group' => HrShift::SHIFT_GROUP_FULL_DAY_SHIFT])->asArray()->all(Yii::$app->get('r_backyard'));
    }


    //获取所有班次
    public function getAllShifts($lang = '', $shift_group = [HrShift::SHIFT_GROUP_FULL_DAY_SHIFT]) {
        $result = [];
        //先查询固定班次
        $shift_list =HrShift::find()->select(['id','start','end','type'])->where(['shift_attendance_type' => HrShift::SHIFT_ATTENDANCE_TYPE_FIXED])->andWhere(['shift_group' => $shift_group])->orderBy('start')->all(Yii::$app->get('r_backyard'));
        foreach ($shift_list as $obj) {
            $result[$obj->type][] = [
                'start' => $obj->start,
                'end' => $obj->end,
                'id' => $obj->id,
                'type' => $obj->type,
                'markup' => '(' . Yii::$app->lang->get('shift_' . $obj->type, '', $lang) . ') ' . $obj->start . '-' . $obj->end,
            ];
        }
        ksort($result);
        return $result;
    }

}

