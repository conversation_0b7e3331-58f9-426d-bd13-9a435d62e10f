<?php

namespace app\services\base;


use app\libs\RocketMQ;
use app\models\backyard\LeaveManageLog;
use app\models\backyard\LeaveManager;

use app\models\backyard\LeaveQuitclaimInfo;
use app\models\backyard\StaffResign;
use app\models\fle\StaffInfo;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HoldStaffManage;
use Exception;
use Yii;

class LeaveManageService extends BaseService
{
    
    // 如果离职状态没变的情况下 变更离职原因和离职日期 需更新最新的一条离职log
    public function updateLeaveManageLog($params)
    {
        $staff_info_id = $params['staff_info_id'];         //工号
        $leave_reason  = $params['leave_reason'];          //离职原因
        $leave_date    = $params['leave_date'];            //离职日期
        $leave_type    = $params['leave_type'];            //离职类型
        $leave_source  = $params['leave_source'];          //离职来源
        $operate_id    = $params['operate_id'] ?? 10000;   //操作人id
        if (empty($staff_info_id) || empty($leave_reason) || empty($leave_date)) {
            return false;
        }
        $leave_manage_log = LeaveManageLog::find()->where(['staff_info_id' => $staff_info_id])->orderBy('id desc')->limit(1)->one();
        $leave_manage_log = !empty($leave_manage_log) ? $leave_manage_log->toArray() : [];
        if (empty($leave_manage_log['id'])) {
            return false;
        }
        $operate = StaffInfo::find()->select(['id', 'name'])->where(['id' => $operate_id])->asArray()->one();
        if (!empty($operate)) {
            $operate_name = $operate['name'];
        } else {
            $operate_name = 'system auto';
        }
        $r = LeaveManageLog::updateAll([
            'leave_date'   => date('Y-m-d', strtotime($leave_date)),
            'leave_reason' => $leave_reason,
            'leave_type'   => $leave_type,
            'leave_source' => $leave_source,
            'operate_id'   => $operate_id,
            'operate_name' => $operate_name,
        ], ['id' => $leave_manage_log['id']]);
        Yii::$app->logger->write_log(['message'       => 'updateLeaveManageLog',
                                      'staff_info_id' => $staff_info_id,
                                      'entry_date'    => $leave_date,
                                      'leave_reason'  => $leave_reason,
                                      'leave_type'  => $leave_type,
                                      'leave_source'  => $leave_source,
                                      'result'        => $r,
        ], 'info');
        return true;
    }
    //如果有其他在职状态变更成离职 leave_manager 有数据更新成未处理状态 无数据新增
    //leave_manager log 表新增
    public function updateLeaveManage($params)
    {
        try {
            $staff_info_id              = $params['staff_info_id'];//工号
            $leave_date                 = $params['leave_date'];   //离职日期
            $leave_source               = $params['leave_source']; //离职来源
            $leave_type                 = $params['leave_type'];   //离职类型
            $leave_reason               = $params['leave_reason']; //离职原因
            $operate_id                 = $params['operate_id'];   //操作人id
            $state                      = $params['state'] ?? 0;//在职状态
            $before_wait_leave_state    = $params['before_wait_leave_state'] ?? 0;
            $before_state               = $params['before_state'] ?? 0;
            $flash_hr_apply_resign_time = $params['flash_hr_apply_resign_time'] ?? null;   //FlashHR同步过来的员工申请离职的时间
            $operate = StaffInfo::find()->select(['id', 'name'])->where(['id' => $operate_id])->asArray()->one();
            if (!empty($operate)) {
                $operate_name = $operate['name'];
            } else {
                $operate_name = 'system auto';
            }
            $leave_manage_detail = LeaveManager::find()->where(['staff_info_id' => $staff_info_id])->one();
            if ($leave_manage_detail) {
                Yii::$app->logger->write_log([
                    'function' => 'LeaveManager',
                    'result'   => ['money_remand_state'=> $leave_manage_detail->money_remand_state],
                    'params'   => $params,
                ], 'info');

                $leave_manage_detail->leave_date                 = $leave_date;
                if(($before_state == BaseStaffInfo::STATE_ON_JOB && $before_wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_YES)
                    && $state == BaseStaffInfo::STATE_RESIGN) {
                    //查找是否有未还钱款，调用hcm接口查找
                    $staff_outstanding_money = Yii::$app->jrpc->getStaffOutstandingMoney(['staff_info_id' => $staff_info_id]);
                    $all_price               = $staff_outstanding_money['all_price'] ?? 0;
                    if ($all_price != 0) {
                        $leave_manage_detail->money_remand_state = LeaveManager::MONEY_STATE_UNPROCESSED;
                    }
                } else {
                    $leave_manage_detail->money_remand_state = LeaveManager::MONEY_STATE_UNPROCESSED;
                }

                $leave_manage_detail->is_new_assets_remand_state = LeaveManager::IS_NEW_ASSETS_REMAND_STATE_YES;

                if (!empty($flash_hr_apply_resign_time)) {
                    $leave_manage_detail->apply_resign_time = $flash_hr_apply_resign_time;
                }

                $result = $leave_manage_detail->save();
                Yii::$app->logger->write_log([
                    'function' => 'updateLeaveManage',
                    'message'  => '更新leave_manager表',
                    'result'   => $result,
                    'params'   => $params,
                    'money_remand_state'   => $leave_manage_detail->money_remand_state,
                ], 'info');
            } else {
                $leave_manage_model = new LeaveManager();

                $leave_manage_model->staff_info_id       = $staff_info_id;
                $leave_manage_model->assets_remand_state = LeaveManager::IS_NEW_ASSETS_REMAND_STATE_YES;
                $leave_manage_model->money_remand_state  = LeaveManager::MONEY_STATE_UNPROCESSED;
                $leave_manage_model->leave_date          = $leave_date;
                if (!empty($flash_hr_apply_resign_time)) {
                    $leave_manage_model->apply_resign_time = $flash_hr_apply_resign_time;
                }
                $result = $leave_manage_model->save();
                Yii::$app->logger->write_log([
                    'function' => 'updateLeaveManage',
                    'message'  => '创建leave_manager表记录',
                    'result'   => $result,
                    'params'   => $params,
                ], 'info');
            }
            //同步hcm 计算员工short notice
            if (isCountry('MY')) {
                $mq = new RocketMQ('cal-short-notice');
                $mq->sendToMsg(['staff_info_id' => $staff_info_id],2);
            }

            //离职才进log
            if ($before_state != BaseStaffInfo::STATE_RESIGN && $state == BaseStaffInfo::STATE_RESIGN ) {
                $serial_no = date('YmdHis') . rand(10000, 99999);
                if ($leave_source == 5) {
                    $staffResign = StaffResign::find()->where([
                        'submitter_id' => $staff_info_id,
                        'leave_date'   => $leave_date,
                        'source'       => 0,
                    ])->andWhere(['IN', 'status', [2, 5]])->orderBy('resign_id desc')->one();
                    if($staffResign){
                        $serial_no = $staffResign->serial_no;
                    }
                }

                $leave_manage_log_model = new LeaveManageLog();
                $leave_manage_log_model->staff_info_id = $staff_info_id;
                $leave_manage_log_model->serial_no     = $serial_no;
                $leave_manage_log_model->leave_date    = $leave_date;
                $leave_manage_log_model->leave_source  = $leave_source;
                $leave_manage_log_model->leave_type    = $leave_type;
                $leave_manage_log_model->leave_reason  = $leave_reason;
                $leave_manage_log_model->operate_id    = $operate_id;
                $leave_manage_log_model->operate_name  = $operate_name;
                $log_result                            = $leave_manage_log_model->save();

                /**
                 * PH 每操作一次离职，新增一条 quitclaim 信息
                 */
                if (YII_COUNTRY == 'PH' && $log_result && $state == BaseStaffInfo::STATE_RESIGN) {
                    LeaveQuitclaimInfo::updateAll(['is_new' => LeaveQuitclaimInfo::IS_NEW_NO],
                        ['staff_info_id' => $staff_info_id]);

                    $leaveQuitclaimInfoModel                       = new LeaveQuitclaimInfo();
                    $leaveQuitclaimInfoModel->leave_manager_log_id = $leave_manage_log_model->id;
                    $leaveQuitclaimInfoModel->staff_info_id        = $staff_info_id;
                    $leaveQuitclaimInfoModel->is_new               = LeaveQuitclaimInfo::IS_NEW_YES;
                    $leaveQuitclaimInfoModel->version              = LeaveQuitclaimInfo::VERSION_2;//第二版
                    $leaveQuitclaimInfoModel->save();
                }

                Yii::$app->logger->write_log([
                    'function' => 'updateLeaveManage',
                    'message'  => '插入leave_manage_log表',
                    'result'   => $log_result,
                    'params'   => $params,
                ], 'info');
            }
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'updateLeaveManage',
                'message'  => $e->getMessage() . $e->getTraceAsString(),
                'line'     => $e->getLine(),
                'params'   => $params,
            ]);
        }
    }
}
