<?php

namespace app\services\base;

use App\Library\Enums\GlobalEnums;
use app\libs\Enums\Enums;
use app\libs\Enums\SuspendReasonEnums;
use app\models\backyard\Banklist;
use app\models\backyard\HrEmailSuffixEnums;
use app\models\backyard\HrJobDepartmentRelation;
use app\models\backyard\HrShift;
use app\models\backyard\SettingEnv;
use app\models\backyard\SysDepartment;
use app\models\backyard\SysStoreType;
use app\models\backyard\VehicleInfo;
use app\models\manage\SysCity;
use app\models\manage\SysDistrict;
use app\models\manage\SysProvince;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HrJobTitleRole;
use app\models\manage\SysManagePiece;
use app\models\manage\SysManageRegion;
use app\models\manage\SysStoreTemp;
use app\modules\v1\business\OutsourcingOrder;
use app\modules\v1\business\SysGroupDeptV2;
use Yii;
use yii\db\Query;

class SysConfigService extends BaseService
{


    public $all_field = [
        'sys_store',
        'sys_department',
        'job_title',
        'outsourcing_search_job_title',
        'position',
        'header_office_position',
        'store_position',
        'relation_department_job_title',
        'relation_job_title_position',
        'area_name_list',
        'shifts',
        'bank_type',
        'independent_bank_type',
        'outsourcing_bank_type',
        'email_suffix',
        'email_suffix_common', //winHr回写使用
        'equipment_cost_job_title',
        'roles',
        'pieces',
        'regions',
        'group_department_tree_v2',
        'job_title_grade_list',
        'sys_province_list',
        'store_categories',
        'nationality',
        'education',
        'relatives_relationship',
        'leave_type_reason',
        'leave_type_reason_agent',
        'call_name',
        'working_country',
        'hire_type',
        'race',
        'religion',
        'leave_type_reason_no_del',
        'leave_type_reason_no_del_agent',
        'os_outsourcing_hire_type_list',
        'ptkp_state',
        'jurisdiction_permission_roles',
        'sys_department_all',//winhr
        'relation_job_title_level_and_grade',
        'offical_partner_position',
        'job_title_level',
        'group_department_tree',
        'allDepeartments_tree',
        'vehicle_type_category',
        'hub_os_shifts',
    ];

    /**
     * ToDo
     * 使用语言包的需要配置在这里
     * @var string[]
     */
    public $disable_cache_method = [
        'leave_type_reason_no_del',
        'leave_type_reason_no_del_agent',
        'leave_type_reason',
        'leave_type_reason_agent',
        'shifts',
        'ptkp_state',
        'race',
        'religion',
        'call_name',
        'relatives_relationship',
        'education',
        'relation_department_job_title',
        'relation_job_title_position',
        'group_department_tree_v2',
        'nationality',
        'working_country',
        'hire_type',
        'os_outsourcing_hire_type_list',
        'suspend_type_reason',
        'roles',
    ];

    public function sysInfo($query_field = []): array
    {
        $query_field = $query_field ?: $this->all_field;

        $data = [];
        foreach ($query_field as $key => $value) {
            $state = method_exists($this, $value);
            if ($state) {
                $method = $value;
                if (!in_array($value, $this->disable_cache_method)) {
                    $method = $value . 'FromCache';
                }
                $data[$value] = $this->$method();
            }
        }
        return $data;
    }

    public function allDepeartments_tree()
    {
        return Yii::$app->sysconfig->allDepeartments_tree;
    }

    public function group_department_tree()
    {
        return Yii::$app->sysconfig->group_dept_tree_list;
    }

    public function job_title_level()
    {
        $job_title_level = [];
        foreach (Yii::$app->sysconfig->config_job_title_level as $item => $value) {
            $job_title_level[] = ['level_id' => $item, 'level_name' => $value];
        }
        return $job_title_level;
    }

    public function offical_partner_position()
    {
        return Yii::$app->sysconfig->getOfficalPartnetPosition();
    }

    public function relation_job_title_level_and_grade()
    {
        return Yii::$app->sysconfig->relationJobTitleLevelAndGrade();
    }

    public function sys_department_all()
    {
        return $this->kvWrap(Yii::$app->sysconfig->allDepeartments);
    }

    public function sys_store()
    {
        return SysStoreTemp::temp();
    }

    public function sys_department()
    {
        return $this->kvWrap(Yii::$app->sysconfig->depeartment);
    }

    public function job_title()
    {
        return $this->kvWrap(Yii::$app->sysconfig->jobTitle);
    }

    public function outsourcing_search_job_title()
    {
        $job_title_list                   = Yii::$app->sysconfig->jobTitle;
        $outsourcing_search_job_title     = [];
        $outsourcing_search_job_title_ids = OutSourcingOrderService::getInstance()->searchJobTitles();
        foreach ($outsourcing_search_job_title_ids as $key => $value) {
            if (isset($job_title_list[$value])) {
                $outsourcing_search_job_title[$value] = $job_title_list[$value];
            }
        }
        return $this->kvWrap($outsourcing_search_job_title);
    }

    public function position()
    {
        return Yii::$app->sysconfig->getPosition();

    }

    public function header_office_position()
    {
        return Yii::$app->sysconfig->getHeaderOfficePosition();
    }

    public function store_position()
    {
        return Yii::$app->sysconfig->getStorePosition();
    }

    /**
     * 部门职位关联关系
     * @return array|array[]
     */
    public function relation_department_job_title()
    {
        $list = HrJobDepartmentRelation::find()->all();
        $relations = [];
        foreach ($list as $key => $row) {
            $relations[$row->department_id][] = $row->job_id;
        }
        $job_title_list  = HrJobTitleService::getInstance()->getDataUseIdIndex(['id', 'job_name'], 'job_name',['status' => 1]);
        return array_map(function ($row) use ($job_title_list) {
            $titles = [];
            foreach ($row as $r) {
                $titles[] = [
                    'key'   => (string)$r,
                    'value' => $job_title_list[$r] ?? '',
                ];
            }
            return $titles;
        }, $relations);
    }

    /**
     * 部门职位角色关联关系
     * @return array
     */
    public function relation_job_title_position()
    {
        $relations = [];
        foreach (HrJobTitleRole::find()->all() as $key => $row) {
            $relations[$row->sys_depeartment_id . '_' . $row->job_title_id][] = $row->role_id;
        }
        return $relations;
    }

    public function area_name_list()
    {
        switch (YII_COUNTRY) {
            case 'MY':
                $belongAreas = Yii::$app->sysconfig->getAreasMy();
                break;
            case 'LA':
                $belongAreas = Yii::$app->sysconfig->getAreasLa();
                break;
            default:
                $belongAreas = Yii::$app->sysconfig->getAreas();
        }


        $areas = [];
        foreach ($belongAreas as $key => $value) {
            $areas[] = ['key' => $key, 'value' => $value];
        }
        return $areas;
    }

    public function shifts()
    {
        return ShiftService::getInstance()->getAllShifts();
    }

    //外协工作订单 获取班次信息
    public function hub_os_shifts()
    {
        if(YII_COUNTRY == 'TH') {
            $shift_group = [HrShift::SHIFT_GROUP_FULL_DAY_SHIFT, HrShift::SHIFT_GROUP_HALF_DAY_SHIFT,HrShift::SHIFT_GROUP_HUB_OUTSOURCE_SHIFT_4, HrShift::SHIFT_GROUP_HUB_OUTSOURCE_SHIFT_5];
        } elseif(YII_COUNTRY == 'MY') {
            $shift_group = [HrShift::SHIFT_GROUP_FULL_DAY_SHIFT, HrShift::SHIFT_GROUP_HUB_FULL_DAY_SHIFT];

        }elseif (YII_COUNTRY == 'PH'){
            $shift_group = [HrShift::SHIFT_GROUP_FULL_DAY_SHIFT, HrShift::SHIFT_GROUP_HALF_DAY_SHIFT];
        } else {
            $shift_group = [HrShift::SHIFT_GROUP_FULL_DAY_SHIFT];
        }
        return ShiftService::getInstance()->getAllShifts('',$shift_group);
    }

    public function bank_type()
    {
        return $this->getBackInfo('staff_bank_id_list');
    }

    /**
     * 个人代理 银行类型
     */
    public function independent_bank_type()
    {
        return $this->getBackInfo('staff_bank_id_list_independent');
    }

    public function getBackInfo($code)
    {
        //可选银行数据
        $bank_type = [];
        $bank_ids  = SettingEnv::find()->where(['code' => $code])->one();
        if (!empty($bank_ids) && $bank_ids->set_val) {
            $bank_id_arr = explode(',', $bank_ids->set_val);

            //根据配置的银行ID获取银行选项数据
            $bank_type = Banklist::find()->select([
                'bank_id',
                'bank_name',
                'max_length',
                'min_length',
            ])->where(['bank_id' => $bank_id_arr])->orderBy(['sort_num' => SORT_DESC,'bank_id' => SORT_DESC])->asArray()->all();
            if ($bank_type) {
                array_walk($bank_type, function (&$bank_item, $key) {
                    $bank_item['bank_id'] = intval($bank_item['bank_id']);
                });
            }
        }
        return $bank_type;
    }


    public function outsourcing_bank_type()
    {
        $outsourcing_bank_type = [];
        
        $staff_bank_id_list_outsource  = SettingEnv::find()->where(['code' => 'staff_bank_id_list_outsource'])->one();
        $config_outsourcing_bank_type = Banklist::find()->select(['bank_id','bank_name','max_length','min_length'])->where(['in','bank_id',explode(',',$staff_bank_id_list_outsource->set_val)])->orderBy(['sort_num' => SORT_DESC,'bank_id' => SORT_DESC])->asArray()->all();
        foreach ($config_outsourcing_bank_type as $item => $value) {
            $outsourcing_bank_type[] = ['bank_id' => $value['bank_id'], 'bank_name' => $value['bank_name'],'max_length'=>$value['max_length'],'min_length'=>$value['min_length']];
        }
        //越南和印尼的外协 跟正式员工的一致
//        if(in_array(YII_COUNTRY,['ID','VN'])){
//            $outsourcing_bank_type = $this->bank_type();
//        }
        return $outsourcing_bank_type;
    }

    /**
     * winhr  使用
     * @return array
     */
    public function email_suffix()
    {
        return $this->getEmailSuffixList(HrEmailSuffixEnums::DATA_SOURCE_WINHR);
    }

    /**
     * winhr回写 hris  hcm使用
     * @return array
     */
    public function email_suffix_common()
    {
        return $this->getEmailSuffixList();
    }

    public function equipment_cost_job_title()
    {
        //按照职位区分设备费用
        $equipment_cost_job_title = [
            '13'   => '6260', //Bike Courier
            '110'  => '5850', //Van Courier
            '1000' => '6260', //Tricycle Courier
            '1199' => '5850', //Car Courier

        ];
        if (isCountry('TH')) {
            $equipment_cost_job_title['1930'] = '5850'; //EV Courier
            $equipment_cost_job_title['1015'] = '5850'; //小黄车
        }
        return $equipment_cost_job_title;
    }

    public function roles()
    {
        $position = Yii::$app->sysconfig->getPosition();
        unset($position[23]);

        $roles = [];
        foreach ($position as $key => $value) {
            $roles[] = [
                'role_id'      => $key,
                'role_name_zh' => Yii::$app->lang->get('role_'.$key, '', 'zh-CN'),
                'role_name_en' => Yii::$app->lang->get('role_'.$key, '', 'en'),
                'role_name_th' => Yii::$app->lang->get('role_'.$key, '', 'th'),
                'role_name_vi' => Yii::$app->lang->get('role_'.$key, '', 'vi'),
            ];
        }
        return $roles;
    }


    public function pieces()
    {
        $pieces   = SysManagePiece::find()->select([
            "id",
            "manage_region_id",
            "name",
        ])->where(["deleted" => 0])->asArray()->all(Yii::$app->get('r_backyard'));
        $pieceses = [];
        foreach ($pieces as $piece) {
            $pieceses[] = [
                "id"               => $piece['id'],
                "manage_region_id" => $piece['manage_region_id'],
                "name"             => $piece['name'],
            ];
        }
        return $pieceses;
    }

    public function regions()
    {
        $regions   = SysManageRegion::find()->asArray()->all(Yii::$app->get('r_backyard'));
        $regionses = [];
        foreach ($regions as $region) {
            $regionses[] = [
                "id"   => $region['id'],
                "name" => $region['name'],
            ];
        }
        return $regionses;
    }

    public function group_department_tree_v2()
    {
        return SysGroupDeptV2::getGroupDeptList();
    }

    public function job_title_grade_list()
    {
        $grade_list = [];
        foreach (Yii::$app->sysconfig->config_job_title_grade_v2 as $key => $value) {
            $grade_list[] = [
                'key'   => $key,
                'value' => $value,
            ];
        }
        return $grade_list;
    }

    /**
     * 直接枚举，value 返回字符串
     * @return array
     */
    public function job_title_grade_list_string()
    {
        $grade_list = [];
        foreach (Yii::$app->sysconfig->config_job_title_grade_v2 as $key => $value) {
            $grade_list[] = [
                'value' => (string)$key,
                'label' => $value,
            ];
        }
        return $grade_list;
    }

    public function sys_province_list()
    {
        return Yii::$app->sysconfig->getProvince();//获取工作所在州;
    }

    public function store_categories()
    {
        $store_categories = [];
        $sys_store_type   = SysStoreType::find()->asArray()->all(Yii::$app->get('backyard'));
        foreach ($sys_store_type as $value) {
            $store_categories[] = [
                'key'   => (string)$value['id'],
                'value' => $value['name'],
            ];
        }
        return $store_categories;
    }

    public function nationality()
    {
        return Yii::$app->jrpc->getDictionaryByDictCode(['dict_code' => 'nationality_region', 'data_type' => 1], Yii::$app->language);
        //return Yii::$app->sysconfig->getNationality();
    }

    public function education()
    {
        return Yii::$app->sysconfig->getEducation();
    }

    public function relatives_relationship()
    {
        return Yii::$app->sysconfig->getRelativesRelationship();
    }

    public function leave_type_reason()
    {
        return Yii::$app->sysconfig->getLeaveTypeReason();
    }
    public function leave_type_reason_agent()
    {
        return Yii::$app->sysconfig->getLeaveTypeReason(true);
    }

    public function call_name()
    {
        return Yii::$app->sysconfig->getCallName();
    }

    public function working_country()
    {
        return Yii::$app->jrpc->getDictionaryByDictCode(['dict_code' => 'working_country', 'data_type' => 1], Yii::$app->language);
        //return Yii::$app->sysconfig->getWorkingCountry();
    }

    public function hire_type($dataType = Enums::DATA_TYPE_INT)
    {
        $hire_type_enum = SettingEnvService::getInstance()->getSetVal('hire_type_enum', ',');
        $config_list = empty($hire_type_enum) ? ['1', '2', '3', '4', '5'] : $hire_type_enum;

        foreach ($config_list as $item) {
            $list[] = [
                'key' => $dataType == Enums::DATA_TYPE_STRING ? strval($item) : intval($item),
                'value' => Yii::$app->lang->get('hire_type_' . $item),
            ];
        }
        return $list;
    }

    public function race()
    {
        return Yii::$app->sysconfig->getRace();
    }

    public function religion()
    {
        return Yii::$app->sysconfig->getReligion();
    }

    public function leave_type_reason_no_del()
    {
        return Yii::$app->sysconfig->getLeaveTypeReasonNoDel();
    }
    public function leave_type_reason_no_del_agent()
    {
        return Yii::$app->sysconfig->getLeaveTypeReasonNoDel(true);
    }

    //外协雇佣类型
    public function os_outsourcing_hire_type_list()
    {
        return [
            ['key' => BaseStaffInfo::HIRE_OS_TYPE_COMMON , 'value' => Yii::$app->lang->get('hire_type_11')],
            ['key' => BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING , 'value' => Yii::$app->lang->get('hire_type_12')],
            ['key' => BaseStaffInfo::HIRE_OS_TYPE_EMPLOY , 'value' => Yii::$app->lang->get('hire_type_15')],
        ];
    }

    //获取PTKP状态
    public function ptkp_state()
    {
        return Yii::$app->sysconfig->getPTKPState();
    }

    public function jurisdiction_permission_roles()
    {
        return RoleService::getInstance()->jurisdictionPermissions();
    }


    public function kvWrap($arr)
    {
        foreach ($arr as $key => $val) {
            $data[] = [
                'key'   => (string)$key,
                'value' => $val,
            ];
        }
        return $data ?? [];
    }


    /**
     * 部门职位角色关联关系接口
     * @return array
     */
    public function getDepartmentJobTitleRoleRelation(): array
    {
        return [
            'relation_department_job_title' => $this->relation_department_job_title(),
            'relation_job_title_position'   => $this->relation_job_title_position(),
            'sys_department_all'            => $this->sys_department_all(),
            'position'                      => $this->position(),
            'roles'                         => $this->roles(),
        ];
    }

    //获取停职原因
    public function suspend_type_reason()
    {
        $suspendList = SuspendReasonEnums::SUSPEND_TYPE_REASON_MAP;
        $result = [];
        foreach ($suspendList as $value => $translationKey) {
            $item = [
                'value'   => $value,
                'label'   => Yii::$app->lang->get($translationKey),
                'disable' => false,
            ];
            if (in_array($value, [6,7])) {
                $item['disable'] = true;
            }
            $result[] = $item;
        }
        return $result;
    }

    //车类型
    public function vehicle_type_category() {
        $vehicle_type_category  = VehicleInfo::$vehicleTypeCategoryMY;
        unset($vehicle_type_category[1]);
        unset($vehicle_type_category[2]);
        return $this->kvWrap($vehicle_type_category);
    }


    /**
     * 获取企业邮箱枚举
     * @param $data_source
     * @return array
     */
    public function getEmailSuffixList($data_source = HrEmailSuffixEnums::DATA_SOURCE_COMMON): array
    {
        $email_suffix = [];

        $config_email_suffix = HrEmailSuffixEnums::find()
            ->select(['id', 'name','is_default','data_source'])
            ->where(['is_deleted' => Enums::DELETED_NO])
            ->orderBy(['order' => SORT_ASC])
            ->asArray()
            ->all();

        foreach ($config_email_suffix as $value) {
            if (in_array($data_source,explode(',',$value['data_source']))) {
                $email_suffix[] = [
                    'key'        => (int) $value['id'],
                    'value'      => $value['name'],
                    'is_default' => (int) $value['is_default'],
                ];
            }
        }

        return $email_suffix;
    }


    public function getProvinceInfoByCode($code)
    {
        if (empty($code)) {
            return [];
        }
        return SysProvince::find()->select(['code', 'name'])->where([
            'deleted' => 0,
            'code'    => $code,
        ])->asArray()->one();
    }

    public function getCityInfoByCode($code)
    {
        if (empty($code)) {
            return [];
        }
        return SysCity::find()->select(['code', 'name', 'province_code'])->where([
            'deleted' => 0,
            'code'    => $code,
        ])->asArray()->one();
    }

    
    public function getDistrictInfoByCode($code)
    {
        if (empty($code)) {
            return [];
        }
        return SysDistrict::find()->select([
            'code',
            'name',
            'city_code',
            'province_code',
        ])->where(['deleted' => 0, 'code' => $code])->asArray()->one();
    }
}

