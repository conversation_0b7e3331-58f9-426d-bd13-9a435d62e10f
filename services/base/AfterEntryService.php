<?php

namespace app\services\base;

use app\libs\RedisListKeyEnums;

use app\models\manage\BaseStaffInfo;
use Yii;


class AfterEntryService extends BaseService
{
    protected $staffInfoModel;
    protected $form;

    /**
     * 初始化数据
     * @param $staffInfoModel
     * @param $form
     * @return $this
     */
    public function entryInitData($staffInfoModel, $form)
    {
        $this->staffInfoModel = $staffInfoModel;
        $this->form           = $form;
        return $this;
    }

    /**
     * 处理入口
     * @return array
     */
    public function fire(): array
    {
        $res['winhr_call_back']               = $this->winhrCallBack();
        $res['china_staff_job_change_notice'] = $this->chinaStaffEntryNotice();
        $res['create_staff_shift']            = $this->createStaffShift();
        $res['insurance_beneficiary']         = $this->lpushSendMessageInsuranceBeneficiary();
        $res['create_hr_probation']           = $this->createHrProbation();
        $res['create_extend_staff_info']      = $this->createStaffExtendInfo();
        return $res;
    }

    public function createStaffExtendInfo($staff_inf_id = 0)
    {
        $callbackParams = [
            'staff_info_id' => $staff_inf_id ? :$this->staffInfoModel->staff_info_id,
        ];
        if (!empty($this->form['project_num'])) {
            $callbackParams['project_num'] = $this->form['project_num'];
        }
        return Yii::$app->redis->lpush(RedisListKeyEnums::CREATE_HR_STAFF_EXTEND, json_encode($callbackParams));
    }

    public function createHrProbation()
    {
        if (
            !(
                $this->staffInfoModel->formal == BaseStaffInfo::FORMAL_YES &&
                $this->staffInfoModel->is_sub_staff == BaseStaffInfo::IS_SUB_STAFF_NO &&
                (
                    (
                        YII_COUNTRY == 'PH' &&
                        in_array($this->staffInfoModel->hire_type,[BaseStaffInfo::HIRE_TYPE_FORMAL,BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY])
                    ) or (
                        YII_COUNTRY == 'TH' &&
                        ($this->staffInfoModel->hire_type == BaseStaffInfo::HIRE_TYPE_FORMAL || (in_array($this->staffInfoModel->hire_type,[BaseStaffInfo::HIRE_TYPE_DAILY_SALARY,BaseStaffInfo::HIRE_TYPE_HOURLY_WAGE]) && $this->staffInfoModel->hire_times >= 365) || ($this->staffInfoModel->hire_type == BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY && $this->staffInfoModel->hire_times >= 12))
                    )
                )
            )
        ) {
            return false;
        }
        $callbackParams = [
            'staff_info_id' => $this->staffInfoModel->staff_info_id,
            'hire_date'     => $this->staffInfoModel->hire_date,
        ];
        return Yii::$app->redis->lpush(RedisListKeyEnums::CREATE_HR_PROBATION, json_encode($callbackParams));
    }
    /**
     * winrh入职 异步回调
     * @return mixed
     */
    protected function winhrCallBack()
    {
        Yii::$app->logger->write_log(['winhrCallBack'=>$this->form,'check_entry_id'=>empty($this->form['entry_id'])], 'info');

        if (empty($this->form['entry_id'])) {
            return true;
        }
        $callbackParams = [
            'new_staff_info_id' => $this->staffInfoModel->staff_info_id,
            'admin_id'          => $this->form['fbid'],
            'entry_id'          => $this->form['entry_id'],
        ];
        return Yii::$app->redis->lpush(RedisListKeyEnums::WINHR_CREATE_STAFF_LIST, json_encode($callbackParams));
    }

    /**
     * 中国员工入职 发邮件提醒
     * @return mixed
     */
    public function chinaStaffEntryNotice()
    {

        $staffInfo = $this->staffInfoModel->toArray();
        return StaffService::getInstance()->staffStateChange([],$staffInfo);

    }

    /**
     * 新入职创建工号
     * @return bool
     * 马来重写
     */
    public function createStaffShift()
    {
       return true;
    }

    /**
     * 发送编辑保险受益人信息消息 马来
     * @return bool
     */
    public function lpushSendMessageInsuranceBeneficiary(): bool
    {
        return true;
    }

    //发送填写保险受益人信息的消息
    public function sendMessageInsuranceBeneficiary($params): bool
    {
        return true;
    }
}

