<?php

namespace app\services\base;

use app\models\backyard\OldLeaveReasonScenarioRelation;
use app\models\backyard\LeaveScenario;
use app\models\backyard\StaffLeaveReason;

use app\models\manage\BaseStaffInfo;
use Yii;



class LeaveService extends BaseService
{
    /**
     * @deprecated
     * 获取离职原因
     * @param string $lang
     * @return array
     */
    public function getLeaveReason($lang = '')
    {
        $all_leave_type_reason = Yii::$app->jrpc->leaveReasonMap($lang);
        $r = [];
        $list = StaffLeaveReason::find()->select(['type','code','t_key','deleted'])->where(['deleted'=>0])->andWhere(['NOT IN', 'group_type', StaffLeaveReason::$agent_leave_reason])->asArray()->all();
        $leave_type_reasson = array_values(array_unique(array_column($list,'type')));
        foreach($leave_type_reasson as $key=>$val){
            $leave_type = [
                'key' => (int)$val,
                'value' => Yii::$app->lang->get('leave_type_' . $val, '', $lang),
            ];
            foreach ($list as $k=>$v){
                if ($val == $v['type']) {
                    $leave_type['child'][] = [
                        'key'   => (int)$v['code'],
                        'value' => $all_leave_type_reason[$v['code']] ?? $v['t_key'],
                    ];
                }
            }
            $r[] = $leave_type;
        }
        return $r;
    }

    /**
     * 获取离职场景
     * @param $is_all
     * @return array
     */
    public function getLeaveReasonScenario($is_all): array
    {
        $reasonListQuery = StaffLeaveReason::find()->select([
            'type',
            'code',
            't_key',
            'deleted',
            'group_type',
        ]);
        if (!$is_all) {
            $reasonListQuery->where(['deleted' => 0]);
        }
        $reasonList = $reasonListQuery->orderBy(['sort' => SORT_ASC])->asArray()->all();

        $scenarioListQuery = LeaveScenario::find()->select([
            'leave_reason_id',
            'code',
            't_key',
            'sort',
        ]);
        if (!$is_all) {
            $scenarioListQuery->where(['deleted' => 0]);
        }
        $scenarioListData = $scenarioListQuery->orderBy(['sort' => SORT_ASC])->asArray()->all();

        $scenarioList     = [];
        foreach ($scenarioListData as $item) {
            $scenarioList[$item['leave_reason_id']][] = [
                'key'   => (int)$item['code'],
                'value' => Yii::$app->lang->get($item['t_key']),
            ];
        }
        $leaveReasonList = [];
        foreach ($reasonList as $item) {
            $leaveReasonList[] = [
                'key'   => (int)$item['code'],
                'value' => Yii::$app->lang->get($item['t_key']),
                'child' => $scenarioList[$item['code']] ?? [],
            ];
        }
        return $leaveReasonList;
    }

    /**
     * 离职场景翻译
     * @return array
     */
    public function getLeaveScenarioTranslateMap(): array
    {
        $scenarioListQuery = LeaveScenario::find()->select([
            'code',
            't_key',
        ]);
        $scenarioListData = $scenarioListQuery->asArray()->all();

        return array_column($scenarioListData, 't_key', 'code');
    }


    /**
     * 获取离职场景和离职原因映射关系
     * @return array
     */
    public function getLeaveScenarioReasonRelationMap(): array
    {
        $query = LeaveScenario::find()->select([
            'leave_reason_id',
            'code',
        ]);
        $mapData = $query->asArray()->all();

        return array_column($mapData, 'leave_reason_id', 'code');
    }

    /**
     * 获取离职原因版本
     * @param null $leave_reason
     * @return array
     */
    public function getLeaveReasonVersionMap($leave_reason = null): array
    {
        $reasonListQuery = StaffLeaveReason::find()->select([
            'code',
            'version',
        ])->where(['group_type'=>StaffLeaveReason::GROUP_TYPE_DEFAULT]);
        if(!empty($leave_reason)){
            $reasonListQuery->where(['code'=>$leave_reason]);
        }
        $reasonList = $reasonListQuery->asArray()->all();

        return array_column($reasonList, 'version', 'code');

    }

    /**
     * 获取老离职原因和离职场景映射关系
     * @return array
     */
    public function getOldNewLeaveReasonMap(): array
    {
        $query = OldLeaveReasonScenarioRelation::find()->select([
            'old_leave_reason',
            'new_leave_reason',
        ]);
        $mapData = $query->asArray()->all();

        return array_column($mapData, 'new_leave_reason', 'old_leave_reason');
    }


    /**
     * @param $leave_reason
     * @return mixed
     */
    public function getNewLeaveReasonByLeaveReason($leave_reason)
    {
        $all_old_new_leave_reason = $this->getOldNewLeaveReasonMap();
        return $all_old_new_leave_reason[$leave_reason] ?? $leave_reason;
    }

    /**
     * 离职场景翻译
     * @param $lang
     * @return mixed
     */
    public function getLeaveScenarioMap($lang)
    {
        return Yii::$app->jrpc->leaveScenarioMap($lang);
    }

    /**
     * 离职原因翻译
     * @param $lang
     * @return mixed
     */
    public function getLeaveReasonMap($lang)
    {
        return Yii::$app->jrpc->leaveReasonMap($lang);
    }


    /**
     * 变更为待离职或离职时，处理离职场景
     * @param $changeAtt
     * @return void
     */
    public function fullLeaveScenario(&$changeAtt)
    {
        if (!isset($changeAtt['state'])) {
            return;
        }

        if ($changeAtt['state'] == BaseStaffInfo::STATE_RESIGN ||
            ($changeAtt['state'] == BaseStaffInfo::STATE_ON_JOB && isset($changeAtt['wait_leave_state']) && $changeAtt['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_YES)) {
            if (empty($changeAtt['leave_reason'])) {
                Yii::$app->logger->write_log(['message' => '变更为离职状态-离职原因为空', 'params' => $changeAtt]);
                return;
            }

            $leave_reason = $changeAtt['leave_reason'];

            $leave_reason_old_new_map = $this->getOldNewLeaveReasonMap();
            //看是否是历史的离职原因
            if (isset($leave_reason_old_new_map[$changeAtt['leave_reason']])) {
                Yii::$app->logger->write_log([
                    'message' => '变更为离职状态-离职原因为历史的，请核对',
                    'params'  => $changeAtt,
                ]);
                $leave_reason = $leave_reason_old_new_map[$leave_reason];
            }
            //新的离职原因
            $query = LeaveScenario::find()->select([
                'leave_reason_id',
                'code',
            ]);
            $query->where(['leave_reason_id' => $leave_reason]);
            $check_leave_scenario = $query->asArray()->all();
            if (!empty($check_leave_scenario) && empty($changeAtt['leave_scenario'])) {
                Yii::$app->logger->write_log([
                    'message' => '变更为离职状态-离职场景未赋值，请核对',
                    'params'  => $changeAtt,
                ]);
                if (count($check_leave_scenario) == 1) {
                    $changeAtt['leave_scenario'] = $check_leave_scenario[0]['code'];
                    Yii::$app->logger->write_log([
                        'message' => '变更为离职状态-离职场景未赋值，触发自动赋值',
                        'params'  => $changeAtt,
                    ]);
                }
            }
        }
    }





}

