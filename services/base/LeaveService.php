<?php

namespace app\services\base;

use app\models\backyard\StaffLeaveReason;

use Yii;



class LeaveService extends BaseService
{
    /**
     * 获取离职原因
     * @param string $lang
     * @return array
     */
    public function getLeaveReason($lang = '')
    {
        $r = [];
        $list = StaffLeaveReason::find()->select(['type','code','t_key','deleted'])->where(['deleted'=>0])->andWhere(['NOT IN', 'group_type', StaffLeaveReason::$agent_leave_reason])->asArray()->all();
        $leave_type_reasson = array_values(array_unique(array_column($list,'type')));
        foreach($leave_type_reasson as $key=>$val){
            $leave_type = [
                'key' => (int)$val,
                'value' => Yii::$app->lang->get('leave_type_' . $val, '', $lang)
            ];
            foreach ($list as $k=>$v){
                if($val==$v['type']){
                    $leave_type['child'][] = [
                        'key' => (int)$v['code'],
                        'value' => Yii::$app->lang->get( $v['t_key'], '', $lang)
                    ];
                }
            }
            $r[] = $leave_type;
        }
        return $r;
    }
}

