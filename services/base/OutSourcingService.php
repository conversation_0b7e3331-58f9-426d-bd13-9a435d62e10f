<?php
/**
 * Author: Bruce
 * Date  : 2022-12-07 11:27
 * Description:
 */

namespace app\services\base;

use app\models\backyard\HrBlacklist;
use app\models\backyard\HrOutSourcingBlacklist;
use app\models\backyard\HrOutsourcingOrder;
use app\models\backyard\HrOutsourcingOrderDetail;
use app\models\backyard\OutsourcingCompany;
use app\models\backyard\OutsourcingCompanyDeviceToken;
use app\models\backyard\SettingEnv;
use app\models\manage\BaseStaffInfo;
use Yii;

class OutSourcingService extends BaseService
{
    public $identity_match   = '/^[A-Za-z0-9]{1,30}$/';
    public $mobile_match     = '/^\d{10}$/';
    public $bank_no_match    = '/^\d{1,30}$/';
    public $validate_job_ids = [13, 110, 1000];

    const COMPANY_ITEM_PREMIER = 10000;//外协公司：Premier 枚举值

    const PREG_MATCH_OS_COMPANY_PREMIER = '/^\d{13}$/';

    public static $os_company_enums = [];



    public static $job_position_country = [
        'TH' => [
            13 => [1],// Bike Courier,快递
            98 => [1, 2],//Shop Officer,仓管
            110 => [1],//  Van Courier,快递
            111 => [2],// Warehouse Staff (Sorter),仓管
            271 => [2],// Hub Staff 仓管
            452 => [1],// Boat Courier,快递
            473 => [2],// Onsite Staff,仓管
            1000 => [1],  // Tricycle Courier 快递
        ],
        'PH' => [
            13	=> [1],//Bike Courier 快递
            110	=> [1],//Van Courier 快递
            111	=> [2],//Warehouse Staff (Sorter)仓管
            452	=> [1],//Boat Courier 快递
            807	=> [2],//Hub Operator 仓管
            812	=> [2],//Key Account Program Hub Officer 仓管
            1000 => [1],//Tricycle Courier 快递
            1194 => [114],// truck Driver 投递员
            1652 => [1],// mobile dc 快递
            300 => [2],// Warehouse Staff 网点仓管
            37 => [2],// Warehouse Staff 网点仓管
        ],
        'MY' => [
            13	=> [1],//Bike Courier 快递
            98	=> [1, 2],//Shop Officer 仓管
            110	=> [1],// Van Courier 快递
            111	=> [2],//Warehouse Staff (Sorter) 仓管
            271	 => [2],// Hub Staff 仓管
            452	 => [1],//Boat Courier 快递
            473	 => [2],//Onsite Staff 仓管
            1000 => [1],//Tricycle Courier 快递
            1199 => [1],//Car Courier 快递
            37   => [2], // DC Officer 仓管
        ],
        'LA' => [
            13	=> [1],//Bike Courier 快递
            110	=> [1],//Van Courier 快递
            111	=> [2],// Warehouse Staff (Sorter) 仓管
            271	=> [2],// Hub Staff 仓管
            812	=> [2],// Onsite Officer 仓管
        ],
    ];



    //公共 验证提交字段
    protected function commonValidateOutsourcingField($staffs, $job_id, $hire_os_type = 11)
    {
        //验证 公共验证 身份证/姓名/性别/手机号/外协员工类型/结算类型/ 不能为空
        //快递员 公司 外协公司/驾驶证号
        //快递员 个人 银行卡类型/持卡人/银行卡号/驾驶证号
        //仓管 公司 外协公司
        //仓管 个人 银行卡类型/持卡人/银行卡号
        if (empty($staffs['identity'])) {
            return ['identity', 'common_empty_error'];
        }

        if (!preg_match($this->identity_match, $staffs['identity'])) {
            return ['outsourcing_tip2'];
        }

        //员工姓名不能为空
        if (empty($staffs['staff_name'])) {
            return ['name', 'common_empty_error'];
        }
        //性别 普通外协验证
        if (empty($staffs['sex']) && in_array($hire_os_type, [BaseStaffInfo::HIRE_OS_TYPE_COMMON, BaseStaffInfo::HIRE_OS_TYPE_EMPLOY])) {
            return ['gender', 'common_empty_error'];
        }
        //性别
        if (!in_array($staffs['sex'], [1, 2]) &&in_array($hire_os_type, [BaseStaffInfo::HIRE_OS_TYPE_COMMON, BaseStaffInfo::HIRE_OS_TYPE_EMPLOY])) {
            return ['gender', 'outsourcing_tip4'];
        }
        //手机号

        if (!preg_match($this->mobile_match, $staffs['mobile'])) {
            return ['excel_mobile_number', 'outsourcing_tip4'];
        }

        //外协类型
        if (!in_array($staffs['outsourcing_type'], ['company', 'individual'])) {
            return ['outsourcing_type', 'outsourcing_tip4'];
        }

        //结算类型
        if (!in_array($staffs['pay_type'], ['BY_DAY', 'BY_MONTH', 'CASH_PAY'])) {
            return ['pay_type', 'outsourcing_tip4'];
        }

        //快递员Bike Courier  Van Courier Tricycle Courier Car Courier Truck Driver驾驶证号
        if (in_array($job_id, $this->validate_job_ids) && in_array($hire_os_type, [BaseStaffInfo::HIRE_OS_TYPE_COMMON, BaseStaffInfo::HIRE_OS_TYPE_EMPLOY])) {
            if (empty($staffs['driver_license'])) {
                return ['excel_driver_license', 'common_empty_error'];
            }
            if (!preg_match("/^[\x{0E00}-\x{0E7F}a-zA-Z0-9\/\-.]{1,20}$/u", $staffs['driver_license'])) {
                return ['excel_driver_license', 'outsourcing_tip4'];
            }

            if (empty($staffs['car_no'])) {
                return ['outsourcing_tip16'];
            }
        }

        //邮箱验证
        if(!empty($staffs['personal_email'])){
            if(!filter_var($staffs['personal_email'],FILTER_VALIDATE_EMAIL)) {
                return ['personal_email','common_exception'];
            }
        }

        if ($staffs['outsourcing_type'] == 'company') {
            //公司 外协公司名称
            if (isCountry('TH') && empty($staffs['company_item_id']) && in_array($hire_os_type, [BaseStaffInfo::HIRE_OS_TYPE_COMMON, BaseStaffInfo::HIRE_OS_TYPE_EMPLOY])) {
                return ['company_name_ef', 'common_empty_error'];
            }
            if (!isCountry('TH') && empty($staffs['company_name_ef']) && in_array($hire_os_type, [BaseStaffInfo::HIRE_OS_TYPE_COMMON, BaseStaffInfo::HIRE_OS_TYPE_EMPLOY])) {
                return ['company_name_ef', 'common_empty_error'];
            }

            if (!empty($staffs['bank_no']) && !preg_match($this->bank_no_match, $staffs['bank_no'])) {
                return ['bank_no', 'outsourcing_tip4'];
            }
            return true;
        }

        //非普通外协 不再验证
        if (!in_array($hire_os_type, [BaseStaffInfo::HIRE_OS_TYPE_COMMON, BaseStaffInfo::HIRE_OS_TYPE_EMPLOY])) {
            return true;
        }

        /*** 以下是个人外协验证 ***/
        //卡类型
        if (empty($staffs['bank_id'])) {
            return ['bank_card_type', 'common_empty_error'];
        }
        //卡类型
        if (!in_array($staffs['bank_id'], array_keys(Yii::$app->sysconfig->all_bank_type()))) {
            return ['bank_card_type', 'outsourcing_tip4'];
        }
        //持卡人
        if (empty($staffs['bank_no_name'])) {
            return ['bank_no_name', 'common_empty_error'];
        }
        //银行卡号
        if (empty($staffs['bank_no'])) {
            return ['bank_no', 'common_empty_error'];
        }
        if (!preg_match($this->bank_no_match, $staffs['bank_no'])) {
            return ['bank_no', 'outsourcing_tip4'];
        }

        return true;
    }

    //验证提交字段
    public function validateOutSourcingField($staff, $job_id, $hire_os_type = 11)
    {
        $com = $this->commonValidateOutsourcingField($staff, $job_id, $hire_os_type);
        if ($com !== true) {
            return $com;
        }
        return true;
    }

    public function validateIndividualField($staff)
    {
        return true;
    }

    /**
     * @description:验证外协身份证号在正式员工数据中是否有异常
     * @param $identity
     * @param $hire_type
     * @param $black_origin_enum //    黑名单来源在 黑名单校验枚举  内 校验
     * @return:
     * @author: L.J
     * @time: 2023/2/3 10:14
     */
    public function validateOutSourcingIdentity($identity, $hire_type = '', $black_origin_enum = [])
    {
        if (empty($identity)) {
            return true;
        }

        //外协黑名单
        $hr_black = HrOutSourcingBlacklist::find()->where(['identity' => $identity])->andWhere(['status' => 1])->one();
        if (!empty($hr_black)) {
            return ['outsourcing_tip25'];
        }

        $staff_info = BaseStaffInfo::find()->where([
            'identity'     => $identity,
            'formal'       => [1, 4,],
            'is_sub_staff' => 0,
        ])->orderBy(['staff_info_id' => SORT_DESC])->asArray()->one();

        if (empty($staff_info)) {
            return true;
        }

        if ($staff_info['state'] == 2) {
            $setting_env          = SettingEnv::find()->where([
                'code' => [
                    'hris_os_leave_date_num',
                    'hris_os_leave_reason',
                ],
            ])->indexBy('code')->asArray()->all();
            $limit                = $setting_env['hris_os_leave_date_num']['set_val'] ?? 30;
            $hris_os_leave_reason = $setting_env['hris_os_leave_reason']['set_val'] ?? [];

            $hr_black = HrBlacklist::find()->select([
                'id',
                'type',
            ])->where(['identity' => $identity])->andWhere(['status' => 1])->asArray()->all();

            if (!empty($hr_black)) {
                //获取黑名单来源
                $hr_black_types = array_column($hr_black, 'type');

                //是否需要检验 指定离职原因
                if (empty($hris_os_leave_reason)) {
                    //非众包外协  验证黑名单
                    if ($hire_type != BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING) {
                        return ['outsourcing_tip25'];
                    }
                    //众包外协创建 当黑名单来源在入参中黑名单枚举值中：不允许创建工号
                    if (empty($black_origin_enum) || !empty(array_intersect($black_origin_enum, $hr_black_types))) {
                        return ['outsourcing_tip25'];
                    }
                } else {
                    $leave_reason = explode(',', $hris_os_leave_reason);
                    if (in_array($staff_info['leave_reason'], $leave_reason)) {
                        //非众包外协  验证黑名单
                        if ($hire_type != BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING) {
                            return ['outsourcing_tip25'];
                        }

                        //众包外协创建 当黑名单来源在入参中黑名单枚举值中：不允许创建工号
                        if (empty($black_origin_enum) || !empty(array_intersect($black_origin_enum, $hr_black_types))) {
                            return ['outsourcing_tip25'];
                        }
                    }
                }
            }

            $diffDate = (new \DateTime())->diff((new \DateTime($staff_info['leave_date'])))->format('%a');
            if ($diffDate <= $limit) {
                $error_message = Yii::$app->lang->get('outsourcing_tip27');
                $error_message = str_replace("{day_count}", $limit, $error_message);
                return [$error_message];
            }
        } else {
            //-- 110 Van courier / 13 Bike courier / 452 Boat courier/ 271 Hub Staff / 98 Shop Officer
            //不配代表所有职位都允许做外协，配置-1代表所有职位都不允许做外协）
            $val = SettingEnvService::getInstance()->getSetVal('staff_not_apply_outsourcing_job_title_id');
            if(!empty($val)) {
                if($val === '-1') {
                    return ['outsourcing_tip23'];
                }
                //[13, 98, 110, 271, 452]
                $setting_env_job_title_id = explode(',', $val);
                if (in_array($staff_info['job_title'], $setting_env_job_title_id)) {
                    return ['outsourcing_tip23'];
                }
            }
        }
        return true;
    }

    /**
     * 通过公司id 获取公司信息
     * @param $companyId
     * @return mixed
     */
    public function getOsCompanyInfo($companyId)
    {
        return OutsourcingCompany::find()->select(['company_name'])->where(['id' => $companyId])->asArray()->one();
    }

    /**
     * 获取所有HUB 外协公司信息
     * @param $columns
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getOsCompanyAll($columns)
    {
        return OutsourcingCompany::find()->select($columns)->asArray()->all(Yii::$app->get('r_backyard'));
    }

    /**
     * 取消工单：给 osm 外协公司的人发push
     * @param $out_company_id
     */
    public function sendPushToOsmCancel($out_company_id)
    {
        $params['out_company_id'] = $out_company_id;
        $params['title_key']      = 'os_new_order_title_cancel';
        $params['content_key']    = 'os_new_order_content_cancel';
        $params['src']            = 'osm';

        $params['message_scheme'] = 'osm://fe/tab?index=1';
        $res                      = $this->sendPushToOsm($params);
        if (!$res) {
            Yii::$app->logger->write_log('取消外协工单，发送push 到 osm:' . json_encode($params) . ' 发送失败',
                'notice');
        }
    }

    /**
     * 创建工单：给 osm 外协公司的人发push
     * @param $out_company_id
     */
    public function sendPushToOsmCreate($out_company_id)
    {
        //todo --- 若果外协公司有值，则给 外协公司的人发push
        $params['out_company_id'] = $out_company_id;
        $params['title_key']      = 'os_new_order_title';
        $params['content_key']    = 'os_new_order_content';
        $params['src']            = 'osm';

        $params['message_scheme'] = 'osm://fe/tab?index=1';
        $res                      = $this->sendPushToOsm($params);
        if (!$res) {
            Yii::$app->logger->write_log('创建外协工单，发送push 到 osm:' . json_encode($params) . ' 发送失败',
                'notice');
        }
    }

    /**
     * 分批发送push-osm
     * @param $params
     * @return bool
     */
    public function sendPushToOsm($params)
    {
        $deviceList = OutsourcingCompanyDeviceToken::find()->where(['company_id' => $params['out_company_id']])->asArray()->all();

        $partDeviceList = array_chunk($deviceList, 100);
        foreach ($partDeviceList as $oneBatch) {
            $this->sendPush($oneBatch, $params);
        }
        return true;
    }

    //发送 push
    public function sendPush($deviceList, $params)
    {
        $allSendData = [];
        foreach ($deviceList as $oneDevice) {
            $sendData['message_title']   = Yii::$app->lang->get($params['title_key'], '',
                $oneDevice['accept_language']);
            $sendData['message_content'] = Yii::$app->lang->get($params['content_key'], '',
                $oneDevice['accept_language']);
            $sendData['device_token']    = $oneDevice['device_token'];
            $sendData['device_type']     = $oneDevice['device_type'];
            $sendData['os']              = $oneDevice['os'];
            $sendData['src']             = $params['src'];
            $sendData['message_scheme']  = $params['message_scheme'] ?? '';

            $allSendData[] = $sendData;
        }
        if (empty($allSendData)) {
            return false;
        }
        return Yii::$app->jrpc->sendPushToOsm($allSendData);
    }

    /**
     * 当外协公司未将外协员工订单配置人员或未配置满人员时，向该账号登录手机推送APP通知
     * @param $params
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
    public function notConfigCompleteOsHubOrderSendPush($params): bool
    {
        $current_time = $params['current_time'];
        $end_time     = $params['end_time'];

        //获取待生效工单
        $order_list = HrOutsourcingOrder::find()
            ->where(['status' => 1])
            ->andWhere(['!=', 'out_company_id', 0])
            ->andWhere(['>', 'effective_date', $current_time])
            ->andWhere(['<=', 'effective_date', $end_time])
            ->asArray()
            ->all(Yii::$app->get('r_backyard'));

        if(empty($order_list)) {
            Yii::$app->logger->write_log(['发送结果' => '未找到需要配置的工单数据', 'params' => $params], 'info');
            return false;
        }

        $serial_no_ids = array_column($order_list, 'serial_no');

        $detail_list = HrOutsourcingOrderDetail::find()
            ->select(['count(serial_no) as count', 'serial_no'])
            ->where(['serial_no' => $serial_no_ids])
            ->andWhere(['is_del' => 0])
            ->groupBy('serial_no')
            ->indexBy('serial_no')
            ->asArray()
            ->all(Yii::$app->get('r_backyard'));

        $result = [];
        foreach ($order_list as $key => $value) {
            $serial_no          = $value['serial_no'];
            $out_company_id     = $value['out_company_id'];
            $order_detail       = $detail_list[$serial_no] ?? [];
            $order_detail_count = $order_detail['count'] ?? 0;

            if ($order_detail_count < $value['final_audit_num']) {
                //发送消息
                $send_result = $this->sendPushToOsm([
                    'out_company_id' => $value['out_company_id'],
                    'title_key'      => 'os_not_config_complete_order_title',
                    'content_key'    => 'os_not_config_complete_order_content',
                    'src'            => 'osm',
                    'message_scheme' => 'osm://fe/page?path=orderList&index=1',
                ]);
                $result[] = ['serial_no' => $serial_no, 'message' => '未配置完成发送push', 'result' => $send_result, 'out_company_id' =>$out_company_id];
            } else {
                $result[] = ['serial_no' => $serial_no, 'message' => '已经配置完成不发送push', 'out_company_id' =>$out_company_id];
            }
        }
        Yii::$app->logger->write_log(['发送结果' => $result, 'params' => $params], 'info');
        return true;
    }

    /**
     * 重新启用工号，将原来的户口信息，加进去
     * @param $before
     * @param $staff_arr
     * @return mixed
     */
    public static function addRegisterInfo($before, $staff_arr)
    {
        return $staff_arr;
    }

    /**
     * 外协员工拓展信息
     * @param $data
     * @return bool
     */
    public static function addOsStaffInfoExtend($data)
    {
        return true;
    }

    /**
     * 获取外协公司枚举信息
     * @param $companyId
     * @return string
     */
    public static function getOsCompanyEnums($companyId)
    {
        return '';
    }
}