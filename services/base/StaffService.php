<?php

namespace app\services\base;

use app\libs\Enums\Enums;
use app\libs\Enums\SuspendReasonEnums;
use app\libs\Enums\StoreCategoryEnums;
use app\libs\RedisListKeyEnums;
use app\libs\ValidationException;
use app\models\backyard\AutoCreateEmail;
use app\models\backyard\StaffResign;
use app\models\backyard\SuspensionManageLog;
use app\models\backyard\Ticket;
use app\models\backyard\TicketLog;
use app\models\fle\StaffInfo as StaffInfoFle;
use app\models\backyard\VehicleInfo;
use app\models\fle\StaffInfo;
use app\models\fle\SysStore;
use app\models\LoginForm;
use app\models\manage\HrStaffItems;
use app\models\manage\StaffItems;
use app\models\User;
use app\modules\v1\business\JobTitleDepartmentRelation;
use app\modules\v1\business\Staff;
use app\modules\v1\business\StaffInfoCheck;
use app\modules\v1\business\SysGroupDeptV2;
use app\modules\v1\config\AreaManager;
use Yii;
use yii\db\Query;
use app\models\manage\BaseStaffInfo;
use Exception;
use app\models\manage\StaffInfo as StaffModel;


class StaffService extends BaseService
{
    protected $queryField;

    protected function setQueryField($queryField)
    {
        $this->queryField = $queryField;
    }

    public function getQueryField()
    {
        return $this->queryField;
    }

    public function getStaffBaseInfo($staff_info_id)
    {
        return BaseStaffInfo::find()->where(['staff_info_id' => $staff_info_id])->one();
    }

    public function getMsCompanyType($contract_company_id)
    {
        return CompanyService::MS_COMPANY_ENUM_FLASH;
    }

    public function isLnt($contract_company_id): bool
    {
        return false;
    }
    //一个网点只有一个在职的员工的职位
    public $store_only_one_job_title = [16];
    //限制一个网点只能有一个职位的网点类型
    protected $check_job_title_store_category = [
        StoreCategoryEnums::SP,
        StoreCategoryEnums::DC,
        StoreCategoryEnums::BDC,
        StoreCategoryEnums::PDC,
        StoreCategoryEnums::CDC,
    ];


    public function getCEmployee()
    {
        return array_values(array_filter(SettingEnvService::getInstance()->getSetVal('c_employee',',')));
    }

    /**
     * 验证网点特定职位是否超编
     * @param $model
     * @return bool
     */
    public function checkStoreJobTitleIsOverstaffed($model): bool
    {
        //一个网点只能有一个网点主管
        if (in_array($model->job_title, $this->store_only_one_job_title) && !in_array($model->state,
                [2, 3]) && $model->is_sub_staff == 0) {
            if (SysStore::find()->where(['id' => $model->sys_store_id])->andWhere(['category' => $this->check_job_title_store_category])->exists()) {
                $job_title_count = BaseStaffInfo::find()
                    ->Where(['<>', 'staff_info_id', $model->staff_info_id ?? 0])
                    ->andWhere(['job_title' => $model->job_title])
                    ->andWhere(['sys_store_id' => $model->sys_store_id])
                    ->andWhere(['state' => 1])
                    ->andWhere(['is_sub_staff' => 0])
                    ->count();
                return $job_title_count > 0;
            }
        }
        return false;
    }


    /**
     * 恢复在职 移除黑灰名单逻辑
     * @param $staff_info_id
     * @param $identity
     * @param string $fbid
     * @return bool
     */
    public function removeBlackGreyList($before, $fbid,$newStaff)
    {
        return true;
    }

    /**
     * @throws ValidationException
     */
    public function login($params): array
    {
        $login = new LoginForm();
        $login->load($params, '');
        if (!$login->login()) {
            throw new ValidationException('Account Or Password error');
        }
        $user = $login->getUser(true,!empty($params['expireTime']) ? $params['expireTime'] : 864000);
        $returnDat['token'] = $user->accessToken;
        return  $returnDat;
    }

    public function logout($params)
    {
        return User::deleteSession($params['staff_id']);
    }



    public static $config_view = ['formal', 'store', 'trainee', 'download'];

    public $change_store_manager_store_category = [StoreCategoryEnums::SP, StoreCategoryEnums::DC, StoreCategoryEnums::BDC,StoreCategoryEnums::PDC, StoreCategoryEnums::CDC];

    public function getTest() {
        return $this->change_store_manager_store_category;
    }

    public function getStaffListQueryObject($params): Query
    {
        //新版查询
        $whereDepartment     = $params['department_id'] ?? null;
        $is_query_department = SysDepartmentService::getSubDepartmentByIsSub($whereDepartment, $params);

        if (!empty($params['job_title'])) {
            $jobTitle = $params['job_title'];
        }
        $state = null;
        if (!empty($params['state'])) {
            $state = $params['state'];
        }

        if (!empty($params['store'])) {
            $store = $params['store'];
        }

        $name = null;
        if (!empty($params['name'])) {
            $name = trim($params['name']);
        }

        $mobile = null;
        if (!empty($params['mobile_identity_bank'])) {
            $mobile = $params['mobile_identity_bank'];
        }

        if (!empty($params['mobile'])) {
            $mobile = $params['mobile'];
        }

        if (!empty($params['bank_no'])) {
            $bank_no = $params['bank_no'];
        }

        if (!empty($params['identity'])) {
            $identity = $params['identity'];
        }


        if (!empty($params['manage_staff_id'])) {
            $manger = $params['manage_staff_id'];
        }

        if (!empty($params['staff_info_id'])) {
            $staff_info_id = $params['staff_info_id'];
        }

        if (!empty($params['nationality'])) {
            $nationality = $params['nationality'];
        }


        if (!empty($params['working_country'])) {
            $working_country = $params['working_country'];
        }

        //合同公司
        if (!empty($params['contract_company_id'])) {
            $contract_company_id = $params['contract_company_id'];
        }

        if (!empty($params['store_area_id'])) {
            switch (YII_COUNTRY) {
                case 'MY':
                    $stores = Yii::$app->sysconfig->getStoresBySortingNoV2(AreaManager::$areasMy,
                        $params['store_area_id']);
                    break;
                case 'LA':
                    $stores = Yii::$app->sysconfig->getStoresBySortingNoV2(AreaManager::$areasLa,
                        $params['store_area_id']);
                    break;
                default:
                    $stores = Yii::$app->sysconfig->getStoresByAreaV2($params['store_area_id']);
            }
        }


        $staffType = null;
        if (!empty($params['staff_type'])) {
            $staff_type = trim($params['staff_type']);
            switch ($staff_type) {
                case 1:
                    $staffType = [1];
                    break;
                case 2:
                    $staffType = [2, 3];
                    break;
                case 4:
                    $staffType = [4];
                    break;
                case 5:
                    $staffType = [5];
                    break;
            }
        }

        $notEqStaff = null;
        if (!empty($params['not_eq_staff'])) {
            $notEqStaff = trim($params['not_eq_staff']);
        }

        /*
         * 判断条件
         * 指定角色(系统管理员[14]/人事专员[16]/人事管理员[17]/HRIS管理员[41]/薪酬管理员[42]/人力资源[43]/HRBP[68]) 可以查看全部
         *指定部门查询
         */
        $_view = $params['_view'];

        if (in_array($_view, self::$config_view)) {
            if (!count(array_intersect(staff::$view_all_roles, $params['user_fbi_role']))) {
                $andConditions = [];
                if (count(array_intersect(staff::$view_manage_range_roles, $params['user_fbi_role']))) {
                    //  管辖范围
                    $result                              = Staff::dominDepartmentsAndStores($params['user_staff_id']);
                    $andConditions['node_department_id'] = $result['flash_home_departments'];
                    $andConditions['sys_store_id']       = $result['stores'];
                }
                if (in_array('OPERATION_SPECIALIST', $params['user_fbi_role'])) {
                    //运营主管[63]权限：仅查看——所属一级部门及一级下级各级部门员工
                    $dept_ids = SysGroupDeptV2::getDeptIdByManagerIdV2($params['user_staff_id'],
                        $params['user_departmentId']);
                } else {
                    $dept_ids = SysGroupDeptV2::getDeptIdByManagerId($params['user_staff_id'], 0); // 去掉自己所属部门
                    $dept_ids = array_unique(array_merge($dept_ids,
                        SysGroupDeptV2::getDeptIdByAssistantId($params['user_staff_id'], 0)));     // 去掉自己所属部门
                    $dept_ids = array_values($dept_ids);
                }

                if (isset($andConditions) && isset($andConditions['node_department_id'])) {
                    $andConditions['node_department_id'] = array_merge($andConditions['node_department_id'], $dept_ids);
                } else {
                    $andConditions['node_department_id'] = $dept_ids;
                }

                $next_level_staff_ids           = Staff::getNextLevelStaff($params['user_staff_id']);
                $andConditions['staff_info_id'] = $next_level_staff_ids;
                $orConditions[]                 = 'OR';
                foreach ($andConditions as $k => $v) {
                    if ($k == 'sys_store_id' && in_array('-2', (array)$v)) {
                        $orConditions[] = ['NOT IN', 'hr_staff_info.' . $k, ['-1']];
                    } else {
                        $orConditions[] = ['IN', 'hr_staff_info.' . $k, $v ?? []];
                    }
                }
                Yii::$app->logger->write_log('rolesPermission 结果：' . json_encode($andConditions,
                        JSON_UNESCAPED_UNICODE), 'info');
            }
        }    // 合作商
        $formal = $params['formal'] ?? 0;


        $queryField = 'hr_staff_info.*';
        $isFormal = false;
        if (in_array($formal, [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE])) {
            $queryField = 'hr_staff_info.*,hr_staff_info_extend.project_num,
            hr_staff_info_extend.van_container_state,hr_staff_info_extend.van_container_start,hr_staff_info_extend.van_container_end';
            $isFormal = true;
        }
        $queryField .= ',sys_store.name as store_name,sys_store.sorting_no as store_sorting_no ,sys_store.province_code as store_province_code,sys_store.manage_region as store_manage_region,sys_store.manage_piece as store_manage_piece';
        $this->setQueryField($queryField);
        $query = (new Query())->select($queryField)->from('hr_staff_info');

        $query->leftJoin('sys_store', 'sys_store.id = hr_staff_info.sys_store_id');
        if (!empty($params['is_offical_partner']) && empty($formal)) {
            $query->where(['IN', 'hr_staff_info.formal', [2, 3]]);
        } else {
            if (!empty($params['from']) && $params['from'] == 'job_title_grade_whitelist') {
                $query->where(['IN', 'hr_staff_info.formal', [1, 4]]);
            } else {
                if ($formal == 1) {//如果是正式员工列表，包含实习生
                    $query->where(['IN', 'hr_staff_info.formal', [1, 4]]);
                } else {
                    $query->where(['hr_staff_info.formal' => $formal]);
                }
            }
        }

        if ($isFormal) {
            $query->leftJoin('hr_staff_info_extend',
                'hr_staff_info_extend.staff_info_id = hr_staff_info.staff_info_id');
            //保险受益人
            if (!empty($params['insurance_beneficiary_status'])) {
                $query->andFilterWhere(['hr_staff_info_extend.insurance_beneficiary_status' => $params['insurance_beneficiary_status']]);
            }
        }

        if (isset($orConditions)) {
            $query->andWhere($orConditions);
        }

        //大区筛选
        if (!empty($params['manage_region'])) {
            $manage_region_array = is_array($params['manage_region']) ? array_values($params['manage_region']) : [$params['manage_region']];
            $query->andWhere(['IN', 'sys_store.manage_region', $manage_region_array]);
        }
        //片区筛选
        if (!empty($params['manage_piece'])) {
            $manage_piece_array = is_array($params['manage_piece']) ? array_values($params['manage_piece']) : [$params['manage_piece']];
            $query->andWhere(['IN', 'sys_store.manage_piece', $manage_piece_array]);
        }

        // 雇佣类型
        if (!empty($params['hire_type'])) {
            $query->andwhere(['IN', 'hr_staff_info.hire_type', $params['hire_type']]);
        }

        //职级
        if (!empty($params['job_title_grade'])) {
            $c_employee = $this->getCEmployee();
            $c_employee && $query->andwhere(['NOT IN', 'hr_staff_info.staff_info_id', $c_employee]);
            $query->andwhere(['IN', 'hr_staff_info.job_title_grade_v2', $params['job_title_grade']]);
        }

        // 入职日期-开始
        if (!empty($params['hire_date_begin'])) {
            $query->andFilterWhere([
                '>=',
                'hr_staff_info.hire_date',
                date('Y-m-d', strtotime($params['hire_date_begin'])),
            ]);
        }
        // 入职日期-结束
        if (!empty($params['hire_date_end'])) {
            $query->andFilterWhere([
                '<=',
                'hr_staff_info.hire_date',
                date('Y-m-d', strtotime($params['hire_date_end'])),
            ]);
        }

        // 离职日期-开始
        if (!empty($params['leave_date_begin'])) {
            $query->andFilterWhere([
                '>=',
                'hr_staff_info.leave_date',
                date('Y-m-d', strtotime($params['leave_date_begin'])),
            ]);
        }
        // 离职日期-结束
        if (!empty($params['leave_date_end'])) {
            $query->andFilterWhere([
                '<=',
                'hr_staff_info.leave_date',
                date('Y-m-d', strtotime($params['leave_date_end'])),
            ]);
        }

        // 仅仅为快捷入口站点
        if (!empty($params['is_only_store'])) {
            $query->andFilterWhere(['<>', 'sys_store_id', '-1']);
        }

        //职级
        if (!empty($params['job_title_grade']) && (is_numeric($params['job_title_grade']) || is_array($params['job_title_grade']))) {
            $job_title_grade = $params['job_title_grade'];
        }

        // from
        if (!empty($params['from']) && $params['from'] == 'job_title_grade_whitelist') {
            $query->andFilterWhere(['hr_staff_info.is_has_job_grade_permission' => 1]);
        }

        if (!empty($state)) {
            if (in_array(999, $state)) {
                $query->andFilterWhere(
                    [
                        'OR',
                        ['hr_staff_info.state' => $state],
                        ['hr_staff_info.wait_leave_state' => BaseStaffInfo::WAIT_LEAVE_STATE_YES],
                    ]
                );
            } else {
                if ($formal == BaseStaffInfo::FORMAL_OUTSOURCE && in_array(BaseStaffInfo::STATE_ON_JOB, $state)) {
                    // 外协员工只有在职和离职 需要特殊处理
                    $query->andFilterWhere(['hr_staff_info.state' => $state]);
                } else {
                    $query->andFilterWhere(['hr_staff_info.state' => $state])
                        ->andFilterWhere(['hr_staff_info.wait_leave_state' => BaseStaffInfo::WAIT_LEAVE_STATE_NO]);
                }
            }
        }

        // 需要查询子部门时 进行条件拼接
        if ($is_query_department && !empty($whereDepartment)) {
            $query->andFilterWhere(['node_department_id' => $whereDepartment]);
            //$query->andFilterWhere(['node_department_id' => $whereDepartment ?? null]);
        }

        $query->andFilterWhere(['job_title' => $jobTitle ?? null])
            ->andFilterWhere(['hr_staff_info.sys_store_id' => $store ?? null])
            ->andFilterWhere(['hr_staff_info.sys_store_id' => $stores ?? null])
            ->andFilterWhere(['hr_staff_info.staff_type' => $staffType ?? null])
            ->andFilterWhere(['hr_staff_info.working_country' => $working_country ?? null])
            ->andFilterWhere(['hr_staff_info.manger' => $manger ?? null])
            ->andFilterWhere(['hr_staff_info.staff_info_id' => $staff_info_id ?? null])
            ->andFilterWhere(['hr_staff_info.nationality' => $nationality ?? null])
            ->andFilterWhere(['hr_staff_info.contract_company_id' => $contract_company_id ?? null])
            ->andFilterWhere([
                'OR',
                ['LIKE', 'hr_staff_info.mobile', $mobile],
                ['LIKE', 'hr_staff_info.mobile_company', $mobile],
            ])
            ->andFilterWhere(['hr_staff_info.bank_no' => $bank_no ?? null])
            ->andFilterWhere(['hr_staff_info.identity' => $identity ?? null])
            ->andFilterWhere(['hr_staff_info.job_title_grade_v2' => $job_title_grade ?? null])
            ->andFilterWhere([
                'OR',
                ['LIKE', 'hr_staff_info.name', $name],
                ['LIKE', 'hr_staff_info.name_en', $name],
                ['LIKE', 'hr_staff_info.nick_name', $name],
            ])
            ->andFilterWhere(['<>', 'hr_staff_info.staff_info_id', $notEqStaff]);


        if (!empty($params['position'])) {
            $positions = !is_array($params['position']) ? explode(',', $params['position']) : $params['position'];
            $subQuery  = (new Query())->select('staff_info_id')
                ->where(['IN', 'hr_staff_info_position.position_category', array_values($positions)])
                ->andWhere('hr_staff_info.staff_info_id = hr_staff_info_position.staff_info_id')
                ->from('hr_staff_info_position');
            $query->andWhere(['IN', 'hr_staff_info.staff_info_id', $subQuery]);
        }

        // 关联查询
        $vehicle_type_category = $params['vehicle_type_category'] ?? null;
        if (!empty($params['pay_type']) || !empty($params['outsourcing_type']) || !empty($vehicle_type_category)) {
            $item_where  = [];
            $value_where = [];
            // 外协类型 改为结算类型
            if (!empty($params['pay_type'])) {
                $item_where[]  = StaffItems::ITEM_PAY_TYPE;
                $value_where[] = $params['pay_type'];
            }
            //外协类型 individual  company
            if (!empty($params['outsourcing_type'])) {
                $item_where[]  = 'OUTSOURCING_TYPE';
                $value_where[] = $params['outsourcing_type'];
            }

            if (!empty($vehicle_type_category)) {
                foreach ($vehicle_type_category as $key => $value) {
                    $value_where[] = VehicleInfo::$vehicleTypeCategoryMY[$value] ?? '-';
                }
                $item_where[] = 'VEHICLE_TYPE_CATEGORY';

                $show_vehicle_type_category_job_title = SettingEnvService::getInstance()->getSetVal('show_vehicle_type_category_job_title');
                $show_vehicle_type_job_title_arr      = [];
                if (!empty($show_vehicle_type_category_job_title)) {
                    $show_vehicle_type_job_title_arr = explode(',', $show_vehicle_type_category_job_title);
                }
                if (!empty($show_vehicle_type_job_title_arr)) {
                    $query->andWhere(['hr_staff_info.job_title' => $show_vehicle_type_job_title_arr]);
                }
            }

            if (!empty($item_where) && !empty($value_where)) {
                $subQuery = (new Query())->select('staff_info_id')
                    ->where([
                        'hr_staff_items.item'  => $item_where,
                        'hr_staff_items.value' => $value_where,
                    ])
                    ->andWhere('hr_staff_info.staff_info_id = hr_staff_items.staff_info_id')
                    ->from('hr_staff_items');
                $query->andWhere(['IN', 'hr_staff_info.staff_info_id', $subQuery]);
            }
        }

        // 排除子账号，关联子账号使用
        if (!empty($params['can_master_staff'])) {
            $subQuery = (new Query())->select('staff_info_id')
                ->where([
                    'hr_staff_items.item' => StaffItems::IIEM_MASTER_STAFF,
                ])
                ->andWhere('hr_staff_info.staff_info_id = hr_staff_items.staff_info_id')
                ->from('hr_staff_items');
            $query->andWhere(['NOT IN', 'hr_staff_info.staff_info_id', $subQuery]);
        }


        //外协 是否完善地址信息
        if ($formal == BaseStaffInfo::FORMAL_OUTSOURCE && isset($params['is_complete_address']) && $params['is_complete_address'] != '') {
            $subQuery = (new Query())->select('staff_info_id')
                ->where([
                    'os_staff_info_extend.is_complete_address' => $params['is_complete_address'],
                ])
                ->andWhere('hr_staff_info.staff_info_id = os_staff_info_extend.staff_info_id')
                ->from('os_staff_info_extend');
            $query->andWhere(['IN', 'hr_staff_info.staff_info_id', $subQuery]);
        }

        //外协公司枚举筛选
        if ($formal == BaseStaffInfo::FORMAL_OUTSOURCE && !empty($params['os_company_id'])) {
            $subQuery = (new Query())->select('staff_info_id')
                ->where([
                    'os_staff_info_extend.company_item_id' => $params['os_company_id'],
                ])
                ->andWhere('hr_staff_info.staff_info_id = os_staff_info_extend.staff_info_id')
                ->from('os_staff_info_extend');
            $query->andWhere(['IN', 'hr_staff_info.staff_info_id', $subQuery]);
        }

        $query->andWhere(['hr_staff_info.is_sub_staff' => 0]);

        return $query;
    }


    /**
     * @param $staff_info_ids
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getStaffListByStaffInfoIds($staff_info_ids)
    {
        $list = BaseStaffInfo::find()
            ->select([
                'staff_info_id',
                'name',
                'node_department_id',
                'job_title',
                'sys_store_id',
                'state',
                'hire_date',
                'stop_duties_date',
                'leave_date',
                'leave_reason',
                'stop_duty_reason',
                'hire_type',
                'formal',
                'bank_no',
                'bank_type',
            ])
            ->where(['staff_info_id' => $staff_info_ids])
            ->asArray()
            ->indexBy('staff_info_id')
            ->all();
        return $list;
    }

    public static function reinstatement($oldState, $form)
    {
        // 不是恢复在职时则直接返回，不做其他处理
        if (2 != $oldState || 1 != $form->state) {
            return true;
        }

        try {
            // 员工恢复在职逻辑 $staffInfoModel->state ==  2 && $form['state'] == 1 为恢复在职
            //1、 查询复职员工是否存在待回复邮箱类的IT工单
            $tickerList = Ticket::find()->select(['id', 'ticket_id', 'created_id'])
                ->where(['created_id' => 10003]) // 10003 是系统发送
                ->andWhere(['status' => 1])      // 待回复
                ->andWhere(['item_type' => 5])   // 邮箱类型
                ->andWhere(['staff_info_id' => $form->staff_info_id])
                ->asArray()->all();

            // 2、如果存在待回复的工单，则需要进行关闭处理。不存在则不做任何处理。
            if (is_array($tickerList) && !empty($tickerList)) {
                $attr    = $ids = [];
                $curTime = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR * 3600);
                foreach ($tickerList as $key => $value) {
                    array_push($ids, (int)$value['id']);
                    $attr[] = [
                        $value['id'],
                        10003,
                        2,
                        Yii::$app->lang->get('system_close_it_order'),
                        $curTime,
                        3,
                    ];
                }

                // 更新 ticket 表的状态为关闭。
                $sql = "UPDATE `" . Ticket::tableName() . "` SET `status` = 3,`updated_at` = '{$curTime}' WHERE id IN (" . implode($ids,
                        ',') . ")";
                Yii::$app->backyard->createCommand($sql)->execute();

                // 记录IT工单回复日志。
                Yii::$app->backyard->createCommand()->batchInsert(
                    TicketLog::tableName(),
                    [
                        'ticket_id',
                        'created_id',
                        'created_type',
                        'mark',
                        'created_at',
                        'status',
                    ],
                    $attr
                )->execute();
            }
        } catch (Exception $e) {
            Yii::$app->logger->write_log('回复在职人员工单关闭失败' . json_encode([
                    $e->getMessage(),
                    $e->getFile(),
                    $e->getLine(),
                ], JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 获取同步工号信息
     * @param $staffInfoId
     * @return bool
     */
    public function getSyncStaffInfo($staffInfoId)
    {
        return Staff::isSynchronizationStaffInfo($staffInfoId);
    }


    /**
     * 员工状态变更处理逻辑
     * @param $oldStaffInfo
     * @param $staffInfo
     * @return bool
     */
    public function staffStateChange($oldStaffInfo, $staffInfo,$fbid = '-1'): bool
    {
        Yii::$app->logger->write_log([
            'staffStateChange' => 'staffStateChange_log ',
            'old_state'        => $oldStaffInfo['state'] ?? null,
            'new_state'        => $staffInfo['state'] ?? null,
            'staff_info_id'    => $staffInfo['staff_info_id'] ?? null,
            'fbid'    => $fbid,
            'wait_leave_state'    => $staffInfo['wait_leave_state'] ?? null,
        ], 'info');
        // 清空社保离职日期
        if (YII_COUNTRY == 'TH' && !empty($oldStaffInfo) && $oldStaffInfo['state'] == BaseStaffInfo::STATE_RESIGN && $staffInfo['state'] != BaseStaffInfo::STATE_RESIGN && in_array($staffInfo['formal'],
                [1, 4]) && $staffInfo['is_sub_staff'] == 0 && $staffInfo['hire_type'] != 13) {
            $items_social_security_leave_date = HrStaffItems::find()->where([
                'staff_info_id' => $staffInfo['staff_info_id'],
                'item'          => 'SOCIAL_SECURITY_LEAVE_DATE',
            ])->asArray()->one();
            if ($items_social_security_leave_date) {
                HrStaffItems::updateAll(['value' => ''], ['id' => $items_social_security_leave_date['id']]);
            }
        }

        // 添加社保离职日期
        $only_edit_leave_date = !empty($oldStaffInfo) && ($oldStaffInfo['state'] == BaseStaffInfo::STATE_RESIGN && $staffInfo['state'] == BaseStaffInfo::STATE_RESIGN && date('Y-m-d',strtotime($staffInfo['leave_date'])) != date('Y-m-d',strtotime($oldStaffInfo['leave_date'])));
        if (
            YII_COUNTRY == 'TH' && 
            !empty($oldStaffInfo) && 
            (
                ($oldStaffInfo['state'] != BaseStaffInfo::STATE_RESIGN && $staffInfo['state'] == BaseStaffInfo::STATE_RESIGN) ||
                $only_edit_leave_date
            ) && 
            in_array($staffInfo['formal'],[BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE]) && 
            $staffInfo['is_sub_staff'] == BaseStaffInfo::IS_SUB_STAFF_NO && 
            $staffInfo['hire_type'] != BaseStaffInfo::HIRE_TYPE_UN_PAID
        ) {
            $staffInfo['only_edit_leave_date'] = $only_edit_leave_date;
            $staffInfo['operator_id'] = $fbid;
            Yii::$app->redis->lpush(RedisListKeyEnums::SOCIAL_SECURITY_LEAVE_DATE, json_encode($staffInfo));
        }

        // 离职状态变在职状态 需走撤销离职逻辑
        if (!empty($oldStaffInfo) && !empty($staffInfo) && $oldStaffInfo['state'] == BaseStaffInfo::STATE_RESIGN && $staffInfo['state'] == BaseStaffInfo::STATE_ON_JOB && $staffInfo['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_NO){
            $staffResign = StaffResign::find()->where([
                'submitter_id' => $staffInfo['staff_info_id'],
            ])->orderBy('resign_id desc')->one();
            // 和hcm getStaffResignTipAction 逻辑一致
            $today = gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600);
            if (!empty($staffResign) && $staffResign->status == 2 && !empty($staffResign->leave_date) && date('Y-m-d',strtotime($staffResign->leave_date)) > $today){
                Yii::$app->redis->lpush(RedisListKeyEnums::LEAVE_MANAGER_CANCEL, json_encode(['staff_info_id'=>$staffInfo['staff_info_id'],'fbid'=>$fbid]));
            }
        }

        //恢复在职
        if (!empty($oldStaffInfo) && in_array($oldStaffInfo['state'], [
                BaseStaffInfo::STATE_SUSPENSION,
                BaseStaffInfo::STATE_RESIGN,
            ]) && $staffInfo['state'] == BaseStaffInfo::STATE_ON_JOB) {

            //停职原因：未回公款、连续旷工，停职恢复在职
            //向恢复在职员工本人的“个人号码”发送手机短信（泰语）
            //向恢复在职员工直线上级、对应HRBP发送BY/KIT-Push通知（根据用户登录语言）
            if (in_array($staffInfo['formal'], [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE])
                && $staffInfo['is_sub_staff'] == 0
                && $oldStaffInfo['state'] == BaseStaffInfo::STATE_SUSPENSION
                && in_array($oldStaffInfo['stop_duty_reason'], [BaseStaffInfo::STOP_DUTY_REASON_6, BaseStaffInfo::STOP_DUTY_REASON_7])
            ) {
                $this->StaffSuspendedReinstatementSendMsg([
                    'staff_info_id'      => $staffInfo['staff_info_id'],
                    'name'               => $staffInfo['name'],
                    'mobile'             => $staffInfo['mobile'],
                    'manager_id'         => $staffInfo['manger'],
                    'node_department_id' => $staffInfo['node_department_id'],
                    'sys_store_id'       => $staffInfo['sys_store_id'],
                ]);
            }

            return Yii::$app->redis->lpush(RedisListKeyEnums::CHANGE_STAFF_STATE_ON_JOB_REDIS_KEY,
                json_encode($staffInfo));
        }

        if ($staffInfo['nationality'] == BaseStaffInfo::COUNTRY_CN && $staffInfo['working_country'] != BaseStaffInfo::COUNTRY_CN && $staffInfo['formal'] == BaseStaffInfo::FORMAL_YES) {
            if (empty($oldStaffInfo) || $oldStaffInfo['state'] == BaseStaffInfo::STATE_RESIGN && $staffInfo['state'] == BaseStaffInfo::STATE_ON_JOB) {
                $staffInfo['china_staff_state_change_notice_type'] = BaseStaffInfo::STATE_ON_JOB;
                Yii::$app->logger->write_log([
                    'staffStateChange' => 'china_staff_state_change_notice_type_1',
                    'old_state'        => $oldStaffInfo['state'] ?? null,
                    'new_state'        => $staffInfo['state'],
                    'staff_info_id'    => $staffInfo['staff_info_id'],
                ], 'info');//响应
                return Yii::$app->redis->lpush(RedisListKeyEnums::CHINA_STAFF_STATE_CHANGE_NOTICE,
                    json_encode($staffInfo));
            }
            if (!empty($oldStaffInfo) && $oldStaffInfo['state'] != BaseStaffInfo::STATE_RESIGN && $staffInfo['state'] == BaseStaffInfo::STATE_RESIGN) {
                $staffInfo['china_staff_state_change_notice_type'] = BaseStaffInfo::STATE_RESIGN;
                Yii::$app->logger->write_log([
                    'staffStateChange' => 'china_staff_state_change_notice_type_2',
                    'old_state'        => $oldStaffInfo['state'],
                    'new_state'        => $staffInfo['state'],
                    'staff_info_id'    => $staffInfo['staff_info_id'],
                ], 'info');//响应
                return Yii::$app->redis->lpush(RedisListKeyEnums::CHINA_STAFF_STATE_CHANGE_NOTICE,
                    json_encode($staffInfo));
            }
        }
        return true;
    }

    /**
     * 同步oa新离职资产 离职员工信息写入redis队列
     * @param $params
     * @return bool
     */
    public function pushRedisSyncResignAssetsStaff($params): bool
    {
        try {
            $result = Yii::$app->redis->lpush(RedisListKeyEnums::SYNC_RESIGN_ASSETS_STAFF, json_encode($params));
            Yii::$app->logger->write_log([
                'function' => 'pushRedisSyncResignAssetsStaff',
                'params'   => $params,
                'result'   => $result,
            ], 'info');
            return true;
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'pushRedisSyncResignAssetsStaff',
                'params'   => $params,
                'message'  => $e->getMessage(),
                'line'     => $e->getLine(),
                'file'     => $e->getFile(),
            ]);
            return false;
        }
    }

    /**
     * 同步oa新离职资产
     * @param $event_params
     * @return array
     */
    public function syncResignAssetsStaff($event_params): array
    {
        $result_data = ['code' => 1, 'message' => 'success'];
        try {
            $event_type         = $event_params['event_type'];
            $params             = $event_params['params'];
            $lang               = $params['lang'] ?? 'en';
            $method             = '';
            $body_params        = [];
            $operation_staff_id = $params['operation_staff_id'];

            $operation = StaffInfoFle::find()->select([
                'id',
                'name',
            ])->where(['id' => $operation_staff_id])->asArray()->one();

            $operation_staff_id = $operation_staff_id == -1 ? 10000 : $operation_staff_id;
            //在职变离职/待离职
            if ($event_type == 'staff_resign') {
                $method      = 'create_leave_asset';
                $body_params = [
                    'staff_info_id'      => $params['staff_info_id'],
                    'source'             => $params['source'],
                    'operation_staff_id' => $operation_staff_id,
                    'staff_state'        => $params['staff_state'],
                    'leave_date'         => date('Y-m-d', strtotime($params['leave_date'])),
                    'last_work_date'     => $params['last_work_date'],
                    'staff_resign_id'    => $params['staff_resign_id'] ?? 0,
                    'work_handover'      => $params['work_handover'] ?? 0,
                ];
            }

            //离职变在职
            if ($event_type == 'staff_work') {
                $method      = 'cancel_leave_asset';
                $body_params = [
                    'staff_info_id' => $params['staff_info_id'],
                    'staff_name'    => $params['staff_name'],
                    'update_id'     => $operation_staff_id,
                    'update_name'   => $operation['name'] ?? 'system auto',
                    'update_remark' => $params['cancel_reason'] ?? '', //hcm 撤销离职会有撤销原因，其他没有
                ];
            }
            //直线上级变更
            if ($event_type == 'change_manager') {
                $method      = 'leave_asset_manager_change';
                $body_params = [
                    'staff_info_id'     => $params['staff_info_id'],
                    'before_manager_id' => $params['before_manager_id'],
                    'after_manager_id'  => $params['after_manager_id'],
                    'update_id'         => $params['operation_staff_id'],
                    'update_name'       => $operation['name'] ?? 'system auto',
                ];
            }

            if (!empty($method) && !empty($body_params)) {
                $body    = [
                    'jsonrpc' => '2.0',
                    'method'  => $method,
                    'params'  => [['locale' => $lang], $body_params],
                    'id'      => time(),
                ];
                $res     = Yii::$app->http->json_rpc_post(Yii::$app->params['oa_rpc_endpoint'], $body);
                $res_arr = json_decode($res, true);
                $result = $res_arr['result'] ?? [];
                if (isset($result['code']) && in_array($result['code'], [1, 20420, 20421, 20422, 20423, 20424])) {
                    Yii::$app->logger->write_log([
                        'function'    => 'syncResignAssetsStaff',
                        'params'      => $params,
                        'body_params' => $body,
                        'result'      => $res,
                    ], 'info');
                } else {
                    Yii::$app->logger->write_log([
                        'function'    => 'syncResignAssetsStaff',
                        'params'      => $params,
                        'body_params' => $body,
                        'result'      => $res,
                    ], 'notice');
                    $result_data = ['code' => 0, 'message' => $res];
                }
            } else {
                Yii::$app->logger->write_log([
                    'function' => 'syncResignAssetsStaff',
                    'message'  => '参数异常',
                    'params'   => $params,
                ], 'notice');
                $result_data = ['code' => 0, 'message' => '参数异常'];
            }
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'syncResignAssetsStaff',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
                'params'   => $params,
            ]);
            $result_data = ['code' => 0, 'message' => $e->getMessage()];
        }
        return $result_data;
    }

    /**
     * 获取车类型 只有马来有
     * @param $staff_info_ids
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public function getVehicleTypeCategoryList($staff_info_ids): array
    {
        return [];
    }

    /**
     * 添加停职记录
     * @param $params
     */
    public function addSuspensionManageLog($params)
    {
        SuspensionManageLog::updateAll(['is_new' => SuspensionManageLog::IS_NEW_NO],
            ['staff_info_id' => $params['staff_info_id']]);

        $suspensionManageLogModel                   = new SuspensionManageLog();
        $suspensionManageLogModel->staff_info_id    = $params['staff_info_id'];
        $suspensionManageLogModel->stop_duties_date = $params['stop_duties_date'];
        $suspensionManageLogModel->stop_duty_reason = $params['stop_duty_reason']??BaseStaffInfo::STOP_DUTY_REASON_5;
        $suspensionManageLogModel->is_new           = SuspensionManageLog::IS_NEW_YES;
        $suspensionManageLogModel->operate_id       = $params['operate_id'];
        $suspensionManageLogModel->origin_info      = $params['origin_info'];

        $res = $suspensionManageLogModel->save();
        if ($res && $params['stop_duty_reason'] == SuspendReasonEnums::SUSPEND_TYPE_REASON_ABSENT){
            $this->sendSuspensionEmail(['suspension_manage_log_id' => $suspensionManageLogModel->id]);
        }
        if(!$res) {
            Yii::$app->logger->write_log('addSuspensionManageLog params:'.json_encode($params,JSON_UNESCAPED_UNICODE));
            return false;
        }
        return true;
    }

    /**
     * 修改停职记录
     * 员工当前状态为停职 状态，修改停职信息
     * 如果没有停职记录 则加一条。
     * @param $params
     * @return bool
     */
    public function updateSuspensionManageLog($params)
    {
        $suspensionInfo = SuspensionManageLog::find()->where(['staff_info_id' => $params['staff_info_id'], 'is_new' => SuspensionManageLog::IS_NEW_YES])->asArray()->one();
        if($suspensionInfo) {
            if($suspensionInfo['stop_duties_date'] != $params['stop_duties_date'] || $suspensionInfo['stop_duty_reason'] != $params['stop_duty_reason']) {
                $updateData['stop_duties_date'] = $params['stop_duties_date'];
                $updateData['stop_duty_reason'] = $params['stop_duty_reason'];
                $updateData['operate_id']       = $params['operate_id'];

                SuspensionManageLog::updateAll($updateData, ['id' => $suspensionInfo['id']]);

                Yii::$app->logger->write_log(['func' => 'updateSuspensionManageLog', 'sus_id' => $suspensionInfo['id'], 'before' => $suspensionInfo, 'params' => $updateData], 'info');
            }
        }

        $issetSuspensionInfo = SuspensionManageLog::find()->where(['staff_info_id' => $params['staff_info_id']])->asArray()->one();
        if (empty($issetSuspensionInfo)) {
            $addData['staff_info_id']    = $params['staff_info_id'];
            $addData['stop_duties_date'] = $params['stop_duties_date'];
            $addData['stop_duty_reason'] = $params['stop_duty_reason'];
            $addData['operate_id']       = $params['operate_id'];
            $addData['origin_info']      = $params['origin_info'];
            $res                         = $this->addSuspensionManageLog($addData);
            Yii::$app->logger->write_log('updateSuspensionManageLog add params:' . json_encode($addData,
                    JSON_UNESCAPED_UNICODE), 'info');
            if ($res) {
                return true;
            }

            return false;
        }

    }
    
    public function sendSuspensionEmail($params)
    {
        return true;
    }

    /**
     * 获取指定网点网点主管列表
     * @param $store_ids
     * @return array
     */
    public function getStoreManagerListByStoreIds($store_ids): array
    {
        $staff_list = BaseStaffInfo::find()
            ->where(['sys_store_id' => $store_ids])
            ->andWhere(['in', 'job_title', $this->store_only_one_job_title])
            ->andWhere(['formal' => [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE]])
            ->andWhere(['state' => BaseStaffInfo::STATE_ON_JOB])
            ->andWhere(['is_sub_staff' => 0])
            ->asArray()
            ->all();

        return $staff_list;
    }

    /**
     * redis 网点变更网点主管更新为网点负责人
     * @param $params
     * @return bool
     */
    public function pushChangeStoreSupervisorManager($params): bool
    {
        return true;
    }

    /**
     * 网点变更网点主管更新为网点负责人
     * @param $params
     * @return mixed
     */
    public function changeStoreSupervisorManager($params)
    {
        try {
            $operator_id   = $params['operator_id'];
            $store_id      = $params['store_id'];
            $manager_id    = $params['staff_info_id'];
            $manager_position_state = $params['manager_position_state'] ?? null;
            $operator_info = StaffInfo::find()->where(['id' => $operator_id])->asArray()->one();

            $store = SysStoreService::getInstance()->getStoreDetail($store_id);
            $store_category = $store['category'] ?? 0;
            if (!in_array($store_category, $this->change_store_manager_store_category)) {
                return false;
            }

            $api_params = [
                'id'                     => $store_id,
                'manager_id'             => $manager_id,
                'manager_position_state' => $manager_position_state,
                'user'                   => [
                    'id'   => $params['operator_id'],
                    'name' => $operator_info['name'] ?? '',
                ],
            ];
            $lang       = $params['lang'] ?? 'th';
            $result     = Yii::$app->jrpc->sync_change_store_supervisor_manager($api_params, $lang);
            if(!$result) {
                if(!isset($params['count'])) {
                    $params['count'] = 1;
                } else {
                    $params['count'] += 1;
                }
                $push_result = Yii::$app->redis->lpush(RedisListKeyEnums::CHANGE_STORE_SUPERVISOR_MANAGER_ALTERNATE, json_encode($params, JSON_UNESCAPED_UNICODE));
                Yii::$app->logger->write_log(['change_store_supervisor_manager_alternate' => $params, 'result' => $push_result], 'info');
            }
            return $result;
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'changeStoreSupervisorManager',
                'message'  => $e->getMessage(),
                'line'     => $e->getLine(),
                'file'     => $e->getFile(),
                'params'   => $params,
            ]);
            return false;
        }
    }

    /**
     * 获取员工信息详情
     * @param $staff_info_id
     */
    public function getStaffInfo($staff_info_id) {
        $staff_info = BaseStaffInfo::find()->select([
            'hr_staff_info.staff_info_id',
            'hr_staff_info.name',
            'hr_staff_info.job_title',
            'hr_staff_info.node_department_id',
            'hr_job_title.job_name as job_title_name',
        ])
            ->leftJoin('hr_job_title', 'hr_job_title.id = hr_staff_info.job_title')
            ->where(['hr_staff_info.staff_info_id' => $staff_info_id])
            ->asArray()
            ->one(Yii::$app->get('r_backyard'));

        return $staff_info;
    }

    /**
     * @description 同步角色变更
     * @param $before_position_category
     * @param $after_position_category
     * @param $staff_info_id
     * @param $operator_id
     * @return void
     */
    public function syncApprovalJurisdictionChanges($before_position_category, $after_position_category, $staff_info_id, $operator_id)
    {
        $positionCategoryNeedToSync = [68];
        foreach ($positionCategoryNeedToSync as $roleId) {
            if(!empty($before_position_category) && in_array($roleId, $before_position_category) && !in_array($roleId, $after_position_category)){
                Yii::$app->jrpc->approvalJurisdictionChanges($staff_info_id, $operator_id);
                break;
            }
        }
    }


    /**
     * redis 同步by来源hold处理状态
     * @param $params
     * @return bool
     */
    public function pushSyncBackyardSourceHoldHandle($params): bool
    {
        try {
            $result = Yii::$app->redis->lpush(RedisListKeyEnums::SYNC_BACKYARD_SOURCE_HOLD_HANDLE, json_encode($params));
            Yii::$app->logger->write_log([
                'function' => 'pushSyncBackyardSourceHoldHandle',
                'params'   => $params,
                'result'   => $result,
            ], 'info');
            return true;
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'pushSyncBackyardSourceHoldHandle',
                'params'   => $params,
                'message'  => $e->getMessage(),
                'line'     => $e->getLine(),
                'file'     => $e->getFile(),
            ]);
            return false;
        }
    }

    /**
     * 同步hold处理状态
     * @param $params
     * @return mixed
     */
    public function syncBackyardSourceHoldHandle($params)
    {
        $staff_info_id = $params['staff_info_id'];
        $language      = $params['language'];
        return Yii::$app->jrpc->syncBackyardSourceHoldHandle(['staff_info_id' => $staff_info_id], $language);
    }

    //停职原因：未回公款、连续旷工，停职恢复在职
    //向恢复在职员工本人的“个人号码”发送手机短信（泰语）
    //向恢复在职员工直线上级、对应HRBP发送BY/KIT-Push通知（根据用户登录语言）
    public function StaffSuspendedReinstatementSendMsg($params): bool
    {
        return true;
    }


    function getEvaluated($formula,$params)
    {
        registerStream();
        extract($params);
        return include 'var://<?php return '. $formula .';';
    }

    /**
     * 添加黑灰名单逻辑
     * @param $staff
     * @param $fbid
     * @param $is_add
     * @return void
     */
    public function addDelinquencyList($staff,$fbid,$is_add,$model)
    {}
    
    public function delDelinquencyList($staff,$fbid,$before)
    {
        
    }



    public function syncSocialSecurityLeaveDate($data)
    {
        $body = [
            'jsonrpc' => '2.0',
            'method'  => 'social_security_leave_date',
            'params'  => [
                ['locale' => 'en'],
                $data,
            ],
            'id'      => time(),
        ];
        try {
            $re = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            return $re;
        } catch (Exception $e) {
            Yii::$app->logger->write_log('syncSocialSecurityLeaveDate 失败，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body,
                    JSON_UNESCAPED_UNICODE));
            throw $e;
        }
    }

    public function syncLeaveManagerCancel($data)
    {
        $body = [
            'jsonrpc' => '2.0',
            'method'  => 'leave_manager_cancel',
            'params'  => [
                ['locale' => 'en'],
                $data,
            ],
            'id'      => time(),
        ];
        try {
            $re = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            return $re;
        } catch (Exception $e) {
            Yii::$app->logger->write_log('syncLeaveManagerCancel 失败，可能出现的原因' . $e->getMessage() . ';行号:' . $e->getLine() . ';body:' . json_encode($body,
                    JSON_UNESCAPED_UNICODE));
            throw $e;
        }
    }

    /**
     * 获取网点下的所有员工 工号
     * @param $storeId
     * @return array
     */
    public function getStaffInfoByStoreId($storeId)
    {
        if(empty($storeId)) {
            return [];
        }
        $staff_list = StaffModel::find()
            ->where(['state' => [BaseStaffInfo::STATE_ON_JOB, BaseStaffInfo::STATE_SUSPENSION]])
            ->andWhere(['formal' => [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE]])
            ->andWhere(['is_sub_staff' => BaseStaffInfo::IS_SUB_STAFF_NO])
            ->andWhere(['sys_store_id' => $storeId])
            ->asArray()
            ->all();
        return array_column($staff_list, 'staff_info_id');
    }

    /**
     * 批量修改员工信息验证 --- 从 actionBatchUpd 拆分出来的逻辑
     * @param $data
     * @param $fbid
     * @return mixed
     */
    public function batchUpd($data,$fbid)
    {
        set_time_limit(0);
        $canAttrs = [
            'immediate_supervisor', 'identity', 'bank_no', 'email', 'personal_email', 'state', 'leave_date', 'stop_duties_date','name','remarks','name_en','mobile'
            ,'car_no','birthday'
            ,'residence_house_num','residence_village_num','residence_village','residence_alley','residence_street','residence_postcodes'
            ,'register_house_num','register_village_num','register_village','register_alley','register_street','register_postcodes'
            ,'relatives_first_name','relatives_last_name','relatives_mobile','dad_first_name','dad_last_name','mum_first_name','mum_last_name','education','graduate_school','major','graduate_time','working_country'
            , 'middle_name', 'first_name', 'last_name', 'suffix_name'
            ,'social_security_num','medical_insurance_num','bank_branch_name','household_registration','ptkp_state','tax_card','fund_num'
            ,'working_day_rest_type','bank_no_name','stop_duty_reason'
        ];
        if (isCountry('MY')){
            $canAttrs = array_values(array_diff($canAttrs,[
                'residence_house_num',
                'residence_village_num',
                'residence_village',
                'residence_alley',
                'residence_street',
                'register_house_num',
                'register_village_num',
                'register_village',
                'register_alley',
                'register_street',
            ]));
            array_push($canAttrs, 'residence_city'); //居住地城市
            array_push($canAttrs, 'residence_detail_address'); //居住地地址
            array_push($canAttrs, 'register_city'); //户口所在地城市
            array_push($canAttrs, 'register_detail_address'); //户口所在地地址
        }
        array_push($canAttrs, 'mobile_company'); //企业号码

        // MY ID VN 银行卡类型
        if(in_array(YII_COUNTRY,['MY','ID','VN'])) {
            array_push($canAttrs, 'bank_name');
            if(YII_COUNTRY == 'MY'){
                $bank_type = [1 => '15', 2 => 54];
            }else  if(YII_COUNTRY == 'ID'){
                $bank_type = [1 => 55, 2 => 78];//1 BCA； 2 OCBC
            }else if(YII_COUNTRY == 'VN'){
                $bank_type = [1 => 20, 2 => 84];//1 HSBC； 2 VietinBank
            }
        }

        if (YII_COUNTRY == "PH") {  // 菲律宾备用银行卡信息 backup_bank_no->备用银行卡号 backup_bank_no_name->持卡人信息 backup_bank_type ->持卡银行
            array_push($canAttrs,'backup_bank_no');
            array_push($canAttrs,'tax_card');   // 税号
        }

        //印尼新增
        if (YII_COUNTRY == "ID") {
            array_push($canAttrs, 'residence_rt'); //居住地邻组
            array_push($canAttrs, 'residence_rw'); //居住地居委会
            array_push($canAttrs, 'register_rt');  //户口所在地邻组
            array_push($canAttrs, 'register_rw');  //户口所在地居委会
        }
        $staff_info_ids = array_column($data, 'staff_info_id');
        $staff_items = StaffItems::find()->where(['staff_info_id' => $staff_info_ids])->all(Yii::$app->get('r_backyard'));
        $staff_items_list = [];
        foreach ($staff_items as $key => $obj) {
            $staff_items_list[$obj->staff_info_id][$obj->item] = $obj->value;
        }

        $staff_list = StaffService::getInstance()->getStaffListByStaffInfoIds($staff_info_ids);
        $department_ids = array_values(array_unique(array_column($staff_list, 'node_department_id')));
        $job_title_ids = array_values(array_unique(array_column($staff_list, 'job_title')));
        $relation_list = (new JobTitleDepartmentRelation())->getRelationWorkingDayRestTypeList(['department_ids' => $department_ids, 'job_title_ids' => $job_title_ids]);

        $relation_key_value = BaseStaffInfo::$batch_update_rest_type_enum;

        $batch_stop_duty_reason = [
            BaseStaffInfo::STOP_DUTY_REASON_1,
            BaseStaffInfo::STOP_DUTY_REASON_2,
            BaseStaffInfo::STOP_DUTY_REASON_3,
            BaseStaffInfo::STOP_DUTY_REASON_4,
            BaseStaffInfo::STOP_DUTY_REASON_5,
            BaseStaffInfo::STOP_DUTY_REASON_8
        ];

        foreach ($data as &$rows) {
            $rows = array_filter($rows);
            $rows = array_map('trim', $rows);
            $staffInfoId = trim($rows['staff_info_id'] ?? '');
            if (empty($staffInfoId)) {
                $rows['result'] = 'miss staff_info_id';
                continue;
            }
            if (empty($staff_list[$staffInfoId])){
                $rows['result'] = Yii::$app->lang->get('batch_update_params_error_7');
                continue;
            }
            $attrs = [];
            foreach (array_intersect($canAttrs, array_keys($rows)) as $attr) {
                if ($attr == 'immediate_supervisor') {
                    $attrs['manger'] = intval(trim($rows['immediate_supervisor']));
                } else if ($attr == 'bank_name') {
                    $attrs['bank_type'] = intval(trim($rows['bank_name']));
                } else {
                    $attrs[$attr] = trim($rows[$attr]);
                }
            }

            if (isset($attrs['birthday']) && !preg_match("/\d{4}\-\d{2}\-\d{2}/", $attrs['birthday'])) {
                $rows['result'] = Yii::$app->lang->get('birthday').' '.Yii::$app->lang->get('error_format');
                continue;
            }

            if (isset($attrs['leave_date']) && !preg_match("/\d{4}\-\d{2}\-\d{2}/", $attrs['leave_date'])) {
                $rows['result'] = Yii::$app->lang->get('leave_date').' '.Yii::$app->lang->get('error_format');
                continue;
            }

            if (isset($attrs['stop_duties_date']) && !preg_match("/\d{4}\-\d{2}\-\d{2}/", $attrs['stop_duties_date'])) {
                $rows['result'] = Yii::$app->lang->get('stop_duties_date').' '.Yii::$app->lang->get('error_format');
                continue;
            }

            if (isset($attrs['graduate_time']) && !preg_match("/\d{4}\-\d{2}\-\d{2}/", $attrs['graduate_time'])) {
                $rows['result'] = Yii::$app->lang->get('graduate_time').' '.Yii::$app->lang->get('error_format');
                continue;
            }


            //在职状态只能更新为停职
            if (isset($attrs['state']) && in_array($attrs['state'], [BaseStaffInfo::STATE_ON_JOB, BaseStaffInfo::STATE_RESIGN])) {
                $rows['result'] = 'not support state operate';
                continue;
            }
            //如果更新字段为停职，当前工号状态为停职/离职 提示当前是非在职状态
            //如果停职日期活着停职原因为空的话 提示缺少停职日期或停职原因
            if (isset($attrs['state']) && $attrs['state'] == BaseStaffInfo::STATE_SUSPENSION) {
                if(in_array($staff_list[$staffInfoId]['state'], [BaseStaffInfo::STATE_RESIGN, BaseStaffInfo::STATE_SUSPENSION])) {
                    $rows['result'] = Yii::$app->lang->get('batch_update_params_error_5');//当前非离职状态
                    continue;
                }

                if(!isset($attrs['stop_duties_date']) || !isset($attrs['stop_duty_reason'])) {
                    $rows['result'] = Yii::$app->lang->get('batch_update_params_error_1');//缺少停职日期或停职原因
                    continue;
                }

                if (isset($attrs['stop_duty_reason']) && !in_array($attrs['stop_duty_reason'], $batch_stop_duty_reason)) {
                    $rows['result'] = Yii::$app->lang->get('batch_update_params_error_6');//停职原因不正确
                    continue;
                }
            }
            //如果编辑字段有停职日期或者停职原因,并且员工当前状态不是停职状态
            if((isset($attrs['stop_duties_date']) || isset($attrs['stop_duty_reason'])) && !isset($attrs['state'])) {
                if($staff_list[$staffInfoId]['state'] != BaseStaffInfo::STATE_SUSPENSION) {
                    $rows['result'] = Yii::$app->lang->get('batch_update_params_error_2');//当前非停职状态
                    continue;
                }
            }

            if(isset($attrs['leave_date'])) {
                if($staff_list[$staffInfoId]['state'] != BaseStaffInfo::STATE_RESIGN) {
                    $rows['result'] = Yii::$app->lang->get('batch_update_params_error_3');//当前非离职状态
                    continue;
                }
                $attrs_leave_date = date('Y-m-d', strtotime($attrs['leave_date']));
                $staff_hire_date = date('Y-m-d', strtotime($staff_list[$staffInfoId]['hire_date']));
                $current_day = gmdate('Y-m-d', time()+TIME_ADD_HOUR*3600);
                if((strtotime($attrs_leave_date) < strtotime($staff_hire_date)) || (strtotime($attrs_leave_date) > strtotime($current_day))) {
                    $rows['result'] = Yii::$app->lang->get('batch_update_params_error_4');//离职日期需在入职日期之后～今天之前
                    continue;
                }
            }

            $rows = array_merge($attrs, ['staff_info_id' => $staffInfoId]);
            if (isset($rows['manger'])) {
                $rows['immediate_supervisor'] = $rows['manger'];
                unset($rows['manger']);
            }
            if(isset($rows['bank_type'])) {
                $rows['bank_name'] = $rows['bank_type'];
                unset($rows['bank_type']);
            }

            if(isset($attrs['manger']) && !empty($attrs['manger'])) {
                //校验直线上级 是否在职
                if($staffInfoId == $attrs['manger']) {
                    $rows['result'] = 'The immediate supervisor cannot be set to himself';
                    continue;
                }
                $manager_info = Staff::view($attrs['manger']);
                if(empty($manager_info)) {
                    $rows['result'] = 'immediate supervisor does not exist';
                    continue;
                }
                if($manager_info['state'] != 1) {
                    $rows['result'] = 'immediate supervisor Not a On the job ID in system';
                    continue;
                }
            }

            if(isset($attrs['bank_type'])) {
                if(!in_array($attrs['bank_type'], array_keys($bank_type))) {
                    $rows['result'] = 'bank type error';
                    continue;
                } else {
                    $attrs['bank_type'] = $bank_type[$attrs['bank_type']] ?? '';
                }
            }
            if(isset($attrs['ptkp_state'])){
                $ptkp_state = Yii::$app->sysconfig->getPTKPState();
                $ptkp_state = array_column($ptkp_state, 'key');
                if(!in_array($attrs['ptkp_state'], $ptkp_state)) {
                    $rows['result'] = 'PTKP state error';
                    continue;
                }
            }

            // 马来新增residence_city residence_detail_address register_city register_detail_address
            if (YII_COUNTRY == "MY") {
                if (isset($rows['residence_city'])) {
                    if (mb_strlen($rows['residence_city']) > 128) {
                        $rows['result'] = 'residence_city ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }
                if (isset($rows['residence_detail_address'])) {
                    if (mb_strlen($rows['residence_detail_address']) > 128) {
                        $rows['result'] = 'residence_detail_address ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }
                if (isset($rows['register_city'])) {
                    if (mb_strlen($rows['register_city']) > 128) {
                        $rows['result'] = 'register_city ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }
                if (isset($rows['register_detail_address'])) {
                    if (mb_strlen($rows['register_detail_address']) > 128) {
                        $rows['result'] = 'register_detail_address ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }
            }

            //印尼新增 residence_rt residence_rw register_rt register_rw 拉翻译
            if (YII_COUNTRY == "ID") {
                //residence 居住地验证
                if (isset($rows['residence_street'])) {
                    if (mb_strlen($rows['residence_street']) > 128) {
                        $rows['result'] = 'residence_street ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }

                if (isset($rows['residence_rt'])) {
                    if (!preg_match('/^\d{3}$/',$rows['residence_rt'])) {
                        $rows['result'] = 'residence_rt ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }

                if (isset($rows['residence_rw'])) {
                    if (!preg_match('/^\d{3}$/',$rows['residence_rw'])) {
                        $rows['result'] = 'residence_rw ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }

                //register_street
                if (isset($rows['register_street'])) {
                    if (mb_strlen($rows['register_street']) > 128) {
                        $rows['result'] = 'register_street ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }

                //register_rw
                if (isset($rows['register_rw'])) {
                    if (!preg_match('/^\d{3}$/',$rows['register_rw'])) {
                        $rows['result'] = 'register_rw ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }

                //register_rt
                if (isset($rows['register_rt'])) {
                    if (!preg_match('/^\d{3}$/',$rows['register_rt'])) {
                        $rows['result'] = 'register_rt ' . Yii::$app->lang->get('export_input_length_error');
                        continue;
                    }
                }
            }

            if(isset($attrs['working_day_rest_type'])) {
                $working_day_rest_type = $attrs['working_day_rest_type'];
                if(!in_array($working_day_rest_type, array_keys($relation_key_value))) {
                    $rows['result'] = Yii::$app->lang->get('working_day_rest_type_error_2');
                    continue;
                }

                $staff_department_id = $staff_list[$staffInfoId]['node_department_id'] ?? 0;
                $staff_job_title_id = $staff_list[$staffInfoId]['job_title'] ?? 0;

                $relation = $relation_list[$staff_department_id . '_' . $staff_job_title_id] ?? [];
                if(empty($relation)) {
                    $rows['result'] = Yii::$app->lang->get('working_day_rest_type_error_1');
                    continue;
                }
                $working_day_rest_type_key = $relation_key_value[$working_day_rest_type];
                if(!in_array($working_day_rest_type_key, $relation)) {
                    $rows['result'] = Yii::$app->lang->get('working_day_rest_type_error_1');
                    continue;
                }

                switch ($working_day_rest_type) {
                    case 'A': //5天班周末休
                        $attrs['week_working_day'] = 5;
                        $attrs['rest_type'] = 2;
                        break;
                    case 'B': //5天班轮休
                        $attrs['week_working_day'] = 5;
                        $attrs['rest_type'] = 1;
                        break;
                    case 'C': //6天班周日休
                        $attrs['week_working_day'] = 6;
                        $attrs['rest_type'] = 2;
                        break;
                    case 'D': //6天班轮休
                        $attrs['week_working_day'] = 6;
                        $attrs['rest_type'] = 1;
                        break;
                    case 'E': //自由轮休
                        $attrs['week_working_day'] = 9;
                        $attrs['rest_type'] = 1;
                        break;
                }
            }

            if(isset($attrs['bank_no_name'])) {
                if (mb_strlen($rows['bank_no_name']) > 100) {
                    $rows['result'] = 'bank_no_name ' . Yii::$app->lang->get('input_length_100_error');
                    continue;
                }
            }

            if (!empty($attrs['bank_no']) && !empty($attrs['bank_type'])){
                if(($res = StaffInfoCheck::validateBankNoStingLen($attrs['bank_no'], $attrs['bank_type'],'bank_no')) && $res !== true) {
                    $rows['result'] = $res[0] .' '. $res[1];
                    continue;
                }
            }elseif (!empty($attrs['bank_no']) && empty($attrs['bank_type'])){
                $_bank_type = $staff_list[$staffInfoId]['bank_type'] ?? 0;
                if(($res = StaffInfoCheck::validateBankNoStingLen($attrs['bank_no'], $_bank_type,'bank_no')) && $res !== true) {
                    $rows['result'] = $res[0] .' '. $res[1];
                    continue;
                }
            }elseif (empty($attrs['bank_no']) && !empty($attrs['bank_type'])){
                $_bank_no = $staff_list[$staffInfoId]['bank_no'] ?? 0;
                if(($res = StaffInfoCheck::validateBankNoStingLen($_bank_no, $attrs['bank_type'],'bank_type')) && $res !== true) {
                    $rows['result'] = $res[0] .' '. $res[1];
                    continue;
                }
            }

            // 菲律宾验证备用银行卡号
            if (YII_COUNTRY == 'PH'){
                if (!empty($attrs['backup_bank_no']) && !empty($attrs['backup_bank_type'])){
                    if(($res = StaffInfoCheck::validateBankNoStingLen($attrs['backup_bank_no'], $attrs['backup_bank_type'],'backup_bank_no')) && $res !== true) {
                        $rows['result'] = $res[0] .' '. $res[1];
                        continue;
                    }
                }elseif (!empty($attrs['backup_bank_no']) && empty($attrs['backup_bank_type'])){
                    $_backup_bank_type = $staff_items_list[$staffInfoId]['BACKUP_BANK_TYPE'] ?? 26;
                    if(($res = StaffInfoCheck::validateBankNoStingLen($attrs['backup_bank_no'], $_backup_bank_type,'backup_bank_no')) && $res !== true) {
                        $rows['result'] = $res[0] .' '. $res[1];
                        continue;
                    }
                }elseif (empty($attrs['backup_bank_no']) && !empty($attrs['backup_bank_type'])){
                    $_backup_bank_no = $staff_items_list[$staffInfoId]['BACKUP_BANK_NO'] ?? 0;
                    if(($res = StaffInfoCheck::validateBankNoStingLen($_backup_bank_no, $attrs['backup_bank_type'],'backup_bank_type')) && $res !== true) {
                        $rows['result'] = $res[0] .' '. $res[1];
                        continue;
                    }
                }
            }

            $res = Staff::updateItems($staffInfoId, $attrs, $fbid);
            if ($res !== true) {
                $rows['result'] = $res;
            } else {
                $rows['result'] = 'ok';
            }
        }

        if (!empty(Staff::$change_leader_list)) {
            //直线上级有变更时， 同步给kpi 直线上级变更
            Yii::$app->jrpc->changeLeader([
                "change_leader" => Staff::$change_leader_list
            ],"actionBatchUpd", Yii::$app->language);
        }
        return $data;
    }
    public function checkCompanyEmail($params)
    {
        $baseStaff = BaseStaffInfo::find()->where(['staff_info_id' => $params['staff_info_id']])->asArray()->one();

        $data['is_display'] = $this->checkIsDisplay($params);
        $email = '';
        //满足展示条件 且 当前工号，企业邮箱不为空，则自动生成。仅 ph
        if(!empty($baseStaff) && $data['is_display'] && empty($baseStaff['email'])){
            $email = $this->getCompanyEmail($baseStaff['first_name'] . '.' . $baseStaff['last_name']);
        }

        $data['email'] = $email;

        return $data;
    }

    public function checkIsDisplay($params)
    {
        if(!empty($params['sys_store_id']) && $params['sys_store_id'] == Enums::HEAD_OFFICE_ID) {
            return true;
        }

        $staff_email_roles  = SettingEnvService::getInstance()->getSetVal('staff_email_roles', ',');
        if(!empty($params['role_id']) && array_intersect($params['role_id'], $staff_email_roles)) {
            return true;
        }

        $email_position  = SettingEnvService::getInstance()->getSetVal('email_position', ',');
        if(in_array($params['job_title'], $email_position)) {
            return true;
        }

        return false;
    }

    public function checkIsRequired($params)
    {
        if(!empty($params['sys_store_id']) && $params['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            return true;
        }

        $staff_email_roles  = SettingEnvService::getInstance()->getSetVal('staff_email_roles', ',');
        if(!empty($params['role_id']) && array_intersect($params['role_id'], $staff_email_roles)) {
            return true;
        }

        return false;
    }

    public function getCompanyEmail($name)
    {
        return '';
    }

    /**
     * 自动生成邮箱
     * @param $params
     * @return bool
     */
    public function autoCreateEmail($params)
    {
        $autoEmail = AutoCreateEmail::find()->where(['staff_info_id' => $params['staff_info_id']])->andWhere(['email' => $params['email']])->one();
        if($autoEmail) {
            return true;
        }

        $autoCreateEmailModel                = new AutoCreateEmail();
        $autoCreateEmailModel->staff_info_id = $params['staff_info_id'];
        $autoCreateEmailModel->date          = $params['date'];
        $autoCreateEmailModel->email         = $params['email'];

        $autoCreateEmailModel->save();

        return true;
    }


}

