<?php

namespace app\controllers;

use app\lang\LangV2;
use app\libs\Enums\Enums;
use app\libs\RedisListKeyEnums;
use app\models\backyard\VehicleInspection;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HrDepartmentJobTitleLevelGrade;
use app\modules\v1\business\CrowdSourcingOutsourcing;
use app\services\base\OtherService;
use app\services\base\ShiftConfigService;
use app\modules\v1\business\StaffInfoCheck;
use app\modules\v1\business\StaffManager;
use app\modules\v1\business\StaffSalary;
use app\modules\v1\business\StaffShift;
use app\modules\v1\business\Staff;
use app\modules\v1\business\OutsourcingOrder;
use app\modules\v1\business\StaffSuspensionTask;
use app\modules\v1\config\SysConfig;
use app\services\base\CrowdSourcingOutsourcing AS CrowdSourcingOutsourcingServices;
use app\services\base\LeaveService;
use app\services\base\RoleService;
use app\services\base\ShiftService;
use app\services\base\StaffFpsService;
use app\services\base\StaffPermissionService;
use app\services\base\ShiftManageConfigService;
use app\services\base\StaffService;
use app\services\base\StaffShiftManageService;
use app\services\base\StaffShiftService;
use app\services\base\SysConfigService;
use JsonRPC;
use Yii;
use function foo\func;

class HrSvcController extends BaseController
{
    public function actionCall() {
        if (function_exists('molten_get_traceid')) {
            header('traceid:' . molten_get_traceid());
        }
    	$server = new JsonRPC\Server();
		$server->getProcedureHandler()
            ->withCallback('doSomething', function ($locale,$id)  {//test
                return $locale['locale']."----------".$id['id'];
            })->withCallback('staff_shift', function ($locale,$params)  {
            	//员工班次查询
            	return StaffShift::view($params['staff_info_id'],$locale);
            })->withCallback('staff_oil_card', function ($locale,$params)  {
            	//油卡押金
            	$staff_info_id = $params['staff_info_id'];//员工号
            	$oil_card_deposit  = $params['oil_card_deposit'];//油卡押金
            	$attrs['oil_card_deposit'] = $oil_card_deposit;
            	return Staff::updateItems($staff_info_id,$attrs,'-1');
            })->withCallback('hr_staff_outsourcing', function ($locale,$params) {
                //同步已审批通过的外协用工申请
                $serial_no = $params['serial_no'];
                $employment_days = $params['employment_days'] ?? 0;
                $final_audit_num = $params['final_audit_num'] ?? 0;
                $result = OutsourcingOrder::order_create($serial_no, $employment_days, $final_audit_num);
                Yii::$app->logger->write_log([
                    'function' => 'HrSvcController-hr_staff_outsourcing',
                    'params' => $params,
                    'result' => $result
                ], 'info');
                return $result;
            })->withCallback('hr_outsourcing_detail', function ($locale,$params) {
                //外协工单详情 by入口下掉 暂时没有调用
                $serial_no = $params['serial_no'];
                $lang = $locale['locale'];
                if($lang == 'zh') {
                    $lang = 'zh-CN';
                }
                return OutsourcingOrder::backyard_view($serial_no,$lang);
            })->withCallback('hr_staff_wait_leave', function ($locale,$params) {

                //员工待离职,工资发放状态 阻止发放，阻止发放类型工资和提成，阻止发放原因 离职手续不全
                $staff_info_id = $params['staff_info_id'];//员工id
                $leave_reason = $params['leave_reason'];//离职原因
                $leave_reason_remark = $params['leave_reason_remark'] ?? '';
                $leave_type = $params['leave_type'] ?? 1;
                $staff_resign_id = $params['staff_resign_id'] ?? 0;//离职申请表id
                $work_handover = $params['work_handover'] ?? 0;//工作交接人
                $lang = $locale['locale'];
                if($lang == 'zh') {
                    $lang = 'zh-CN';
                }
                Yii::$app->lang->setLang($lang ?? 'en');
                /*
                $attr = [
                    'leave_date' => $params['wait_leave_date'],//待离职日期
                    'leave_reason' => $leave_reason,//离职原因
                    'wait_leave_state' => 1,//待离职状态
                    'payment_state' => 2,//工资发放状态 阻止发放
                    'payment_markup' => 10,//工资阻止发放原因 离职手续不全
                    'stop_payment_type' => '1,2' //工资阻止发放类型 工资和提成
                ];
                */
                $attr = [
                    'leave_date' => $params['wait_leave_date'],//待离职日期
                    'leave_reason' => $leave_reason,//离职原因
                    'wait_leave_state' => 1,//待离职状态
                    'leave_type' => $leave_type,//离职类型
                    'leave_reason_remark' => $leave_reason_remark //离职原因备注
                ];
                if (!empty($params['from'])){
                    $attr['from'] = $params['from'];
                }

                if(isset($params['leave_source'])){
                    $attr['leave_source'] = $params['leave_source'];
                    if($attr['leave_source'] == 6) {
                        //来源 批量导入 根据离职原因查找离职类型
                        if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[1])) {
                            $attr['leave_type'] = 1;
                        }
                        if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[2])) {
                            $attr['leave_type'] = 2;
                        }
                        if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[3])) {
                            $attr['leave_type'] = 3;
                        }
                        if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[4])) {
                            $attr['leave_type'] = 4;
                        }
                    }
                }

                $operaterId = "";
                if(isset($params['operaterId'])){
                    $operaterId = $params['operaterId'];
                }
                $project_source = $params['project_source'] ?? '';
                $result = Staff::updateStaffWaitLeave($staff_info_id,$attr,$operaterId, $project_source, $staff_resign_id, $work_handover);
                Yii::$app->logger->write_log('hr_staff_wait_leave 参数:'.json_encode($attr)."---员工id:".$staff_info_id."---结果:".json_encode($result)."---操作人:".$operaterId,'info');
                return $result;
            })->withCallback('hr_staff_personal_email', function ($locale, $params) {

                $staff_info_id = $params['staff_info_id'];//员工id
                $personal_email = $params['personal_email'];// 个人邮箱
                $operaterId = "";
                if(isset($params['operaterId'])){
                    $operaterId = $params['operaterId'];
                }

                if ($staff_info_id && $personal_email) {
                    $result =
                    Staff::updateStaffPersonalEmail($staff_info_id, $personal_email, $operaterId);
                    Yii::$app->logger->write_log('hr_staff_personal_email 参数:'.json_encode($params)."---员工id:".$staff_info_id."---结果:".json_encode($result)."---操作人:".$operaterId,'info');
                    return $result;
                }
                return true;
            })->withCallback('staff_hold', function ($locale, $params) {

                //员工hold 管理
                $staff_info_id = $params['staff_info_id'];//员工id
                $operaterId = $params['operator_id'];//员工id
                $attr = [
                    'type' => $params['type'],//1 hold  2 释放
                    'payment_markup' => $params['payment_markup'],//工资阻止发放原因
                    'stop_payment_type' => $params['stop_payment_type'] //工资阻止发放类型 1工资；2提成
                ];
                //新增 子账号要写日志 还要停职
                if(!empty($params['sub_staff_info_id'])){
                    $attr['sub_staff_info_id'] = $params['sub_staff_info_id'];
                }
                $result = Staff::updateStaffHoldFromBiHold($staff_info_id,$attr, $operaterId);
                Yii::$app->logger->write_log('staff_hold 参数:'.json_encode($params)."------结果:".json_encode($result),'info');

                return $result;
            })->withCallback('staff_info_create_vaildate', function ($locale, $params){
                $lang = $locale['locale'];
                if($lang == 'zh') {
                    $lang = 'zh-CN';
                }
                Yii::$app->lang->setLang($lang);
                $result = StaffInfoCheck::staffCreateValidate($params);
                Yii::$app->logger->write_log('staff_info_create_vaildate 参数:'.json_encode($params)."------结果:".json_encode($result),'info');
                if($result === true) {
                    return $result;
                } else {
                    $msg = [];
                    if(($result[0] ?? false)) {
                        $msg[] = Yii::$app->lang->get($result[0],'',$lang);
                    }
                    if(($result[1] ?? false)) {
                        $msg[] = Yii::$app->lang->get($result[1],'',$lang);
                    }
                    return implode(',',$msg);
                }
            })->withCallback('staff_change_job', function ($locale, $param) {
                //员工转岗 工号/部门/职位/网点/角色/直线上级
                $staff_info_id = $param['staff_info_id'];
                $operater      = $param['operater'];//操作人id
                $attr = [
                    'department_id'         => $param['department_id'],
                    'job_title'             => $param['job_title'],
                    'sys_store_id'          => $param['sys_store_id'],
                    'position_category'     => $param['position_category'],
                    'direct_manager'        => $param['direct_manager'],
                    'vehicle_source'        => $param['vehicle_source'] ?? null,
                    'vehicle_use_date'      => $param['vehicle_use_date'] ?? null,
                    'car_type'              => $param['car_type'] ?? null,
                    'base_salary'           => $param['base_salary'] ?? 0,
                    'exp_allowance'         => $param['exp_allowance'] ?? 0,
                    'position_allowance'    => $param['position_allowance'] ?? 0,
                    'car_rental'            => $param['car_rental'] ?? 0,
                    'trip_payment'          => $param['trip_payment'] ?? 0,
                    'notebook_rental'       => $param['notebook_rental'] ?? 0,
                    'recommended'           => $param['recommended'] ?? 0,
                    'food_allowance'        => $param['food_allowance'] ?? 0,
                    'dangerous_area'        => $param['dangerous_area'] ?? 0,
                    'house_rental'          => $param['house_rental'] ?? 0,
                    'working_day_rest_type' => $param['working_day_rest_type'] ?? 0,
                    'type'                  => $param['type'] ?? 0, //转岗类型 1=一线转岗 2=非一线转岗 3=特殊批量转岗
                    'job_title_grade'       => $param['job_title_grade'] ?? 0,
                    'project_num'           => $param['project_num'] ?? null,
                    'hire_type'             => $param['hire_type'] ?? 0,
                    'hire_times'            => $param['hire_times'] ?? 0,
                    'vehicle_type_category' => $param['vehicle_type_category'] ?? 0,
                ];
                $result = Staff::staffChangeJob($staff_info_id, $attr, $operater);
                Yii::$app->logger->write_log('staff_change_job 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                $msg = [];
                $msg_th = [];
                $msg_en = [];
                if(($result['msg'][0] ?? false)) {
                    $msg[] = Yii::$app->lang->get($result['msg'][0],'','zh-CN');
                    $msg_th[] = Yii::$app->lang->get($result['msg'][0],'','th');
                    $msg_en[] = Yii::$app->lang->get($result['msg'][0],'','en');
                }
                if(($result['msg'][1] ?? false)) {
                    $msg[] = Yii::$app->lang->get($result['msg'][1],'','zh-CN');
                    $msg_th[] = Yii::$app->lang->get($result['msg'][1],'','th');
                    $msg_en[] = Yii::$app->lang->get($result['msg'][1],'','en');

                }
                $result['msg'] = implode(',',$msg);
                $result['msg_en'] = implode(',',$msg_en);
                $result['msg_th'] = implode(',',$msg_th);
                return $result;
            })->withCallback('department_job_title_role',function ($locale,$param) {
                //部门 职位 角色 关联关系
                return Staff::getDepartmentJobTitleRoles($param,$locale['locale']);

            })->withCallback('department_job_title_role_by_store',function ($locale,$param) {
                //部门 职位 角色 关联关系 区分总部网点
                $result = RoleService::getInstance()->getDepartmentJobTitleRolesByStore($param,$locale['locale']);
                Yii::$app->logger->write_log('department_job_title_role_by_store 参数:'.json_encode($param)."------结果:".json_encode($result),'info');

                return ['code' => 1, 'message' => 'success', 'data' => $result];

            })->withCallback('get_leave_reason',function ($locale,$param) {
                //获取离职类型，离职原因关联关系
                $data = LeaveService::getInstance()->getLeaveReasonFromCache($locale['locale']);
                return ['code' => 1, 'message' => 'success', 'data' => $data];

            })->withCallback('fleet_out_sourcing_staff', function ($locale, $param) {
                $lang = $locale['locale'];
                if($lang == 'zh') {
                    $lang = 'zh-CN';
                }
                $result = OutsourcingOrder::fleet_create_staff($param,$lang);
                Yii::$app->logger->write_log('fleet_out_sourcing_staff 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                return $result;
            })->withCallback('update_leave_date',function ($locale,$param) {
                //6656【FBI】离职日期相关功能优化
                $lang = $locale['locale'];
                if($lang == 'zh') {
                    $lang = 'zh-CN';
                }
                $result = Staff::updateStaffLeaveDate($param);
                Yii::$app->logger->write_log('update_leave_date 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                return $result;
            })->withCallback('role_list',function ($locale,$param) {
                //角色列表
                $position = Yii::$app->sysconfig->getPosition();
                $roles = [];
                foreach ($position as $key => $value) {
                    $roles[] = [
                        'role_id' => $key,
                        'role_name_zh' => $this->lang->get('role_' .$key,'','zh-CN'),
                        'role_name_en' => $this->lang->get('role_' .$key,'','en'),
                        'role_name_th' => $this->lang->get('role_' .$key,'','th')
                    ];
                }
                return $roles;
            })->withCallback('update_mobile_company',function ($locale,$param) {
                //更改企业手机号
                $staff_info_id = $param['staff_info_id'] ?? '';
                $mobile_company = $param['mobile_company'] ?? '';//企业手机号

                $attr = ['mobile_company' => $mobile_company];
                $result = Staff::updateItems($staff_info_id, $attr, $staff_info_id);
                Yii::$app->logger->write_log('update_mobile_company 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                if($result === true) {
                    return ['code' => 1,'msg' => 'success'];
                }
                return ['code' => 0,'msg' => $result];
            })->withCallback('update_staff_instructor',function ($locale,$param) {
                //添加员工辅导员

                $staff_info_id = $param['staff_info_id'];
                $instructor_id = $param['instructor_id'];//辅导员id
                $operatorId = $param['operatorId'];//操作人工号

                $attr = ['instructor_id' => $instructor_id];
                $result = Staff::updateItems($staff_info_id, $attr, $operatorId);
                Yii::$app->logger->write_log('add_staff_instructor 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                if($result === true) {
                    return ['code' => 1,'msg' => 'success'];
                }
                return ['code' => 0,'msg' => $result];
            })->withCallback('staff_view', function ($locale, $param) {
                try {
                    Yii::$app->lang->setLang($locale['locale'] ?? 'en');
                    $staffId = $param['staff_info_id'];
                    $staffInfo = Staff::view($staffId);
                    return $this->succ($staffInfo);
                } catch (\Exception $e) {
                    return $this->err(sprintf('Err_Msg: %s, Err_File: %s, Err_Line: %s',
                        $e->getMessage(),
                        $e->getFile(),
                        $e->getLine())
                    );
                }
            })->withCallback('department_job_title_level_grade',function ($locale,$param) {
                //部门职位默认职等职级
                $department_id = $param['department_id'];
                $job_title_id = $param['job_title_id'];

                $level_grade = HrDepartmentJobTitleLevelGrade::find()->where(['department_id' => $department_id])->andWhere(['job_title_id' => $job_title_id])->asArray()->one();
                $result = [];
                Yii::$app->logger->write_log('update_mobile_company 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                if(!$level_grade) {
                    $result = ['job_title_level' => '', 'job_title_grade' => ''];
                } else {
                    $result = ['job_title_level' => $level_grade['job_title_level'], 'job_title_grade' => $level_grade['job_title_grade']];
                }
                return $result;
            })->withCallback('get_staff_config_shift',function ($locale,$param) {
                Yii::$app->logger->write_log('get_staff_config_shift 参数:'.json_encode($param),'info');
                $lang = $locale['locale'] ?? 'en';
                if($lang == 'zh') {
                    $lang = 'zh-CN';
                }
                //根据部门职位网点获取可配置的班次信息
                $department_id = $param['department_id'] ?? '';
                $job_title_id = $param['job_title_id'] ?? '';
                $store_id = $param['store_id'] ?? '';
                if(YII_COUNTRY == 'MY') {
                    $result = ShiftManageConfigService::getInstance()->getShiftConfigList($param, $lang);
                } else {
                    $result = ShiftConfigService::getInstance()->getStaffConfigShift($department_id, $job_title_id, $store_id, $lang);
                }
                Yii::$app->logger->write_log('get_staff_config_shift 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                return $result;
            })->withCallback('staff_change_roles',function ($locale,$param) {
                Yii::$app->logger->write_log('staff_change_roles 参数:'.json_encode($param),'info');
                $lang = $locale['locale'];
                if($lang == 'zh') {
                    $lang = 'zh-CN';
                }
                //角色变更
                $result = Staff::StaffChangRoles($param);
                Yii::$app->logger->write_log('staff_change_roles 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                if($result !== true) {
                    $msg = [];
                    if(($result[0] ?? false)) {
                        $msg[] = Yii::$app->lang->get($result[0],'',$lang);
                    }
                    if(($result[1] ?? false)) {
                        $msg[] = Yii::$app->lang->get($result[1],'',$lang);
                    }
                    return ['code' => 0, 'msg' => implode(',',$msg)];
                } else {
                    return ['code' => 1, 'msg' => 'success'];
                }
            })->withCallback('winhr_by_cv_synchronize',function ($locale,$param) {
                //by 完善信息 同步数据
                Yii::$app->logger->write_log('winhr_by_cv_synchronize 参数:'.json_encode($param),'info');
                $result = Staff::winhr_by_resume_synchronize($param);
                Yii::$app->logger->write_log('winhr_by_cv_synchronize 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                return $result;
            })->withCallback('get_short_url', function ($locale, $param) {
                // 获取短链接
                Yii::$app->logger->write_log('get_short_url 参数:'.json_encode($param, JSON_UNESCAPED_UNICODE),'info');
                $url = OtherService::getShortUrl($param['url']);
                return ['code' => 1, 'msg' => 'success', 'data' => $url];
            })->withCallback('reset_password', function ($locale, $param) {
                $lang = $locale['locale'];
                if($lang == 'zh') {
                    $lang = 'zh-CN';
                }
                Yii::$app->logger->write_log('reset_password 参数:'.json_encode($param, JSON_UNESCAPED_UNICODE),'info');

                try {
                    $result = Staff::resetPassword($param['staff_info_id'], $param['fbid'], $lang);
                    if ($result) {
                        return ['code' => 1, 'msg' => 'success'];
                    }
                    return ['code' => 0, 'msg' => 'error'];
                } catch (\Exception $exception) {
                    Yii::$app->logger->write_log('reset_password 参数:'.json_encode($param, JSON_UNESCAPED_UNICODE) . " result " . json_encode([
                            'E_Msg' => $exception->getMessage(),
                            'E_File' => $exception->getFile(),
                            'E_Line' => $exception->getLine(),
                            'E_Trace' => $exception->getTraceAsString()
                        ], JSON_UNESCAPED_UNICODE) ,'error');
                    return ['code' => 0, 'msg' => $exception->getMessage()];
                }

            })->withCallback('oa_sync_staff_manager_update',function ($locale,$param) {
                //oa 同步员工直线上级变更
                Yii::$app->logger->write_log('oa_sync_staff_manager_update 参数:'.json_encode($param),'info');
                return  (new StaffManager())->oaSyncStaffManagerUpdate($param);
            })->withCallback('sync_staff_state',function ($locale,$param) {
                //同步员工在职状态
                $result = Staff::syncStaffState($param);
                Yii::$app->logger->write_log('sync_staff_state 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                return $result;

                //从 hcm 同步员工离职状态
            })->withCallback('synchronization_resignation', function ($locale, $param) {

				// 从 hcm 同步员工离职状态
				Yii::$app->logger->write_log('synchronization_resignation 参数:'.json_encode($param, JSON_UNESCAPED_UNICODE),'info');
				//默认个人原因
				//自愿离职
				$param['state'] = $param['state'] ?? 2; //只能离职;
                $result = Staff::synchronizationResignation($param);
                if(!isset($result['code']) || $result['code'] != 1){
                    Yii::$app->logger->write_log('synchronization_resignation  从 hcm 同步员工离职状态 参数:'.json_encode($param)."------结果:".json_encode($result));
                }else{
                    Yii::$app->logger->write_log('synchronization_resignation  从 hcm 同步员工离职状态 参数:'.json_encode($param)."------结果:".json_encode($result),'info');

                }

				return ['code' => $result['code'] ?? 0, 'msg' => $result['msg'] ?? ' result empty ', 'data' => []];

			})
            ->withCallback('create_sub_staff_info', function ($locale, $params) {
                //创建子账号
                Yii::$app->logger->write_log(['message' => 'create_sub_staff_info', 'params' => $params],'info');
                $params['is_sub_staff'] = 1;
                $operator_id = -1;
                $staff_info = Staff::combination($params);
                $result = Staff::save($staff_info, $operator_id);
                if($result === true) {
                    if(isCountry('TH')) {
                        $vehicleInfo = VehicleInspection::find()->where(['staff_info_id' => $params['master_staff']])->andWhere(['status' => VehicleInspection::STATUS_1])->asArray()->one();
                        if(!empty($vehicleInfo)) {
                            $dataVehicle['staff_info_id']     = $params['master_staff'];
                            $dataVehicle['sub_staff_info_id'] = $staff_info->staff_info_id;
                            $push_result = Yii::$app->redis->lpush(RedisListKeyEnums::VEHICLE_INSPECTION_TO_SUB_MESSAGE, json_encode($dataVehicle, JSON_UNESCAPED_UNICODE));
                            Yii::$app->logger->write_log(['vehicle_inspection_to_sub_message' => $dataVehicle, 'result' => $push_result], 'info');
                        }
                    }

                    return ['code' => 1, 'msg' => 'success', 'data' => ['staff_info_id' => $staff_info->staff_info_id]];
                }
                $lang = $locale['locale'] ?? 'en';
                if ($lang == 'zh') {
                    $lang = 'zh-CN';
                }
                $msg = '';
                foreach ($result as $item) {
                    $msg .= Yii::$app->lang->get($item, '', $lang);
                }
                Yii::$app->logger->write_log([
                    'message' => 'create_sub_staff_info',
                    'params'  => $params,
                    "result"  => $result,
                    'msg'     => $msg,
                ], 'info');
                return ['code' => 0, 'msg' => $msg, 'data' => []];
            })
            ->withCallback('create_hub_os_staff', function ($locale, $params) {
                //外协账号
                $lang = $locale['locale'];
                Yii::$app->logger->write_log(['message' => 'create_os_staff_info', 'params' => $params],'info');
                $params['formal'] = 0;
                $params['staff_type'] = 4;
                $params['leave_date'] = null;
                $params['outsourcing_type'] = 'company';
                $params['sys_department_id'] = $params['node_department_id'];
                $params['pay_type'] = 'BY_MONTH';
                $params['hire_type'] = BaseStaffInfo::HIRE_OS_TYPE_COMMON;
                $operator_id = -1;
                $staff_info = Staff::combination($params);
                $result = Staff::save($staff_info, $operator_id);
                if($result === true) {
                    return ['code' => 1, 'msg' => 'success', 'data' => ['staff_info_id' => $staff_info->staff_info_id]];
                } else {
                    $msg = [];
                    if(($result[0] ?? false)) {
                        $msg[] = Yii::$app->lang->get($result[0],'',$lang);
                    }
                    if(($result[1] ?? false)) {
                        $msg[] = Yii::$app->lang->get($result[1],'',$lang);
                    }
                    Yii::$app->logger->write_log(['message' => 'create_os_staff_info', 'params' => $params, "result" => $result],'info');
                    return ['code' => 0, 'msg' => implode(',', $msg), 'data' => []];
                }
            })
            ->withCallback('update_hub_os_staff_state', function ($locale, $params) {
                //更新外协账号在职状态
                $lang = $locale['locale'];
                Yii::$app->logger->write_log(['message' => 'update_os_staff_state', 'params' => $params],'info');
                $result = Staff::updateHubOsStaffState($params);
                Yii::$app->logger->write_log(['message' => 'update_os_staff_state', 'params' => $params, "result" => $result],'info');
                if($result == true) {
                    return ['code' => 1, 'msg' => 'success', 'data' => []];
                } else {
                    $msg = [];
                    if(($result[0] ?? false)) {
                        $msg[] = Yii::$app->lang->get($result[0],'',$lang);
                    }
                    if(($result[1] ?? false)) {
                        $msg[] = Yii::$app->lang->get($result[1],'',$lang);
                    }
                    return ['code' => 0, 'msg' => implode(',', $msg), 'data' => []];
                }
			})->withCallback('add_operate_logs',function ($locale,$param) {
                //同步员工操作记录
                $result = Staff::addOperateLogs($param);
                Yii::$app->logger->write_log('add_operate_logs 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                return $result;
            })->withCallback('sync_staff_items',function ($locale,$param) {
                //同步员工操作记录
                $result = Staff::syncStaffItems($param);
                Yii::$app->logger->write_log('sync_staff_items 参数:'.json_encode($param)."------结果:".json_encode($result),'info');
                return $result;
            })->withCallback('update_staff_info',function ($locale,$param) {
                $msg = [];
                try {
                    //更新员工信息
                    $staff_info_id = $param['staff_info_id'];
                    $operater      = $param['operater'] ?? -1;//操作人工号

                    // 可以调整写入的日志的type
                    if (isset($param['operate_type']) && in_array($param['operate_type'], array_keys(Enums::$operateLogTypeMap))) {
                        Staff::$update_items_operate_type = $param['operate_type'];
                    }
                    $result        = Staff::updateItems($staff_info_id, $param, $operater);
                    Yii::$app->logger->write_log('update_staff_info 参数:' . json_encode($param) . "------结果:" . json_encode($result),
                        'info');
                    if ($result === true) {
                        return ['code' => 1, 'msg' => 'success'];
                    }
                } catch (\Exception $e) {
                    $result[] = $e->getMessage();
                }
                foreach ($result as $item) {
                    $msg[] = Yii::$app->lang->get($item,'',$locale['locale']);
                }
                return ['code' => 0,'msg' => $msg];
            })->withCallback('update_staff_status', function ($locale, $param) {
                ini_set('max_execution_time', '60');
                Yii::$app->logger->write_log(json_encode($param, JSON_UNESCAPED_UNICODE), 'info');
                if (empty($param)) {
                    return $this->err(['miss params']);
                }
                $staffInfos = Staff::updateStatus($param);

                return $this->succ($staffInfos);
            })->withCallback('shift-purview-v2', function ($locale, $params) {
                try {
                    Yii::$app->logger->write_log('shift-purview-v2 参数:' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                    $result = StaffShift::shiftPurviewV2($params['staff_info_id']);
                    Yii::$app->logger->write_log('shift-purview-v2 结果:' . json_encode($result, JSON_UNESCAPED_UNICODE), 'info');
                    return $this->succ($result);
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log('shift-purview-v2 error : ' . json_encode(['E_msg' => $e->getMessage(), 'E_file' => $e->getFile(), 'E_line' => $e->getLine()]), 'info');
                    return $this->err();
                }
            })->withCallback('face-data-select-permission', function ($locale, $params) {
                try {
                    Yii::$app->logger->write_log('face-data-select-permission 参数:' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                    $result = StaffPermissionService::getInstance()->faceDataSelect($params['staff_info_id']);
                    Yii::$app->logger->write_log('face-data-select-permission 结果:' . json_encode($result, JSON_UNESCAPED_UNICODE), 'info');
                    return $this->succ($result);
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log('face-data-select-permission error : ' . json_encode(['E_msg' => $e->getMessage(), 'E_file' => $e->getFile(), 'E_line' => $e->getLine()]), 'info');
                    return $this->err();
                }
            })->withCallback('sync-item', function ($locale, $params) {
                try {
                    Yii::$app->logger->write_log('sync-item 参数:' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                    $result = Staff::syncItem($params, $params['fbid']);
                    if (is_integer($result)) {
                        return $this->succ($result);
                    } else {
                        $msg = [];
                        if (is_array($result[1])) {
                            foreach ($result[1] as $item) {
                                $msg[] = Yii::$app->lang->get($item, '', $locale['locale']);
                            }
                        } elseif (is_string($result[0]) && empty($result[1])) {
                            $msg[] = Yii::$app->lang->get($result[0], '', $locale['locale']);
                        } else {
                            $msg[] = $result[1];
                        }
                        return $this->err(implode('', $msg));
                    }
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log('sync-item error : ' . json_encode(['E_msg' => $e->getMessage(), 'E_file' => $e->getFile(), 'E_line' => $e->getLine()]), 'info');
                    return [];
                }
            })->withCallback('staffs-salary', function ($locale, $params) {
                try {
                    Yii::$app->logger->write_log('staffs-salary 参数:' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                    $result = StaffSalary::salary($params['fbid']);
                    return $this->succ($result);
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log('shift-purview-v2 error : ' . json_encode(['E_msg' => $e->getMessage(), 'E_file' => $e->getFile(), 'E_line' => $e->getLine()]), 'info');
                    return [];
                }
            })->withCallback('staffs-create', function ($locale, $params) {
                Yii::$app->lang->setLang($locale['locale']);
                //创建员工
                try {
                    $result = Staff::create($params, $params['fbid']);
                    if ($result[0]) {
                        return $this->succ(["staff_info_id" => $result[1]]);
                    } else {
                        $msg = [];
                        if (is_array($result[1])) {
                            foreach ($result[1] as $item) {
                                $msg[] = Yii::$app->lang->get($item, '', $locale['locale']);
                            }
                        } else {
                            $msg[] = $result[1];
                        }
                        return $this->err(implode('', $msg));
                    }
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log('员工信息保存失败，可能出现的问题：' . json_encode(['E_msg'  => $e->getMessage(),
                                                                                                   'E_file' => $e->getFile(),
                                                                                                   'E_line' => $e->getLine(),
                        ]) . ';行号:' . $e->getLine());
                }
                return $this->err('system error', 0);
            })->withCallback('staffs-manager', function ($locale, $params) {
                Yii::$app->logger->write_log(['svc' => 'staffs-manager', 'params' => $params],'info');
                try {

                    $result = Staff::staffsManager($params);
                    return $this->succ($result);
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log('staffs-manager：' .json_encode(['E_msg' => $e->getMessage(), 'E_file' => $e->getFile(), 'E_line' => $e->getLine()]).';行号:'.$e->getLine());
                }
                return $this->err('system error');
            })
            ->withCallback('ms_staff_stop_not_repaid', function ($locale, $params) {
                //ms 同步未回款员工信息
                $result = (new StaffSuspensionTask())->syncMsStaffSuspensionInfo($params);
                Yii::$app->logger->write_log(['function' => 'ms_staff_stop_not_repaid', 'params' => $params, 'result' => $result],'info');
                return $result;
            })
            ->withCallback('crowd_sourcing_outsourcing_staff_create',function ($locale, $params) {
                //创建众包外协工号
                $lang = $locale['locale'] ?? 'en';
                $result = (new CrowdSourcingOutsourcing)->createStaff($params, $lang);
                Yii::$app->logger->write_log([
                    'svc' => 'crowd_sourcing_outsourcing_staff_create',
                    'params' => $params,
                    'result' => $result
                ],'info');
                return $result;
            })
            ->withCallback('crowd_sourcing_outsourcing_staff_leave',function ($locale, $params) {
                //众包外协工号离职
                $lang = $locale['locale'] ?? 'en';
                $result = (new CrowdSourcingOutsourcing)->leaveStaff($params, $lang);
                Yii::$app->logger->write_log([
                    'svc' => 'crowd_sourcing_outsourcing_staff_leave',
                    'params' => $params,
                    'result' => $result
                ],'info');
                return $result;
            })
            ->withCallback('crowd_sourcing_validation_staff_list',function ($locale, $params) {
                //众包外协验证查询列表
                $lang = $locale['locale'] ?? 'en';
                $result = CrowdSourcingOutsourcingServices::getInstance()->validationStaffList($params, $lang);
                Yii::$app->logger->write_log([
                    'svc' => 'crowd_sourcing_outsourcing_validation_staff_list',
                    'params' => $params,
                    'result' => $result
                ],'info');
                return $result;
            })
            ->withCallback('send_staff_Leave_mq',function ($locale, $params) {
                //hcm 同步 发送it 工单
                $lang = $locale['locale'] ?? 'en';
                $result = staff::sendStaffLeavelMQ($params);
                Yii::$app->logger->write_log([
                    'svc' => 'send_staff_Leave_mq',
                    'params' => $params,
                    'result' => $result
                ],'info');
                if($result) {
                    return ['code' => 1, 'msg' => 'success', 'data' => $params];
                }
                return ['code' => 0, 'msg' => 'fail', 'data' => $params];
            })
            ->withCallback('get_hub_shift',function ($locale, $params) {
                try {
                    $result = ShiftService::getInstance()->getHubShift($params);
                    Yii::$app->logger->write_log(['svc' => 'get_hub_shift', 'params' => $params, 'result' => $result],'info');
                    return ['code' => 1, 'msg' => 'success', 'data' => $result];
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log(['svc' => 'get_hub_shift','params' => $params,'msg'=>$e->getMessage()]);
                    return ['code' => 0, 'msg' => $e->getMessage(), 'data' => []];
                }
            })
            ->withCallback('sync_staff_fps',function ($locale, $params) {
                try {
                    $result = StaffFpsService::getInstance()->sendStaffFpsMQ($params);
                    Yii::$app->logger->write_log(['svc' => 'sync_staff_fps', 'params' => $params, 'result' => $result],'info');
                    return ['code' => 1, 'msg' => 'success', 'data' => $result];
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log(['svc' => 'sync_staff_fps','params' => $params,'msg'=>$e->getMessage()]);
                    return ['code' => 0, 'msg' => $e->getMessage(), 'data' => []];
                }
            })
            ->withCallback('hcm_create_staff_shift', function ($locale, $params) {
                try {
                    $params['language'] = $locale['locale'] ?? 'en';
                    $result = StaffShiftManageService::getInstance()->createStaffShift($params);
                    Yii::$app->logger->write_log(['svc' => 'hcm_create_staff_shift', 'params' => $params, 'result' => $result],'info');
                    return ['code' => 1, 'msg' => 'success', 'data' => $result];
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log(['svc' => 'hcm_create_staff_shift','params' => $params,'msg'=>$e->getMessage()]);
                    return ['code' => 0, 'msg' => $e->getMessage(), 'data' => false];
                }
            })
            ->withCallback('login', function ($locale, $params) {
                try {
                    $params['language'] = $locale['locale'] ?? 'en';
                    $result = StaffService::getInstance()->login($params);
                    Yii::$app->logger->write_log(['svc' => 'login', 'params' => $params, 'result' => $result],'info');
                    return $result;
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log(['svc' => 'login','params' => $params,'msg'=>$e->getMessage()]);
                    return ['code' => 0, 'msg' => $e->getMessage(), 'data' => []];
                }
            })
            ->withCallback('logout', function ($locale, $params) {
                $params['language'] = $locale['locale'] ?? 'en';
                $result = StaffService::getInstance()->logout($params);
                Yii::$app->logger->write_log(['svc' => 'logout', 'params' => $params, 'result' => $result],'info');
                return $result;
            })

            ->withCallback('create_staff_shift_middle_date', function ($locale, $params) {
                try {
                    $params['language'] = $locale['locale'] ?? 'en';
                    $result = StaffShiftService::getInstance()->createStaffShiftMiddleDate($params);
                    Yii::$app->logger->write_log(['svc' => 'create_staff_shift_date', 'params' => $params, 'result' => $result],'info');
                    return $result;
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log(['svc' => 'hcm_create_staff_shift','params' => $params,'msg'=>$e->getMessage()]);
                    return ['code' => 0, 'msg' => $e->getMessage(), 'data' => []];
                }
            })
            ->withCallback('sys_info', function ($locale, $params) {
                try {
                    Yii::$app->lang->setLang($locale['locale'] ?? 'en');
                    $result = SysConfigService::getInstance()->sysInfo($params);
                    Yii::$app->logger->write_log(['svc' => 'sys_info', 'params' => $params, 'result' => $result],'info');
                    return $this->succ($result);
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log(['svc' => 'sys_info','params' => $params,'msg'=>$e->getMessage()]);
                    return $this->err();
                }
            })
            ->withCallback('hcm_sync_staff_manager_update',function ($locale,$param) {
                //hcm OA-组织网点-当前组织变更上级组织，找新的组织负责人为当前组织负责人的上级。
                Yii::$app->logger->write_log('hcm_sync_staff_manager_update 参数:'.json_encode($param),'info');
                return ['code' => 0, 'msg' => 'SUCCESS', 'data' => (new StaffManager())->hcmSyncStaffManagerUpdate($param)];
            })
            ->withCallback('my_add_staff_shift',function ($locale,$params) {
                Yii::$app->logger->write_log('my_add_staff_shift 参数:'.json_encode($params),'info');
                $result   = StaffShiftManageService::getInstance()->editStaffWeeklyShift($params);
                return ['code' => 0, 'msg' => 'SUCCESS', 'data' => $result];
            })
            ->withCallback('has_grade_permission_tab', function ($locale, $params) {
                try {
                    $result = Staff::hasGradePermissionTab($params['staff_info_id'], $params['fbid']);
                    Yii::$app->logger->write_log(['svc' => 'has_grade_permission_tab', 'params' => $params, 'result' => $result],'info');
                    return $this->succ($result);
                } catch (\Exception $e) {
                    Yii::$app->logger->write_log(['svc' => 'has_grade_permission_tab','params' => $params,'msg'=>$e->getMessage()]);
                    return $this->err();
                }
            })
        ;//这个分号不要动！！！
     	echo $server->execute();
     	die;
    }
}
