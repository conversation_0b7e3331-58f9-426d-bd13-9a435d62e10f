<?php
namespace app\libs;


/**
 * redis key 数据
 * Class enums
 */
final class RedisListKeyEnums
{
    //班次设置范围 redis list  key
    const LIST_SHIFT_CONFIG_CREATE_REDIS_KEY = 'hris_shift_config_create_list';
    //员工恢复在职
    const CHANGE_STAFF_STATE_ON_JOB_REDIS_KEY = 'change_staff_state_on_job';
    // 社保离职日期
    const SOCIAL_SECURITY_LEAVE_DATE = 'social_security_leave_date';
    // 撤销离职逻辑
    const LEAVE_MANAGER_CANCEL = 'leave_manager_cancel';
    //中国员工入职离职提醒
    const CHINA_STAFF_STATE_CHANGE_NOTICE = 'china_staff_state_change_notice';
    //入职回调
    const WINHR_CREATE_STAFF_LIST = 'winhr_create_staff';
    /**
     * 入职创建试用期 月薪制员工
     */
    const CREATE_HR_PROBATION = 'create_hr_probation';

    //入职 创建员工扩展表数据
    const CREATE_HR_STAFF_EXTEND = 'create_staff_extend_info';

    //新入职员工创建班次
    const NEW_STAFF_CREATE_SHIFT = 'new_staff_create_shift';
    //转岗变更班次
    const JOB_TRANSFER_UPDATE_SHIFT = 'job_transfer_update_shift';
    //可选班次维护 配置后更新员工默认班次
    const CONFIG_SHIFT_UPDATE_STAFF_SHIFT = 'config_shift_update_staff_shift';
    /**
     * 创建轮休员工默认设置的休息日
     */
    const NEW_STAFF_CREATE__DEFAULT_REST_DAY = 'new_staff_create_default_rest_day';

    //网点变更 更新支援信息
    const CHANGE_STORE_UPDATE_SUPPORT = 'change_store_update_support';

    //网点变动网点主管同步为网点负责人
    const CHANGE_STORE_SUPERVISOR_MANAGER = 'change_store_supervisor_manager';
    //网点变动网点主管同步为网点负责人候补
    const CHANGE_STORE_SUPERVISOR_MANAGER_ALTERNATE = 'change_store_supervisor_manager_alternate';

    //同步新离职资产 员工在职/离职信息
    const SYNC_RESIGN_ASSETS_STAFF = 'sync_resign_assets_staff';

    //同步by来源hold处理状态
    const SYNC_BACKYARD_SOURCE_HOLD_HANDLE = 'sync_backyard_source_hold_handle';

    //新入职工号发送填写保险受益人信息
    const SEND_MESSAGE_INSURANCE_BENEFICIARY_EDIT = 'send_message_insurance_beneficiary_edit';

    //停职恢复在职
    const SUSPENDED_REINSTATEMENT = 'suspended_reinstatement';

    //组织架构负责人变更，更新上级
    const OA_SYNC_STAFF_MANAGER_UPDATE = 'oa_sync_staff_manager_update';

    //hcm OA组织网点 组织变更 将新的组织负责人，更新为当前组织负责人的直属上级
    const HCM_SYNC_STAFF_MANAGER_UPDATE = 'hcm_sync_staff_manager_update';

    //快递员车辆稽查信息：创建子账号，给子账号发消息
    const VEHICLE_INSPECTION_TO_SUB_MESSAGE = 'vehicle_inspection_to_sub_message';


}
